{"version": 3, "file": "react-data-table-component.dev.js", "sources": ["../src/DataTable/types.ts", "../src/DataTable/util.ts", "../src/DataTable/tableReducer.ts", "../src/DataTable/Table.tsx", "../src/DataTable/TableHead.tsx", "../src/DataTable/TableHeadRow.tsx", "../src/DataTable/media.ts", "../src/DataTable/Cell.ts", "../src/DataTable/TableCell.tsx", "../src/DataTable/Checkbox.tsx", "../src/DataTable/TableCellCheckbox.tsx", "../src/DataTable/ExpanderButton.tsx", "../src/DataTable/TableCellExpander.tsx", "../src/DataTable/ExpanderRow.tsx", "../src/DataTable/constants.ts", "../src/DataTable/TableRow.tsx", "../src/icons/NativeSortIcon.tsx", "../src/DataTable/TableCol.tsx", "../src/DataTable/TableColCheckbox.tsx", "../src/hooks/useRTL.ts", "../src/DataTable/ContextMenu.tsx", "../src/DataTable/TableHeader.tsx", "../src/DataTable/TableSubheader.tsx", "../src/DataTable/TableBody.tsx", "../src/DataTable/ResponsiveWrapper.tsx", "../src/DataTable/ProgressWrapper.tsx", "../src/DataTable/TableWrapper.tsx", "../src/DataTable/TableColExpander.tsx", "../src/DataTable/NoDataWrapper.tsx", "../src/icons/Dropdown.tsx", "../src/DataTable/Select.tsx", "../src/hooks/useWindowSize.ts", "../src/icons/FirstPage.tsx", "../src/icons/LastPage.tsx", "../src/icons/Left.tsx", "../src/icons/Right.tsx", "../src/icons/ExpanderCollapsedIcon.tsx", "../src/icons/ExpanderExpandedIcon.tsx", "../src/DataTable/defaultProps.tsx", "../src/DataTable/Pagination.tsx", "../src/hooks/useDidUpdateEffect.ts", "../node_modules/deepmerge/dist/cjs.js", "../src/DataTable/themes.ts", "../src/DataTable/styles.ts", "../src/hooks/useColumns.ts", "../src/DataTable/DataTable.tsx"], "sourcesContent": ["import { Alignment, Direction, Media } from './constants';\nimport { CSSObject } from 'styled-components';\n\nexport enum SortOrder {\n\tASC = 'asc',\n\tDESC = 'desc',\n}\n\nexport type Primitive = string | number | boolean;\nexport type ColumnSortFunction<T> = (a: T, b: T) => number;\nexport type ExpandRowToggled<T> = (expanded: boolean, row: T) => void;\nexport type Format<T> = (row: T, rowIndex: number) => React.ReactNode;\nexport type RowState<T> = ((row: T) => boolean) | null;\nexport type Selector<T> = (row: T, rowIndex?: number) => Primitive;\nexport type SortFunction<T> = (rows: T[], field: Selector<T>, sortDirection: SortOrder) => T[];\nexport type TableRow = Record<string, unknown>;\nexport type ComponentProps = Record<string, unknown>;\nexport type ExpanderComponentProps<T> = { data: T };\nexport type ExpandableRowsComponent<T> = React.ComponentType<ExpanderComponentProps<T>>;\nexport type PaginationChangePage = (page: number, totalRows: number) => void;\nexport type PaginationChangeRowsPerPage = (currentRowsPerPage: number, currentPage: number) => void;\nexport type PaginationComponentProps = {\n\trowsPerPage: number;\n\trowCount: number;\n\tcurrentPage: number;\n\tonChangePage: PaginationChangePage;\n\tonChangeRowsPerPage: PaginationChangeRowsPerPage;\n};\nexport type PaginationComponent = React.ComponentType<PaginationComponentProps>;\n\nexport type TableProps<T> = {\n\tactions?: React.ReactNode | React.ReactNode[];\n\tariaLabel?: string;\n\tclassName?: string;\n\tclearSelectedRows?: boolean;\n\tcolumns: TableColumn<T>[];\n\tconditionalRowStyles?: ConditionalStyles<T>[];\n\tcontextActions?: React.ReactNode | React.ReactNode[];\n\tcontextComponent?: React.ReactNode;\n\tcontextMessage?: ContextMessage;\n\tcustomStyles?: TableStyles;\n\tdata: T[];\n\tdefaultSortAsc?: boolean;\n\tdefaultSortFieldId?: string | number | null | undefined;\n\tdense?: boolean;\n\tdirection?: Direction;\n\tdisabled?: boolean;\n\texpandableIcon?: ExpandableIcon;\n\texpandableInheritConditionalStyles?: boolean;\n\texpandableRowDisabled?: RowState<T>;\n\texpandableRowExpanded?: RowState<T>;\n\texpandableRows?: boolean;\n\texpandableRowsComponent?: ExpandableRowsComponent<T>;\n\texpandableRowsComponentProps?: ComponentProps;\n\texpandableRowsHideExpander?: boolean;\n\texpandOnRowClicked?: boolean;\n\texpandOnRowDoubleClicked?: boolean;\n\tfixedHeader?: boolean;\n\tfixedHeaderScrollHeight?: string;\n\thighlightOnHover?: boolean;\n\tkeyField?: string;\n\tnoContextMenu?: boolean;\n\tnoDataComponent?: React.ReactNode;\n\tnoHeader?: boolean;\n\tnoTableHead?: boolean;\n\tonChangePage?: PaginationChangePage;\n\tonChangeRowsPerPage?: PaginationChangeRowsPerPage;\n\tonRowClicked?: (row: T, e: React.MouseEvent) => void;\n\tonRowDoubleClicked?: (row: T, e: React.MouseEvent) => void;\n\tonRowMouseEnter?: (row: T, e: React.MouseEvent) => void;\n\tonRowMouseLeave?: (row: T, e: React.MouseEvent) => void;\n\tonRowExpandToggled?: ExpandRowToggled<T>;\n\tonSelectedRowsChange?: (selected: { allSelected: boolean; selectedCount: number; selectedRows: T[] }) => void;\n\tonSort?: (selectedColumn: TableColumn<T>, sortDirection: SortOrder, sortedRows: T[]) => void;\n\tonColumnOrderChange?: (nextOrder: TableColumn<T>[]) => void;\n\tpagination?: boolean;\n\tpaginationComponent?: PaginationComponent;\n\tpaginationComponentOptions?: PaginationOptions;\n\tpaginationDefaultPage?: number;\n\tpaginationIconFirstPage?: React.ReactNode;\n\tpaginationIconLastPage?: React.ReactNode;\n\tpaginationIconNext?: React.ReactNode;\n\tpaginationIconPrevious?: React.ReactNode;\n\tpaginationPerPage?: number;\n\tpaginationResetDefaultPage?: boolean;\n\tpaginationRowsPerPageOptions?: number[];\n\tpaginationServer?: boolean;\n\tpaginationServerOptions?: PaginationServerOptions;\n\tpaginationTotalRows?: number;\n\tpersistTableHead?: boolean;\n\tpointerOnHover?: boolean;\n\tprogressComponent?: React.ReactNode;\n\tprogressPending?: boolean;\n\tresponsive?: boolean;\n\tselectableRowDisabled?: RowState<T>;\n\tselectableRows?: boolean;\n\tselectableRowsComponent?: 'input' | React.ReactNode;\n\tselectableRowsComponentProps?: ComponentProps;\n\tselectableRowSelected?: RowState<T>;\n\tselectableRowsHighlight?: boolean;\n\tselectableRowsNoSelectAll?: boolean;\n\tselectableRowsVisibleOnly?: boolean;\n\tselectableRowsSingle?: boolean;\n\tsortFunction?: SortFunction<T> | null;\n\tsortIcon?: React.ReactNode;\n\tsortServer?: boolean;\n\tstriped?: boolean;\n\tstyle?: CSSObject;\n\tsubHeader?: React.ReactNode | React.ReactNode[];\n\tsubHeaderAlign?: Alignment;\n\tsubHeaderComponent?: React.ReactNode | React.ReactNode[];\n\tsubHeaderWrap?: boolean;\n\ttheme?: Themes;\n\t/**\n\t *  Shows and displays a header with a title\n\t *  */\n\ttitle?: string | React.ReactNode;\n};\n\nexport type TableColumnBase = {\n\tallowOverflow?: boolean;\n\tbutton?: boolean;\n\tcenter?: boolean;\n\tcompact?: boolean;\n\treorder?: boolean;\n\tgrow?: number;\n\thide?: number | ((value: number) => CSSObject) | Media;\n\tid?: string | number;\n\tignoreRowClick?: boolean;\n\tmaxWidth?: string;\n\tminWidth?: string;\n\tname?: string | number | React.ReactNode;\n\tomit?: boolean;\n\tright?: boolean;\n\tsortable?: boolean;\n\tstyle?: CSSObject;\n\twidth?: string;\n\twrap?: boolean;\n};\n\nexport interface TableColumn<T> extends TableColumnBase {\n\tname?: string | number | React.ReactNode;\n\tsortField?: string;\n\tcell?: (row: T, rowIndex: number, column: TableColumn<T>, id: string | number) => React.ReactNode;\n\tconditionalCellStyles?: ConditionalStyles<T>[];\n\tformat?: Format<T> | undefined;\n\tselector?: Selector<T>;\n\tsortFunction?: ColumnSortFunction<T>;\n}\n\nexport interface ConditionalStyles<T> {\n\twhen: (row: T) => boolean;\n\tstyle?: CSSObject | ((row: T) => CSSObject);\n\tclassNames?: string[];\n}\n\nexport interface TableStyles {\n\ttable?: {\n\t\tstyle: CSSObject;\n\t};\n\ttableWrapper?: {\n\t\tstyle: CSSObject;\n\t};\n\tresponsiveWrapper?: {\n\t\tstyle: CSSObject;\n\t};\n\theader?: {\n\t\tstyle: CSSObject;\n\t};\n\tsubHeader?: {\n\t\tstyle: CSSObject;\n\t};\n\thead?: {\n\t\tstyle: CSSObject;\n\t};\n\theadRow?: {\n\t\tstyle?: CSSObject;\n\t\tdenseStyle?: CSSObject;\n\t};\n\theadCells?: {\n\t\tstyle?: CSSObject;\n\t\tdraggingStyle?: CSSObject;\n\t};\n\tcontextMenu?: {\n\t\tstyle?: CSSObject;\n\t\tactiveStyle?: CSSObject;\n\t};\n\tcells?: {\n\t\tstyle: CSSObject;\n\t\tdraggingStyle?: CSSObject;\n\t};\n\trows?: {\n\t\tstyle?: CSSObject;\n\t\tselectedHighlightStyle?: CSSObject;\n\t\tdenseStyle?: CSSObject;\n\t\thighlightOnHoverStyle?: CSSObject;\n\t\tstripedStyle?: CSSObject;\n\t};\n\texpanderRow?: {\n\t\tstyle: CSSObject;\n\t};\n\texpanderCell?: {\n\t\tstyle: CSSObject;\n\t};\n\texpanderButton?: {\n\t\tstyle: CSSObject;\n\t};\n\tpagination?: {\n\t\tstyle?: CSSObject;\n\t\tpageButtonsStyle?: CSSObject;\n\t};\n\tnoData?: {\n\t\tstyle: CSSObject;\n\t};\n\tprogress?: {\n\t\tstyle: CSSObject;\n\t};\n}\n\nexport interface PaginationOptions {\n\tnoRowsPerPage?: boolean;\n\trowsPerPageText?: string;\n\trangeSeparatorText?: string;\n\tselectAllRowsItem?: boolean;\n\tselectAllRowsItemText?: string;\n}\n\nexport interface PaginationServerOptions {\n\tpersistSelectedOnSort?: boolean;\n\tpersistSelectedOnPageChange?: boolean;\n}\n\nexport interface ExpandableIcon {\n\tcollapsed: React.ReactNode;\n\texpanded: React.ReactNode;\n}\n\nexport interface ContextMessage {\n\tsingular: string;\n\tplural: string;\n\tmessage?: string;\n}\n\nexport type TableState<T> = {\n\tallSelected: boolean;\n\tcontextMessage: ContextMessage;\n\tselectedCount: number;\n\tselectedRows: T[];\n\tselectedColumn: TableColumn<T>;\n\tsortDirection: SortOrder;\n\tcurrentPage: number;\n\trowsPerPage: number;\n\tselectedRowsFlag: boolean;\n\t/* server-side pagination and server-side sorting will cause selectedRows to change\n\t because of this behavior onSelectedRowsChange useEffect is triggered (by design it should notify if there was a change)\n\t however, when using selectableRowsSingle\n\t*/\n\ttoggleOnSelectedRowsChange: boolean;\n};\n\n// Theming\ntype ThemeText = {\n\tprimary: string;\n\tsecondary: string;\n\tdisabled: string;\n};\n\ntype ThemeBackground = {\n\tdefault: string;\n};\n\ntype ThemeContext = {\n\tbackground: string;\n\ttext: string;\n};\n\ntype ThemeDivider = {\n\tdefault: string;\n};\n\ntype ThemeButton = {\n\tdefault: string;\n\tfocus: string;\n\thover: string;\n\tdisabled: string;\n};\n\ntype ThemeSelected = {\n\tdefault: string;\n\ttext: string;\n};\n\ntype ThemeHighlightOnHover = {\n\tdefault: string;\n\ttext: string;\n};\n\ntype ThemeStriped = {\n\tdefault: string;\n\ttext: string;\n};\n\nexport type Themes = string;\n\nexport interface Theme {\n\ttext: ThemeText;\n\tbackground: ThemeBackground;\n\tcontext: ThemeContext;\n\tdivider: ThemeDivider;\n\tbutton: ThemeButton;\n\tselected: ThemeSelected;\n\thighlightOnHover: ThemeHighlightOnHover;\n\tstriped: ThemeStriped;\n}\n\n// Reducer Actions\nexport interface AllRowsAction<T> {\n\ttype: 'SELECT_ALL_ROWS';\n\tkeyField: string;\n\trows: T[];\n\trowCount: number;\n\tmergeSelections: boolean;\n}\n\nexport interface SingleRowAction<T> {\n\ttype: 'SELECT_SINGLE_ROW';\n\tkeyField: string;\n\trow: T;\n\tisSelected: boolean;\n\trowCount: number;\n\tsingleSelect: boolean;\n}\n\nexport interface MultiRowAction<T> {\n\ttype: 'SELECT_MULTIPLE_ROWS';\n\tkeyField: string;\n\tselectedRows: T[];\n\ttotalRows: number;\n\tmergeSelections: boolean;\n}\n\nexport interface SortAction<T> {\n\ttype: 'SORT_CHANGE';\n\tsortDirection: SortOrder;\n\tselectedColumn: TableColumn<T>;\n\tclearSelectedOnSort: boolean;\n}\n\nexport interface PaginationPageAction {\n\ttype: 'CHANGE_PAGE';\n\tpage: number;\n\tpaginationServer: boolean;\n\tvisibleOnly: boolean;\n\tpersistSelectedOnPageChange: boolean;\n}\n\nexport interface PaginationRowsPerPageAction {\n\ttype: 'CHANGE_ROWS_PER_PAGE';\n\trowsPerPage: number;\n\tpage: number;\n}\n\nexport interface ClearSelectedRowsAction {\n\ttype: 'CLEAR_SELECTED_ROWS';\n\tselectedRowsFlag: boolean;\n}\n\nexport interface ColumnsAction<T> {\n\ttype: 'UPDATE_COLUMNS';\n\tcols: TableColumn<T>[];\n}\n\nexport type Action<T> =\n\t| AllRowsAction<T>\n\t| SingleRowAction<T>\n\t| MultiRowAction<T>\n\t| SortAction<T>\n\t| PaginationPageAction\n\t| PaginationRowsPerPageAction\n\t| ClearSelectedRowsAction;\n", "import { CSSObject } from 'styled-components';\nimport { ConditionalStyles, TableColumn, Format, TableRow, Selector, SortOrder, SortFunction } from './types';\n\nexport function prop<T, K extends keyof T>(obj: T, key: K): T[K] {\n\treturn obj[key];\n}\n\nexport function isEmpty(field: string | number | undefined = ''): boolean {\n\tif (typeof field === 'number') {\n\t\treturn false;\n\t}\n\n\treturn !field || field.length === 0;\n}\n\nexport function sort<T>(\n\trows: T[],\n\tselector: Selector<T> | null | undefined,\n\tdirection: SortOrder,\n\tsortFn?: SortFunction<T> | null,\n): T[] {\n\tif (!selector) {\n\t\treturn rows;\n\t}\n\n\tif (sortFn && typeof sortFn === 'function') {\n\t\t// we must create a new rows reference\n\t\treturn sortFn(rows.slice(0), selector, direction);\n\t}\n\n\treturn rows.slice(0).sort((a: T, b: T) => {\n\t\tconst aValue = selector(a);\n\t\tconst bValue = selector(b);\n\n\t\tif (direction === 'asc') {\n\t\t\tif (aValue < bValue) {\n\t\t\t\treturn -1;\n\t\t\t}\n\n\t\t\tif (aValue > bValue) {\n\t\t\t\treturn 1;\n\t\t\t}\n\t\t}\n\n\t\tif (direction === 'desc') {\n\t\t\tif (aValue > bValue) {\n\t\t\t\treturn -1;\n\t\t\t}\n\n\t\t\tif (aValue < bValue) {\n\t\t\t\treturn 1;\n\t\t\t}\n\t\t}\n\n\t\treturn 0;\n\t});\n}\n\nexport function getProperty<T>(\n\trow: T,\n\t// TODO: remove string type in V8\n\tselector: Selector<T> | undefined | null,\n\tformat: Format<T> | undefined | null,\n\trowIndex: number,\n): React.ReactNode {\n\tif (!selector) {\n\t\treturn null;\n\t}\n\n\t// format will override how the selector is displayed but the original dataset is used for sorting\n\tif (format && typeof format === 'function') {\n\t\treturn format(row, rowIndex);\n\t}\n\n\treturn selector(row, rowIndex);\n}\n\nexport function insertItem<T>(array: T[] = [], item: T, index = 0): T[] {\n\treturn [...array.slice(0, index), item, ...array.slice(index)];\n}\n\nexport function removeItem<T>(array: T[] = [], item: T, keyField = 'id'): T[] {\n\tconst newArray = array.slice();\n\tconst outerField = prop(item as TableRow, keyField);\n\n\tif (outerField) {\n\t\tnewArray.splice(\n\t\t\tnewArray.findIndex((a: T) => {\n\t\t\t\tconst innerField = prop(a as TableRow, keyField);\n\n\t\t\t\treturn innerField === outerField;\n\t\t\t}),\n\t\t\t1,\n\t\t);\n\t} else {\n\t\tnewArray.splice(\n\t\t\tnewArray.findIndex(a => a === item),\n\t\t\t1,\n\t\t);\n\t}\n\n\treturn newArray;\n}\n\n// Make sure columns have unique id's\nexport function decorateColumns<T>(columns: TableColumn<T>[]): TableColumn<T>[] {\n\treturn columns.map((column, index) => {\n\t\tconst decoratedColumn: TableColumn<T> = {\n\t\t\t...column,\n\t\t\tsortable: column.sortable || !!column.sortFunction || undefined,\n\t\t};\n\n\t\tif (!column.id) {\n\t\t\tdecoratedColumn.id = index + 1;\n\n\t\t\treturn decoratedColumn;\n\t\t}\n\n\t\treturn decoratedColumn;\n\t});\n}\n\nexport function getSortDirection(ascDirection: boolean | undefined = false): SortOrder {\n\treturn ascDirection ? SortOrder.ASC : SortOrder.DESC;\n}\n\nexport function handleFunctionProps(\n\tobject: { [key: string]: unknown },\n\t...args: unknown[]\n): { [key: string]: unknown } {\n\tlet newObject;\n\n\tObject.keys(object)\n\t\t.map(o => object[o])\n\t\t.forEach((value, index) => {\n\t\t\tconst oldObject = object;\n\n\t\t\tif (typeof value === 'function') {\n\t\t\t\tnewObject = { ...oldObject, [Object.keys(object)[index]]: value(...args) };\n\t\t\t\t// delete oldObject[value];\n\t\t\t}\n\t\t});\n\n\treturn newObject || object;\n}\n\nexport function getNumberOfPages(rowCount: number, rowsPerPage: number): number {\n\treturn Math.ceil(rowCount / rowsPerPage);\n}\n\nexport function recalculatePage(prevPage: number, nextPage: number): number {\n\treturn Math.min(prevPage, nextPage);\n}\n\nexport const noop = (): null => null;\n\nexport function getConditionalStyle<T>(\n\trow: T,\n\tconditionalRowStyles: ConditionalStyles<T>[] = [],\n\tbaseClassNames: string[] = [],\n): { conditionalStyle: CSSObject; classNames: string } {\n\tlet rowStyle = {};\n\tlet classNames: string[] = [...baseClassNames];\n\n\tif (conditionalRowStyles.length) {\n\t\tconditionalRowStyles.forEach(crs => {\n\t\t\tif (!crs.when || typeof crs.when !== 'function') {\n\t\t\t\tthrow new Error('\"when\" must be defined in the conditional style object and must be function');\n\t\t\t}\n\n\t\t\t// evaluate the field and if true return a the style to be applied\n\t\t\tif (crs.when(row)) {\n\t\t\t\trowStyle = crs.style || {};\n\n\t\t\t\tif (crs.classNames) {\n\t\t\t\t\tclassNames = [...classNames, ...crs.classNames];\n\t\t\t\t}\n\n\t\t\t\tif (typeof crs.style === 'function') {\n\t\t\t\t\trowStyle = crs.style(row) || {};\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n\n\treturn { conditionalStyle: rowStyle, classNames: classNames.join(' ') };\n}\n\nexport function isRowSelected<T>(row: T, selectedRows: T[] = [], keyField = 'id'): boolean {\n\t// cast row as TableRow because the property is unknown in advance therefore, typescript will throw an error\n\tconst outerField = prop(row as TableRow, keyField);\n\n\tif (outerField) {\n\t\treturn selectedRows.some(r => {\n\t\t\tconst innerField = prop(r as TableRow, keyField);\n\n\t\t\treturn innerField === outerField;\n\t\t});\n\t}\n\n\treturn selectedRows.some(r => r === row);\n}\n\nexport function isOdd(num: number): boolean {\n\treturn num % 2 === 0;\n}\n\nexport function findColumnIndexById<T>(columns: TableColumn<T>[], id: string | undefined): number {\n\tif (!id) {\n\t\treturn -1;\n\t}\n\n\treturn columns.findIndex(c => {\n\t\treturn equalizeId(c.id, id);\n\t});\n}\n\nexport function equalizeId(a: string | number | undefined, b: string | number | undefined): boolean {\n\treturn a == b;\n}\n", "import { insertItem, isRowSelected, removeItem } from './util';\nimport { Action, TableState } from './types';\n\nexport function tableReducer<T>(state: TableState<T>, action: Action<T>): TableState<T> {\n\tconst toggleOnSelectedRowsChange = !state.toggleOnSelectedRowsChange;\n\n\tswitch (action.type) {\n\t\tcase 'SELECT_ALL_ROWS': {\n\t\t\tconst { keyField, rows, rowCount, mergeSelections } = action;\n\t\t\tconst allChecked = !state.allSelected;\n\t\t\tconst toggleOnSelectedRowsChange = !state.toggleOnSelectedRowsChange;\n\n\t\t\tif (mergeSelections) {\n\t\t\t\tconst selections = allChecked\n\t\t\t\t\t? [...state.selectedRows, ...rows.filter(row => !isRowSelected(row, state.selectedRows, keyField))]\n\t\t\t\t\t: state.selectedRows.filter(row => !isRowSelected(row, rows, keyField));\n\n\t\t\t\treturn {\n\t\t\t\t\t...state,\n\t\t\t\t\tallSelected: allChecked,\n\t\t\t\t\tselectedCount: selections.length,\n\t\t\t\t\tselectedRows: selections,\n\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tallSelected: allChecked,\n\t\t\t\tselectedCount: allChecked ? rowCount : 0,\n\t\t\t\tselectedRows: allChecked ? rows : [],\n\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t};\n\t\t}\n\n\t\tcase 'SELECT_SINGLE_ROW': {\n\t\t\tconst { keyField, row, isSelected, rowCount, singleSelect } = action;\n\n\t\t\t// handle single select mode\n\t\t\tif (singleSelect) {\n\t\t\t\tif (isSelected) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...state,\n\t\t\t\t\t\tselectedCount: 0,\n\t\t\t\t\t\tallSelected: false,\n\t\t\t\t\t\tselectedRows: [],\n\t\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\treturn {\n\t\t\t\t\t...state,\n\t\t\t\t\tselectedCount: 1,\n\t\t\t\t\tallSelected: false,\n\t\t\t\t\tselectedRows: [row],\n\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// handle multi select mode\n\t\t\tif (isSelected) {\n\t\t\t\treturn {\n\t\t\t\t\t...state,\n\t\t\t\t\tselectedCount: state.selectedRows.length > 0 ? state.selectedRows.length - 1 : 0,\n\t\t\t\t\tallSelected: false,\n\t\t\t\t\tselectedRows: removeItem(state.selectedRows, row, keyField),\n\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tselectedCount: state.selectedRows.length + 1,\n\t\t\t\tallSelected: state.selectedRows.length + 1 === rowCount,\n\t\t\t\tselectedRows: insertItem(state.selectedRows, row),\n\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t};\n\t\t}\n\n\t\tcase 'SELECT_MULTIPLE_ROWS': {\n\t\t\tconst { keyField, selectedRows, totalRows, mergeSelections } = action;\n\n\t\t\tif (mergeSelections) {\n\t\t\t\tconst selections = [\n\t\t\t\t\t...state.selectedRows,\n\t\t\t\t\t...selectedRows.filter(row => !isRowSelected(row, state.selectedRows, keyField)),\n\t\t\t\t];\n\n\t\t\t\treturn {\n\t\t\t\t\t...state,\n\t\t\t\t\tselectedCount: selections.length,\n\t\t\t\t\tallSelected: false,\n\t\t\t\t\tselectedRows: selections,\n\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tselectedCount: selectedRows.length,\n\t\t\t\tallSelected: selectedRows.length === totalRows,\n\t\t\t\tselectedRows,\n\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t};\n\t\t}\n\n\t\tcase 'CLEAR_SELECTED_ROWS': {\n\t\t\tconst { selectedRowsFlag } = action;\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tallSelected: false,\n\t\t\t\tselectedCount: 0,\n\t\t\t\tselectedRows: [],\n\t\t\t\tselectedRowsFlag,\n\t\t\t};\n\t\t}\n\n\t\tcase 'SORT_CHANGE': {\n\t\t\tconst { sortDirection, selectedColumn, clearSelectedOnSort } = action;\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tselectedColumn,\n\t\t\t\tsortDirection,\n\t\t\t\tcurrentPage: 1,\n\t\t\t\t// when using server-side paging reset selected row counts when sorting\n\t\t\t\t...(clearSelectedOnSort && {\n\t\t\t\t\tallSelected: false,\n\t\t\t\t\tselectedCount: 0,\n\t\t\t\t\tselectedRows: [],\n\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t}),\n\t\t\t};\n\t\t}\n\n\t\tcase 'CHANGE_PAGE': {\n\t\t\tconst { page, paginationServer, visibleOnly, persistSelectedOnPageChange } = action;\n\t\t\tconst mergeSelections = paginationServer && persistSelectedOnPageChange;\n\t\t\tconst clearSelectedOnPage = (paginationServer && !persistSelectedOnPageChange) || visibleOnly;\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tcurrentPage: page,\n\t\t\t\t...(mergeSelections && {\n\t\t\t\t\tallSelected: false,\n\t\t\t\t}),\n\t\t\t\t// when using server-side paging reset selected row counts\n\t\t\t\t...(clearSelectedOnPage && {\n\t\t\t\t\tallSelected: false,\n\t\t\t\t\tselectedCount: 0,\n\t\t\t\t\tselectedRows: [],\n\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t}),\n\t\t\t};\n\t\t}\n\n\t\tcase 'CHANGE_ROWS_PER_PAGE': {\n\t\t\tconst { rowsPerPage, page } = action;\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tcurrentPage: page,\n\t\t\t\trowsPerPage,\n\t\t\t};\n\t\t}\n\t}\n}\n", "import styled, { css } from 'styled-components';\n\nconst disabledCSS = css`\n\tpointer-events: none;\n\topacity: 0.4;\n`;\n\nconst TableStyle = styled.div<{\n\tdisabled?: boolean;\n}>`\n\tposition: relative;\n\tbox-sizing: border-box;\n\tdisplay: flex;\n\tflex-direction: column;\n\twidth: 100%;\n\theight: 100%;\n\tmax-width: 100%;\n\t${({ disabled }) => disabled && disabledCSS};\n\t${({ theme }) => theme.table.style};\n`;\n\nexport default TableStyle;\n", "import styled, { css } from 'styled-components';\n\nconst fixedCSS = css`\n\tposition: sticky;\n\tposition: -webkit-sticky; /* Safari */\n\ttop: 0;\n\tz-index: 1;\n`;\n\nconst Head = styled.div<{\n\t$fixedHeader?: boolean;\n}>`\n\tdisplay: flex;\n\twidth: 100%;\n\t${({ $fixedHeader }) => $fixedHeader && fixedCSS};\n\t${({ theme }) => theme.head.style};\n`;\n\nexport default Head;\n", "import styled from 'styled-components';\n\nconst HeadRow = styled.div<{\n\t$dense?: boolean;\n\tdisabled?: boolean;\n}>`\n\tdisplay: flex;\n\talign-items: stretch;\n\twidth: 100%;\n\t${({ theme }) => theme.headRow.style};\n\t${({ $dense, theme }) => $dense && theme.headRow.denseStyle};\n`;\n\nexport default HeadRow;\n", "import { css, CSSObject, RuleSet } from 'styled-components';\n\nexport const SMALL = 599;\nexport const MEDIUM = 959;\nexport const LARGE = 1280;\n\nexport const media = {\n\tsm: (literals: TemplateStringsArray, ...args: CSSObject[]): RuleSet<object> => css`\n\t\t@media screen and (max-width: ${SMALL}px) {\n\t\t\t${css(literals, ...args)}\n\t\t}\n\t`,\n\tmd: (literals: TemplateStringsArray, ...args: CSSObject[]): RuleSet<object> => css`\n\t\t@media screen and (max-width: ${MEDIUM}px) {\n\t\t\t${css(literals, ...args)}\n\t\t}\n\t`,\n\tlg: (literals: TemplateStringsArray, ...args: CSSObject[]): RuleSet<object> => css`\n\t\t@media screen and (max-width: ${LARGE}px) {\n\t\t\t${css(literals, ...args)}\n\t\t}\n\t`,\n\tcustom:\n\t\t(value: number) =>\n\t\t(literals: TemplateStringsArray, ...args: CSSObject[]): RuleSet<object> => css`\n\t\t\t@media screen and (max-width: ${value}px) {\n\t\t\t\t${css(literals, ...args)}\n\t\t\t}\n\t\t`,\n};\n", "import styled, { css } from 'styled-components';\nimport { media } from './media';\nimport { TableColumnBase } from './types';\n\nexport const CellBase = styled.div<{\n\t$headCell?: boolean;\n\t$noPadding?: boolean;\n}>`\n\tposition: relative;\n\tdisplay: flex;\n\talign-items: center;\n\tbox-sizing: border-box;\n\tline-height: normal;\n\t${({ theme, $headCell }) => theme[$headCell ? 'headCells' : 'cells'].style};\n\t${({ $noPadding }) => $noPadding && 'padding: 0'};\n`;\n\nexport type CellProps = Pick<\n\tTableColumnBase,\n\t'button' | 'grow' | 'maxWidth' | 'minWidth' | 'width' | 'right' | 'center' | 'compact' | 'hide' | 'allowOverflow'\n>;\n\n// Flex calculations\nexport const CellExtended = styled(CellBase)<CellProps>`\n\tflex-grow: ${({ button, grow }) => (grow === 0 || button ? 0 : grow || 1)};\n\tflex-shrink: 0;\n\tflex-basis: 0;\n\tmax-width: ${({ maxWidth }) => maxWidth || '100%'};\n\tmin-width: ${({ minWidth }) => minWidth || '100px'};\n\t${({ width }) =>\n\t\twidth &&\n\t\tcss`\n\t\t\tmin-width: ${width};\n\t\t\tmax-width: ${width};\n\t\t`};\n\t${({ right }) => right && 'justify-content: flex-end'};\n\t${({ button, center }) => (center || button) && 'justify-content: center'};\n\t${({ compact, button }) => (compact || button) && 'padding: 0'};\n\n\t/* handle hiding cells */\n\t${({ hide }) =>\n\t\thide &&\n\t\thide === 'sm' &&\n\t\tmedia.sm`\n    display: none;\n  `};\n\t${({ hide }) =>\n\t\thide &&\n\t\thide === 'md' &&\n\t\tmedia.md`\n    display: none;\n  `};\n\t${({ hide }) =>\n\t\thide &&\n\t\thide === 'lg' &&\n\t\tmedia.lg`\n    display: none;\n  `};\n\t${({ hide }) =>\n\t\thide &&\n\t\tNumber.isInteger(hide) &&\n\t\tmedia.custom(hide as number)`\n    display: none;\n  `};\n`;\n", "import * as React from 'react';\nimport styled, { css, CSSObject } from 'styled-components';\nimport { CellExtended } from './Cell';\nimport { getProperty, getConditionalStyle } from './util';\nimport { TableColumn } from './types';\n\ninterface CellStyleProps {\n\t$renderAsCell: boolean | undefined;\n\t$wrapCell: boolean | undefined;\n\t$allowOverflow: boolean | undefined;\n\t$cellStyle: CSSObject | undefined;\n\t$isDragging: boolean;\n}\n\nconst overflowCSS = css<CellStyleProps>`\n\tdiv:first-child {\n\t\twhite-space: ${({ $wrapCell }) => ($wrapCell ? 'normal' : 'nowrap')};\n\t\toverflow: ${({ $allowOverflow }) => ($allowOverflow ? 'visible' : 'hidden')};\n\t\ttext-overflow: ellipsis;\n\t}\n`;\n\nconst CellStyle = styled(CellExtended).attrs(props => ({\n\tstyle: props.style,\n}))<CellStyleProps>`\n\t${({ $renderAsCell }) => !$renderAsCell && overflowCSS};\n\t${({ theme, $isDragging }) => $isDragging && theme.cells.draggingStyle};\n\t${({ $cellStyle }) => $cellStyle};\n`;\n\ninterface CellProps<T> {\n\tid: string;\n\tdataTag: string | null;\n\tcolumn: TableColumn<T>;\n\trow: T;\n\trowIndex: number;\n\tisDragging: boolean;\n\tonDragStart: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragOver: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnd: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnter: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;\n}\n\nfunction Cell<T>({\n\tid,\n\tcolumn,\n\trow,\n\trowIndex,\n\tdataTag,\n\tisDragging,\n\tonDragStart,\n\tonDragOver,\n\tonDragEnd,\n\tonDragEnter,\n\tonDragLeave,\n}: CellProps<T>): JSX.Element {\n\tconst { conditionalStyle, classNames } = getConditionalStyle(row, column.conditionalCellStyles, ['rdt_TableCell']);\n\n\treturn (\n\t\t<CellStyle\n\t\t\tid={id}\n\t\t\tdata-column-id={column.id}\n\t\t\trole=\"cell\"\n\t\t\tclassName={classNames}\n\t\t\tdata-tag={dataTag}\n\t\t\t$cellStyle={column.style}\n\t\t\t$renderAsCell={!!column.cell}\n\t\t\t$allowOverflow={column.allowOverflow}\n\t\t\tbutton={column.button}\n\t\t\tcenter={column.center}\n\t\t\tcompact={column.compact}\n\t\t\tgrow={column.grow}\n\t\t\thide={column.hide}\n\t\t\tmaxWidth={column.maxWidth}\n\t\t\tminWidth={column.minWidth}\n\t\t\tright={column.right}\n\t\t\twidth={column.width}\n\t\t\t$wrapCell={column.wrap}\n\t\t\tstyle={conditionalStyle as React.CSSProperties}\n\t\t\t$isDragging={isDragging}\n\t\t\tonDragStart={onDragStart}\n\t\t\tonDragOver={onDragOver}\n\t\t\tonDragEnd={onDragEnd}\n\t\t\tonDragEnter={onDragEnter}\n\t\t\tonDragLeave={onDragLeave}\n\t\t>\n\t\t\t{!column.cell && <div data-tag={dataTag}>{getProperty(row, column.selector, column.format, rowIndex)}</div>}\n\t\t\t{column.cell && column.cell(row, rowIndex, column, id)}\n\t\t</CellStyle>\n\t);\n}\n\nexport default React.memo(Cell) as typeof Cell;\n", "import * as React from 'react';\nimport { handleFunctionProps, noop } from './util';\n\nconst defaultComponentName = 'input';\n\nconst calculateBaseStyle = (disabled: boolean) => ({\n\tfontSize: '18px',\n\t...(!disabled && { cursor: 'pointer' }),\n\tpadding: 0,\n\tmarginTop: '1px',\n\tverticalAlign: 'middle',\n\tposition: 'relative',\n});\n\ninterface CheckboxProps {\n\tname: string;\n\t// eslint-disable-next-line @typescript-eslint/no-explicit-any\n\tcomponent?: any;\n\tcomponentOptions?: { [key: string]: unknown };\n\tindeterminate?: boolean;\n\tchecked?: boolean;\n\tdisabled?: boolean;\n\tonClick?: (e: React.MouseEvent) => void;\n}\n\nfunction Checkbox({\n\tname,\n\tcomponent = defaultComponentName,\n\tcomponentOptions = { style: {} },\n\tindeterminate = false,\n\tchecked = false,\n\tdisabled = false,\n\tonClick = noop,\n}: CheckboxProps): JSX.Element {\n\tconst setCheckboxRef = (checkbox: HTMLInputElement) => {\n\t\tif (checkbox) {\n\t\t\t// eslint-disable-next-line no-param-reassign\n\t\t\tcheckbox.indeterminate = indeterminate;\n\t\t}\n\t};\n\n\tconst TagName = component;\n\tconst baseStyle = TagName !== defaultComponentName ? componentOptions.style : calculateBaseStyle(disabled);\n\tconst resolvedComponentOptions = React.useMemo(\n\t\t() => handleFunctionProps(componentOptions, indeterminate),\n\t\t[componentOptions, indeterminate],\n\t);\n\n\treturn (\n\t\t<TagName\n\t\t\t// allow this component to fully control these options\n\t\t\ttype=\"checkbox\"\n\t\t\tref={setCheckboxRef}\n\t\t\tstyle={baseStyle}\n\t\t\tonClick={disabled ? noop : onClick}\n\t\t\tname={name}\n\t\t\taria-label={name}\n\t\t\tchecked={checked}\n\t\t\tdisabled={disabled}\n\t\t\t{...resolvedComponentOptions}\n\t\t\tonChange={noop} // prevent uncontrolled checkbox warnings -  we don't need onChange\n\t\t/>\n\t);\n}\n\nexport default React.memo(Checkbox);\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport { CellBase } from './Cell';\nimport Checkbox from './Checkbox';\nimport { RowState, SingleRowAction, ComponentProps } from './types';\n\nconst TableCellCheckboxStyle = styled(CellBase)`\n\tflex: 0 0 48px;\n\tmin-width: 48px;\n\tjustify-content: center;\n\talign-items: center;\n\tuser-select: none;\n\twhite-space: nowrap;\n`;\n\ntype TableCellCheckboxProps<T> = {\n\tname: string;\n\tkeyField: string;\n\trow: T;\n\trowCount: number;\n\tselected: boolean;\n\tselectableRowsComponent: 'input' | React.ReactNode;\n\tselectableRowsComponentProps: ComponentProps;\n\tselectableRowsSingle: boolean;\n\tselectableRowDisabled: RowState<T>;\n\tonSelectedRow: (action: SingleRowAction<T>) => void;\n};\n\nfunction TableCellCheckbox<T>({\n\tname,\n\tkeyField,\n\trow,\n\trowCount,\n\tselected,\n\tselectableRowsComponent,\n\tselectableRowsComponentProps,\n\tselectableRowsSingle,\n\tselectableRowDisabled,\n\tonSelectedRow,\n}: TableCellCheckboxProps<T>): JSX.Element {\n\tconst disabled = !!(selectableRowDisabled && selectableRowDisabled(row));\n\n\tconst handleOnRowSelected = () => {\n\t\tonSelectedRow({\n\t\t\ttype: 'SELECT_SINGLE_ROW',\n\t\t\trow,\n\t\t\tisSelected: selected,\n\t\t\tkeyField,\n\t\t\trowCount,\n\t\t\tsingleSelect: selectableRowsSingle,\n\t\t});\n\t};\n\n\treturn (\n\t\t<TableCellCheckboxStyle onClick={(e: React.MouseEvent) => e.stopPropagation()} className=\"rdt_TableCell\" $noPadding>\n\t\t\t<Checkbox\n\t\t\t\tname={name}\n\t\t\t\tcomponent={selectableRowsComponent}\n\t\t\t\tcomponentOptions={selectableRowsComponentProps}\n\t\t\t\tchecked={selected}\n\t\t\t\taria-checked={selected}\n\t\t\t\tonClick={handleOnRowSelected}\n\t\t\t\tdisabled={disabled}\n\t\t\t/>\n\t\t</TableCellCheckboxStyle>\n\t);\n}\n\nexport default TableCellCheckbox;\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport { ExpandableIcon } from './types';\n\nconst ButtonStyle = styled.button`\n\tdisplay: inline-flex;\n\talign-items: center;\n\tuser-select: none;\n\twhite-space: nowrap;\n\tborder: none;\n\tbackground-color: transparent;\n\t${({ theme }) => theme.expanderButton.style};\n`;\n\ntype ExpanderButtonProps<T> = {\n\tdisabled?: boolean;\n\texpanded?: boolean;\n\texpandableIcon: ExpandableIcon;\n\tid: string | number;\n\trow: T;\n\tonToggled?: (row: T) => void;\n};\n\nfunction ExpanderButton<T>({\n\tdisabled = false,\n\texpanded = false,\n\texpandableIcon,\n\tid,\n\trow,\n\tonToggled,\n}: ExpanderButtonProps<T>): JSX.Element {\n\tconst icon = expanded ? expandableIcon.expanded : expandableIcon.collapsed;\n\tconst handleToggle = () => onToggled && onToggled(row);\n\n\treturn (\n\t\t<ButtonStyle\n\t\t\taria-disabled={disabled}\n\t\t\tonClick={handleToggle}\n\t\t\tdata-testid={`expander-button-${id}`}\n\t\t\tdisabled={disabled}\n\t\t\taria-label={expanded ? 'Collapse Row' : 'Expand Row'}\n\t\t\trole=\"button\"\n\t\t\ttype=\"button\"\n\t\t>\n\t\t\t{icon}\n\t\t</ButtonStyle>\n\t);\n}\n\nexport default ExpanderButton;\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport { CellBase } from './Cell';\nimport ExpanderButton from './ExpanderButton';\nimport { ExpandableIcon } from './types';\n\nconst CellExpanderStyle = styled(CellBase)`\n\twhite-space: nowrap;\n\tfont-weight: 400;\n\tmin-width: 48px;\n\t${({ theme }) => theme.expanderCell.style};\n`;\n\ntype CellExpanderProps<T> = {\n\tdisabled: boolean;\n\texpanded: boolean;\n\texpandableIcon: ExpandableIcon;\n\tid: string | number;\n\trow: T;\n\tonToggled: (row: T) => void;\n};\n\nfunction CellExpander<T>({\n\trow,\n\texpanded = false,\n\texpandableIcon,\n\tid,\n\tonToggled,\n\tdisabled = false,\n}: CellExpanderProps<T>): JSX.Element {\n\treturn (\n\t\t<CellExpanderStyle onClick={(e: React.MouseEvent) => e.stopPropagation()} $noPadding>\n\t\t\t<ExpanderButton\n\t\t\t\tid={id}\n\t\t\t\trow={row}\n\t\t\t\texpanded={expanded}\n\t\t\t\texpandableIcon={expandableIcon}\n\t\t\t\tdisabled={disabled}\n\t\t\t\tonToggled={onToggled}\n\t\t\t/>\n\t\t</CellExpanderStyle>\n\t);\n}\n\nexport default CellExpander;\n", "import * as React from 'react';\nimport styled, { CSSObject } from 'styled-components';\nimport { ComponentProps, ExpandableRowsComponent } from './types';\n\nconst ExpanderRowStyle = styled.div<{\n\t$extendedRowStyle: CSSObject;\n}>`\n\twidth: 100%;\n\tbox-sizing: border-box;\n\t${({ theme }) => theme.expanderRow.style};\n\t${({ $extendedRowStyle }) => $extendedRowStyle};\n`;\n\ntype ExpanderRowProps<T> = {\n\tdata: T;\n\tExpanderComponent: ExpandableRowsComponent<T>;\n\textendedRowStyle: CSSObject;\n\textendedClassNames: string;\n\texpanderComponentProps: ComponentProps;\n};\n\nfunction ExpanderRow<T>({\n\tdata,\n\tExpanderComponent,\n\texpanderComponentProps,\n\textendedRowStyle,\n\textendedClassNames,\n}: ExpanderRowProps<T>): JSX.Element {\n\t// we need to strip of rdt_TableRow from extendedClassNames\n\tconst classNamesSplit = extendedClassNames.split(' ').filter(c => c !== 'rdt_TableRow');\n\tconst classNames = ['rdt_ExpanderRow', ...classNamesSplit].join(' ');\n\n\treturn (\n\t\t<ExpanderRowStyle className={classNames} $extendedRowStyle={extendedRowStyle as CSSObject}>\n\t\t\t<ExpanderComponent data={data} {...expanderComponentProps} />\n\t\t</ExpanderRowStyle>\n\t);\n}\n\nexport default React.memo(ExpanderRow) as typeof ExpanderRow;\n", "export const STOP_PROP_TAG = 'allowRowEvents';\n\nexport enum Direction {\n\tLTR = 'ltr',\n\tRTL = 'rtl',\n\tAUTO = 'auto',\n}\n\nexport enum Alignment {\n\tLEFT = 'left',\n\tRIGHT = 'right',\n\tCENTER = 'center',\n}\n\nexport enum Media {\n\tSM = 'sm',\n\tMD = 'md',\n\tLG = 'lg',\n}\n", "import * as React from 'react';\nimport styled, { css } from 'styled-components';\nimport TableCell from './TableCell';\nimport TableCellCheckbox from './TableCellCheckbox';\nimport TableCellExpander from './TableCellExpander';\nimport ExpanderRow from './ExpanderRow';\nimport { prop, equalizeId, getConditionalStyle, isOdd, noop } from './util';\nimport { STOP_PROP_TAG } from './constants';\nimport { TableRow, SingleRowAction, TableProps } from './types';\nimport { CSSObject } from 'styled-components';\n\nconst highlightCSS = css<{\n\t$highlightOnHover?: boolean;\n}>`\n\t&:hover {\n\t\t${({ $highlightOnHover, theme }) => $highlightOnHover && theme.rows.highlightOnHoverStyle};\n\t}\n`;\n\nconst pointerCSS = css`\n\t&:hover {\n\t\tcursor: pointer;\n\t}\n`;\n\nconst TableRowStyle = styled.div.attrs(props => ({\n\tstyle: props.style,\n}))<{\n\t$dense?: boolean;\n\t$highlightOnHover?: boolean;\n\t$pointerOnHover?: boolean;\n\t$selected?: boolean;\n\t$striped?: boolean;\n\t$conditionalStyle?: CSSObject;\n}>`\n\tdisplay: flex;\n\talign-items: stretch;\n\talign-content: stretch;\n\twidth: 100%;\n\tbox-sizing: border-box;\n\t${({ theme }) => theme.rows.style};\n\t${({ $dense, theme }) => $dense && theme.rows.denseStyle};\n\t${({ $striped, theme }) => $striped && theme.rows.stripedStyle};\n\t${({ $highlightOnHover }) => $highlightOnHover && highlightCSS};\n\t${({ $pointerOnHover }) => $pointerOnHover && pointerCSS};\n\t${({ $selected, theme }) => $selected && theme.rows.selectedHighlightStyle};\n\t${({ $conditionalStyle }) => $conditionalStyle};\n`;\n\ntype DProps<T> = Pick<\n\tTableProps<T>,\n\t| 'columns'\n\t| 'conditionalRowStyles'\n\t| 'dense'\n\t| 'expandableIcon'\n\t| 'expandableRows'\n\t| 'expandableRowsComponent'\n\t| 'expandableRowsComponentProps'\n\t| 'expandableRowsHideExpander'\n\t| 'expandOnRowClicked'\n\t| 'expandOnRowDoubleClicked'\n\t| 'highlightOnHover'\n\t| 'expandableInheritConditionalStyles'\n\t| 'keyField'\n\t| 'onRowClicked'\n\t| 'onRowDoubleClicked'\n\t| 'onRowMouseEnter'\n\t| 'onRowMouseLeave'\n\t| 'onRowExpandToggled'\n\t| 'pointerOnHover'\n\t| 'selectableRowDisabled'\n\t| 'selectableRows'\n\t| 'selectableRowsComponent'\n\t| 'selectableRowsComponentProps'\n\t| 'selectableRowsHighlight'\n\t| 'selectableRowsSingle'\n\t| 'striped'\n>;\n\ninterface TableRowProps<T> extends Required<DProps<T>> {\n\tdraggingColumnId: number | string;\n\tdefaultExpanded?: boolean;\n\tdefaultExpanderDisabled: boolean;\n\tid: string | number;\n\tonSelectedRow: (action: SingleRowAction<T>) => void;\n\tpointerOnHover: boolean;\n\trow: T;\n\trowCount: number;\n\trowIndex: number;\n\tselected: boolean;\n\tonDragStart: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragOver: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnd: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnter: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;\n}\n\nfunction Row<T>({\n\tcolumns = [],\n\tconditionalRowStyles = [],\n\tdefaultExpanded = false,\n\tdefaultExpanderDisabled = false,\n\tdense = false,\n\texpandableIcon,\n\texpandableRows = false,\n\texpandableRowsComponent,\n\texpandableRowsComponentProps,\n\texpandableRowsHideExpander,\n\texpandOnRowClicked = false,\n\texpandOnRowDoubleClicked = false,\n\thighlightOnHover = false,\n\tid,\n\texpandableInheritConditionalStyles,\n\tkeyField,\n\tonRowClicked = noop,\n\tonRowDoubleClicked = noop,\n\tonRowMouseEnter = noop,\n\tonRowMouseLeave = noop,\n\tonRowExpandToggled = noop,\n\tonSelectedRow = noop,\n\tpointerOnHover = false,\n\trow,\n\trowCount,\n\trowIndex,\n\tselectableRowDisabled = null,\n\tselectableRows = false,\n\tselectableRowsComponent,\n\tselectableRowsComponentProps,\n\tselectableRowsHighlight = false,\n\tselectableRowsSingle = false,\n\tselected,\n\tstriped = false,\n\tdraggingColumnId,\n\tonDragStart,\n\tonDragOver,\n\tonDragEnd,\n\tonDragEnter,\n\tonDragLeave,\n}: TableRowProps<T>): JSX.Element {\n\tconst [expanded, setExpanded] = React.useState(defaultExpanded);\n\n\tReact.useEffect(() => {\n\t\tsetExpanded(defaultExpanded);\n\t}, [defaultExpanded]);\n\n\tconst handleExpanded = React.useCallback(() => {\n\t\tsetExpanded(!expanded);\n\t\tonRowExpandToggled(!expanded, row);\n\t}, [expanded, onRowExpandToggled, row]);\n\n\tconst showPointer = pointerOnHover || (expandableRows && (expandOnRowClicked || expandOnRowDoubleClicked));\n\n\tconst handleRowClick = React.useCallback(\n\t\t(e: React.MouseEvent<HTMLDivElement>) => {\n\t\t\t// use event delegation allow events to propagate only when the element with data-tag STOP_PROP_TAG is present\n\t\t\tconst target = e.target as HTMLDivElement;\n\n\t\t\tif (target.getAttribute('data-tag') === STOP_PROP_TAG) {\n\t\t\t\tonRowClicked(row, e);\n\n\t\t\t\tif (!defaultExpanderDisabled && expandableRows && expandOnRowClicked) {\n\t\t\t\t\thandleExpanded();\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t[defaultExpanderDisabled, expandOnRowClicked, expandableRows, handleExpanded, onRowClicked, row],\n\t);\n\n\tconst handleRowDoubleClick = React.useCallback(\n\t\t(e: React.MouseEvent<HTMLDivElement>) => {\n\t\t\tconst target = e.target as HTMLDivElement;\n\n\t\t\tif (target.getAttribute('data-tag') === STOP_PROP_TAG) {\n\t\t\t\tonRowDoubleClicked(row, e);\n\t\t\t\tif (!defaultExpanderDisabled && expandableRows && expandOnRowDoubleClicked) {\n\t\t\t\t\thandleExpanded();\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t[defaultExpanderDisabled, expandOnRowDoubleClicked, expandableRows, handleExpanded, onRowDoubleClicked, row],\n\t);\n\n\tconst handleRowMouseEnter = React.useCallback(\n\t\t(e: React.MouseEvent<Element, MouseEvent>) => {\n\t\t\tonRowMouseEnter(row, e);\n\t\t},\n\t\t[onRowMouseEnter, row],\n\t);\n\n\tconst handleRowMouseLeave = React.useCallback(\n\t\t(e: React.MouseEvent<Element, MouseEvent>) => {\n\t\t\tonRowMouseLeave(row, e);\n\t\t},\n\t\t[onRowMouseLeave, row],\n\t);\n\n\tconst rowKeyField = prop(row as TableRow, keyField);\n\tconst { conditionalStyle, classNames } = getConditionalStyle(row, conditionalRowStyles, ['rdt_TableRow']);\n\tconst highlightSelected = selectableRowsHighlight && selected;\n\tconst inheritStyles = expandableInheritConditionalStyles ? conditionalStyle : {};\n\tconst isStriped = striped && isOdd(rowIndex);\n\n\treturn (\n\t\t<>\n\t\t\t<TableRowStyle\n\t\t\t\tid={`row-${id}`}\n\t\t\t\trole=\"row\"\n\t\t\t\t$striped={isStriped}\n\t\t\t\t$highlightOnHover={highlightOnHover}\n\t\t\t\t$pointerOnHover={!defaultExpanderDisabled && showPointer}\n\t\t\t\t$dense={dense}\n\t\t\t\tonClick={handleRowClick}\n\t\t\t\tonDoubleClick={handleRowDoubleClick}\n\t\t\t\tonMouseEnter={handleRowMouseEnter}\n\t\t\t\tonMouseLeave={handleRowMouseLeave}\n\t\t\t\tclassName={classNames}\n\t\t\t\t$selected={highlightSelected}\n\t\t\t\t$conditionalStyle={conditionalStyle}\n\t\t\t>\n\t\t\t\t{selectableRows && (\n\t\t\t\t\t<TableCellCheckbox\n\t\t\t\t\t\tname={`select-row-${rowKeyField}`}\n\t\t\t\t\t\tkeyField={keyField}\n\t\t\t\t\t\trow={row}\n\t\t\t\t\t\trowCount={rowCount}\n\t\t\t\t\t\tselected={selected}\n\t\t\t\t\t\tselectableRowsComponent={selectableRowsComponent}\n\t\t\t\t\t\tselectableRowsComponentProps={selectableRowsComponentProps}\n\t\t\t\t\t\tselectableRowDisabled={selectableRowDisabled}\n\t\t\t\t\t\tselectableRowsSingle={selectableRowsSingle}\n\t\t\t\t\t\tonSelectedRow={onSelectedRow}\n\t\t\t\t\t/>\n\t\t\t\t)}\n\n\t\t\t\t{expandableRows && !expandableRowsHideExpander && (\n\t\t\t\t\t<TableCellExpander\n\t\t\t\t\t\tid={rowKeyField as string}\n\t\t\t\t\t\texpandableIcon={expandableIcon}\n\t\t\t\t\t\texpanded={expanded}\n\t\t\t\t\t\trow={row}\n\t\t\t\t\t\tonToggled={handleExpanded}\n\t\t\t\t\t\tdisabled={defaultExpanderDisabled}\n\t\t\t\t\t/>\n\t\t\t\t)}\n\n\t\t\t\t{columns.map(column => {\n\t\t\t\t\tif (column.omit) {\n\t\t\t\t\t\treturn null;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn (\n\t\t\t\t\t\t<TableCell\n\t\t\t\t\t\t\tid={`cell-${column.id}-${rowKeyField}`}\n\t\t\t\t\t\t\tkey={`cell-${column.id}-${rowKeyField}`}\n\t\t\t\t\t\t\t// apply a tag that Row will use to stop event propagation when TableCell is clicked\n\t\t\t\t\t\t\tdataTag={column.ignoreRowClick || column.button ? null : STOP_PROP_TAG}\n\t\t\t\t\t\t\tcolumn={column}\n\t\t\t\t\t\t\trow={row}\n\t\t\t\t\t\t\trowIndex={rowIndex}\n\t\t\t\t\t\t\tisDragging={equalizeId(draggingColumnId, column.id)}\n\t\t\t\t\t\t\tonDragStart={onDragStart}\n\t\t\t\t\t\t\tonDragOver={onDragOver}\n\t\t\t\t\t\t\tonDragEnd={onDragEnd}\n\t\t\t\t\t\t\tonDragEnter={onDragEnter}\n\t\t\t\t\t\t\tonDragLeave={onDragLeave}\n\t\t\t\t\t\t/>\n\t\t\t\t\t);\n\t\t\t\t})}\n\t\t\t</TableRowStyle>\n\n\t\t\t{expandableRows && expanded && (\n\t\t\t\t<ExpanderRow\n\t\t\t\t\tkey={`expander-${rowKeyField}`}\n\t\t\t\t\tdata={row}\n\t\t\t\t\textendedRowStyle={inheritStyles}\n\t\t\t\t\textendedClassNames={classNames}\n\t\t\t\t\tExpanderComponent={expandableRowsComponent}\n\t\t\t\t\texpanderComponentProps={expandableRowsComponentProps}\n\t\t\t\t/>\n\t\t\t)}\n\t\t</>\n\t);\n}\n\nexport default Row;\n", "import React from 'react';\nimport styled from 'styled-components';\nimport { SortOrder } from '../DataTable/types';\n\nconst Icon = styled.span<{\n\t$sortActive: boolean;\n\t$sortDirection: SortOrder;\n}>`\n\tpadding: 2px;\n\tcolor: inherit;\n\tflex-grow: 0;\n\tflex-shrink: 0;\n\t${({ $sortActive }) => ($sortActive ? 'opacity: 1' : 'opacity: 0')};\n\t${({ $sortDirection }) => $sortDirection === 'desc' && 'transform: rotate(180deg)'};\n`;\n\ninterface NativeSortIconProps {\n\tsortActive: boolean;\n\tsortDirection: SortOrder;\n}\n\nconst NativeSortIcon: React.FC<NativeSortIconProps> = ({ sortActive, sortDirection }) => (\n\t<Icon $sortActive={sortActive} $sortDirection={sortDirection}>\n\t\t&#9650;\n\t</Icon>\n);\n\nexport default NativeSortIcon;\n", "import * as React from 'react';\nimport styled, { css } from 'styled-components';\nimport { CellExtended, CellProps } from './Cell';\nimport NativeSortIcon from '../icons/NativeSortIcon';\nimport { equalizeId } from './util';\nimport { TableColumn, SortAction, SortOrder } from './types';\n\ninterface ColumnStyleProps extends CellProps {\n\t$isDragging?: boolean;\n\tonDragStart: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragOver: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnd: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnter: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;\n}\n\nconst ColumnStyled = styled(CellExtended)<ColumnStyleProps>`\n\t${({ button }) => button && 'text-align: center'};\n\t${({ theme, $isDragging }) => $isDragging && theme.headCells.draggingStyle};\n`;\n\ninterface ColumnSortableProps {\n\tdisabled: boolean;\n\t$sortActive: boolean;\n}\n\nconst sortableCSS = css<ColumnSortableProps>`\n\tcursor: pointer;\n\tspan.__rdt_custom_sort_icon__ {\n\t\ti,\n\t\tsvg {\n\t\t\ttransform: 'translate3d(0, 0, 0)';\n\t\t\t${({ $sortActive }) => ($sortActive ? 'opacity: 1' : 'opacity: 0')};\n\t\t\tcolor: inherit;\n\t\t\tfont-size: 18px;\n\t\t\theight: 18px;\n\t\t\twidth: 18px;\n\t\t\tbackface-visibility: hidden;\n\t\t\ttransform-style: preserve-3d;\n\t\t\ttransition-duration: 95ms;\n\t\t\ttransition-property: transform;\n\t\t}\n\n\t\t&.asc i,\n\t\t&.asc svg {\n\t\t\ttransform: rotate(180deg);\n\t\t}\n\t}\n\n\t${({ $sortActive }) =>\n\t\t!$sortActive &&\n\t\tcss`\n\t\t\t&:hover,\n\t\t\t&:focus {\n\t\t\t\topacity: 0.7;\n\n\t\t\t\tspan,\n\t\t\t\tspan.__rdt_custom_sort_icon__ * {\n\t\t\t\t\topacity: 0.7;\n\t\t\t\t}\n\t\t\t}\n\t\t`};\n`;\n\nconst ColumnSortable = styled.div<ColumnSortableProps>`\n\tdisplay: inline-flex;\n\talign-items: center;\n\tjustify-content: inherit;\n\theight: 100%;\n\twidth: 100%;\n\toutline: none;\n\tuser-select: none;\n\toverflow: hidden;\n\t${({ disabled }) => !disabled && sortableCSS};\n`;\n\nconst ColumnText = styled.div`\n\toverflow: hidden;\n\twhite-space: nowrap;\n\ttext-overflow: ellipsis;\n`;\n\ntype TableColProps<T> = {\n\tcolumn: TableColumn<T>;\n\tdisabled: boolean;\n\tdraggingColumnId?: string | number;\n\tsortIcon?: React.ReactNode;\n\tpagination: boolean;\n\tpaginationServer: boolean;\n\tpersistSelectedOnSort: boolean;\n\tselectedColumn: TableColumn<T>;\n\tsortDirection: SortOrder;\n\tsortServer: boolean;\n\tselectableRowsVisibleOnly: boolean;\n\tonSort: (action: SortAction<T>) => void;\n\tonDragStart: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragOver: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnd: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnter: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;\n};\n\nfunction TableCol<T>({\n\tcolumn,\n\tdisabled,\n\tdraggingColumnId,\n\tselectedColumn = {},\n\tsortDirection,\n\tsortIcon,\n\tsortServer,\n\tpagination,\n\tpaginationServer,\n\tpersistSelectedOnSort,\n\tselectableRowsVisibleOnly,\n\tonSort,\n\tonDragStart,\n\tonDragOver,\n\tonDragEnd,\n\tonDragEnter,\n\tonDragLeave,\n}: TableColProps<T>): JSX.Element | null {\n\tReact.useEffect(() => {\n\t\tif (typeof column.selector === 'string') {\n\t\t\tconsole.error(\n\t\t\t\t`Warning: ${column.selector} is a string based column selector which has been deprecated as of v7 and will be removed in v8. Instead, use a selector function e.g. row => row[field]...`,\n\t\t\t);\n\t\t}\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, []);\n\n\tconst [showTooltip, setShowTooltip] = React.useState(false);\n\tconst columnRef = React.useRef<HTMLDivElement | null>(null);\n\n\tReact.useEffect(() => {\n\t\tif (columnRef.current) {\n\t\t\tsetShowTooltip(columnRef.current.scrollWidth > columnRef.current.clientWidth);\n\t\t}\n\t}, [showTooltip]);\n\n\tif (column.omit) {\n\t\treturn null;\n\t}\n\n\tconst handleSortChange = () => {\n\t\tif (!column.sortable && !column.selector) {\n\t\t\treturn;\n\t\t}\n\n\t\tlet direction = sortDirection;\n\n\t\tif (equalizeId(selectedColumn.id, column.id)) {\n\t\t\tdirection = sortDirection === SortOrder.ASC ? SortOrder.DESC : SortOrder.ASC;\n\t\t}\n\n\t\tonSort({\n\t\t\ttype: 'SORT_CHANGE',\n\t\t\tsortDirection: direction,\n\t\t\tselectedColumn: column,\n\t\t\tclearSelectedOnSort:\n\t\t\t\t(pagination && paginationServer && !persistSelectedOnSort) || sortServer || selectableRowsVisibleOnly,\n\t\t});\n\t};\n\n\tconst handleKeyPress = (event: React.KeyboardEvent<HTMLDivElement>) => {\n\t\tif (event.key === 'Enter') {\n\t\t\thandleSortChange();\n\t\t}\n\t};\n\n\tconst renderNativeSortIcon = (sortActive: boolean) => (\n\t\t<NativeSortIcon sortActive={sortActive} sortDirection={sortDirection} />\n\t);\n\n\tconst renderCustomSortIcon = () => (\n\t\t<span className={[sortDirection, '__rdt_custom_sort_icon__'].join(' ')}>{sortIcon}</span>\n\t);\n\n\tconst sortActive = !!(column.sortable && equalizeId(selectedColumn.id, column.id));\n\tconst disableSort = !column.sortable || disabled;\n\tconst nativeSortIconLeft = column.sortable && !sortIcon && !column.right;\n\tconst nativeSortIconRight = column.sortable && !sortIcon && column.right;\n\tconst customSortIconLeft = column.sortable && sortIcon && !column.right;\n\tconst customSortIconRight = column.sortable && sortIcon && column.right;\n\n\treturn (\n\t\t<ColumnStyled\n\t\t\tdata-column-id={column.id}\n\t\t\tclassName=\"rdt_TableCol\"\n\t\t\t$headCell\n\t\t\tallowOverflow={column.allowOverflow}\n\t\t\tbutton={column.button}\n\t\t\tcompact={column.compact}\n\t\t\tgrow={column.grow}\n\t\t\thide={column.hide}\n\t\t\tmaxWidth={column.maxWidth}\n\t\t\tminWidth={column.minWidth}\n\t\t\tright={column.right}\n\t\t\tcenter={column.center}\n\t\t\twidth={column.width}\n\t\t\tdraggable={column.reorder}\n\t\t\t$isDragging={equalizeId(column.id, draggingColumnId)}\n\t\t\tonDragStart={onDragStart}\n\t\t\tonDragOver={onDragOver}\n\t\t\tonDragEnd={onDragEnd}\n\t\t\tonDragEnter={onDragEnter}\n\t\t\tonDragLeave={onDragLeave}\n\t\t>\n\t\t\t{column.name && (\n\t\t\t\t<ColumnSortable\n\t\t\t\t\tdata-column-id={column.id}\n\t\t\t\t\tdata-sort-id={column.id}\n\t\t\t\t\trole=\"columnheader\"\n\t\t\t\t\ttabIndex={0}\n\t\t\t\t\tclassName=\"rdt_TableCol_Sortable\"\n\t\t\t\t\tonClick={!disableSort ? handleSortChange : undefined}\n\t\t\t\t\tonKeyPress={!disableSort ? handleKeyPress : undefined}\n\t\t\t\t\t$sortActive={!disableSort && sortActive}\n\t\t\t\t\tdisabled={disableSort}\n\t\t\t\t>\n\t\t\t\t\t{!disableSort && customSortIconRight && renderCustomSortIcon()}\n\t\t\t\t\t{!disableSort && nativeSortIconRight && renderNativeSortIcon(sortActive)}\n\n\t\t\t\t\t{typeof column.name === 'string' ? (\n\t\t\t\t\t\t<ColumnText title={showTooltip ? column.name : undefined} ref={columnRef} data-column-id={column.id}>\n\t\t\t\t\t\t\t{column.name}\n\t\t\t\t\t\t</ColumnText>\n\t\t\t\t\t) : (\n\t\t\t\t\t\tcolumn.name\n\t\t\t\t\t)}\n\n\t\t\t\t\t{!disableSort && customSortIconLeft && renderCustomSortIcon()}\n\t\t\t\t\t{!disableSort && nativeSortIconLeft && renderNativeSortIcon(sortActive)}\n\t\t\t\t</ColumnSortable>\n\t\t\t)}\n\t\t</ColumnStyled>\n\t);\n}\n\nexport default React.memo(TableCol) as typeof TableCol;\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport { CellBase } from './Cell';\nimport Checkbox from './Checkbox';\nimport { AllRowsAction, RowState } from './types';\n\nconst ColumnStyle = styled(CellBase)`\n\tflex: 0 0 48px;\n\tjustify-content: center;\n\talign-items: center;\n\tuser-select: none;\n\twhite-space: nowrap;\n\tfont-size: unset;\n`;\n\ninterface ColumnCheckboxProps<T> {\n\theadCell?: boolean;\n\tselectableRowsComponent: 'input' | React.ReactNode;\n\tselectableRowsComponentProps: Record<string, unknown>;\n\tselectableRowDisabled: RowState<T>;\n\tkeyField: string;\n\tmergeSelections: boolean;\n\trowData: T[];\n\tselectedRows: T[];\n\tallSelected: boolean;\n\tonSelectAllRows: (action: AllRowsAction<T>) => void;\n}\n\nfunction ColumnCheckbox<T>({\n\theadCell = true,\n\trowData,\n\tkeyField,\n\tallSelected,\n\tmergeSelections,\n\tselectedRows,\n\tselectableRowsComponent,\n\tselectableRowsComponentProps,\n\tselectableRowDisabled,\n\tonSelectAllRows,\n}: ColumnCheckboxProps<T>): JSX.Element {\n\tconst indeterminate = selectedRows.length > 0 && !allSelected;\n\tconst rows = selectableRowDisabled ? rowData.filter((row: T) => !selectableRowDisabled(row)) : rowData;\n\tconst isDisabled = rows.length === 0;\n\t// The row count should subtract rows that are disabled\n\tconst rowCount = Math.min(rowData.length, rows.length);\n\n\tconst handleSelectAll = () => {\n\t\tonSelectAllRows({\n\t\t\ttype: 'SELECT_ALL_ROWS',\n\t\t\trows,\n\t\t\trowCount,\n\t\t\tmergeSelections,\n\t\t\tkeyField,\n\t\t});\n\t};\n\n\treturn (\n\t\t<ColumnStyle className=\"rdt_TableCol\" $headCell={headCell} $noPadding>\n\t\t\t<Checkbox\n\t\t\t\tname=\"select-all-rows\"\n\t\t\t\tcomponent={selectableRowsComponent}\n\t\t\t\tcomponentOptions={selectableRowsComponentProps}\n\t\t\t\tonClick={handleSelectAll}\n\t\t\t\tchecked={allSelected}\n\t\t\t\tindeterminate={indeterminate}\n\t\t\t\tdisabled={isDisabled}\n\t\t\t/>\n\t\t</ColumnStyle>\n\t);\n}\n\nexport default ColumnCheckbox;\n", "import * as React from 'react';\nimport { Direction } from '../DataTable/constants';\n\nfunction useRTL(direction: Direction = Direction.AUTO): boolean {\n\tconst isClient = typeof window === 'object';\n\n\tconst [isRTL, setIsRTL] = React.useState(false);\n\n\tReact.useEffect(() => {\n\t\tif (!isClient) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (direction === 'auto') {\n\t\t\tconst canUse = !!(window.document && window.document.createElement);\n\t\t\tconst bodyRTL = <HTMLScriptElement>document.getElementsByTagName('BODY')[0];\n\t\t\tconst htmlTRL = <HTMLScriptElement>document.getElementsByTagName('HTML')[0];\n\t\t\tconst hasRTL = bodyRTL.dir === 'rtl' || htmlTRL.dir === 'rtl';\n\n\t\t\tsetIsRTL(canUse && hasRTL);\n\n\t\t\treturn;\n\t\t}\n\n\t\tsetIsRTL(direction === 'rtl');\n\t}, [direction, isClient]);\n\n\treturn isRTL;\n}\n\nexport default useRTL;\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport useRTL from '../hooks/useRTL';\nimport { Direction } from './constants';\nimport { ContextMessage } from './types';\n\nconst Title = styled.div`\n\tdisplay: flex;\n\talign-items: center;\n\tflex: 1 0 auto;\n\theight: 100%;\n\tcolor: ${({ theme }) => theme.contextMenu.fontColor};\n\tfont-size: ${({ theme }) => theme.contextMenu.fontSize};\n\tfont-weight: 400;\n`;\n\nconst ContextActions = styled.div`\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: flex-end;\n\tflex-wrap: wrap;\n`;\n\nconst ContextMenuStyle = styled.div<{\n\t$rtl?: boolean;\n\t$visible: boolean;\n}>`\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbox-sizing: inherit;\n\tz-index: 1;\n\talign-items: center;\n\tjustify-content: space-between;\n\tdisplay: flex;\n\t${({ $rtl }) => $rtl && 'direction: rtl'};\n\t${({ theme }) => theme.contextMenu.style};\n\t${({ theme, $visible }) => $visible && theme.contextMenu.activeStyle};\n`;\n\nconst generateDefaultContextTitle = (contextMessage: ContextMessage, selectedCount: number, rtl: boolean) => {\n\tif (selectedCount === 0) {\n\t\treturn null;\n\t}\n\n\tconst datumName = selectedCount === 1 ? contextMessage.singular : contextMessage.plural;\n\n\t// TODO: add mock document rtl tests\n\tif (rtl) {\n\t\treturn `${selectedCount} ${contextMessage.message || ''} ${datumName}`;\n\t}\n\n\treturn `${selectedCount} ${datumName} ${contextMessage.message || ''}`;\n};\n\ntype ContextMenuProps = {\n\tcontextMessage: ContextMessage;\n\tcontextActions: React.ReactNode | React.ReactNode[];\n\tcontextComponent: React.ReactNode | null;\n\tselectedCount: number;\n\tdirection: Direction;\n};\n\nfunction ContextMenu({\n\tcontextMessage,\n\tcontextActions,\n\tcontextComponent,\n\tselectedCount,\n\tdirection,\n}: ContextMenuProps): JSX.Element {\n\tconst isRTL = useRTL(direction);\n\tconst visible = selectedCount > 0;\n\n\tif (contextComponent) {\n\t\treturn (\n\t\t\t<ContextMenuStyle $visible={visible}>\n\t\t\t\t{React.cloneElement(contextComponent as React.ReactElement, { selectedCount })}\n\t\t\t</ContextMenuStyle>\n\t\t);\n\t}\n\n\treturn (\n\t\t<ContextMenuStyle $visible={visible} $rtl={isRTL}>\n\t\t\t<Title>{generateDefaultContextTitle(contextMessage, selectedCount, isRTL)}</Title>\n\t\t\t<ContextActions>{contextActions}</ContextActions>\n\t\t</ContextMenuStyle>\n\t);\n}\n\nexport default ContextMenu;\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport ContextMenu from './ContextMenu';\nimport { Direction } from './constants';\nimport { ContextMessage } from './types';\n\nconst HeaderStyle = styled.div`\n\tposition: relative;\n\tbox-sizing: border-box;\n\toverflow: hidden;\n\tdisplay: flex;\n\tflex: 1 1 auto;\n\talign-items: center;\n\tjustify-content: space-between;\n\twidth: 100%;\n\tflex-wrap: wrap;\n\t${({ theme }) => theme.header.style}\n`;\n\nconst Title = styled.div`\n\tflex: 1 0 auto;\n\tcolor: ${({ theme }) => theme.header.fontColor};\n\tfont-size: ${({ theme }) => theme.header.fontSize};\n\tfont-weight: 400;\n`;\n\nconst Actions = styled.div`\n\tflex: 1 0 auto;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: flex-end;\n\n\t> * {\n\t\tmargin-left: 5px;\n\t}\n`;\n\ntype HeaderProps = {\n\ttitle?: string | React.ReactNode;\n\tactions?: React.ReactNode | React.ReactNode[];\n\tdirection: Direction;\n\tselectedCount: number;\n\tshowMenu?: boolean;\n\tcontextMessage: ContextMessage;\n\tcontextActions: React.ReactNode | React.ReactNode[];\n\tcontextComponent: React.ReactNode | null;\n};\n\nconst Header = ({\n\ttitle,\n\tactions = null,\n\tcontextMessage,\n\tcontextActions,\n\tcontextComponent,\n\tselectedCount,\n\tdirection,\n\tshowMenu = true,\n}: HeaderProps): JSX.Element => (\n\t<HeaderStyle className=\"rdt_TableHeader\" role=\"heading\" aria-level={1}>\n\t\t<Title>{title}</Title>\n\t\t{actions && <Actions>{actions}</Actions>}\n\n\t\t{showMenu && (\n\t\t\t<ContextMenu\n\t\t\t\tcontextMessage={contextMessage}\n\t\t\t\tcontextActions={contextActions}\n\t\t\t\tcontextComponent={contextComponent}\n\t\t\t\tdirection={direction}\n\t\t\t\tselectedCount={selectedCount}\n\t\t\t/>\n\t\t)}\n\t</HeaderStyle>\n);\n\nexport default Header;\n", "import * as React from 'react';\nimport styled from 'styled-components';\n\nconst alignMap = {\n\tleft: 'flex-start',\n\tright: 'flex-end',\n\tcenter: 'center',\n};\n\ntype AlignItems = 'center' | 'left' | 'right';\n\nconst SubheaderWrapper = styled.header<{\n\talign: AlignItems;\n\t$wrapContent: boolean;\n}>`\n\tposition: relative;\n\tdisplay: flex;\n\tflex: 1 1 auto;\n\tbox-sizing: border-box;\n\talign-items: center;\n\tpadding: 4px 16px 4px 24px;\n\twidth: 100%;\n\tjustify-content: ${({ align }) => alignMap[align]};\n\tflex-wrap: ${({ $wrapContent }) => ($wrapContent ? 'wrap' : 'nowrap')};\n\t${({ theme }) => theme.subHeader.style}\n`;\n\ntype SubheaderProps = {\n\talign?: AlignItems;\n\twrapContent?: boolean;\n\tchildren?: React.ReactNode;\n};\n\nconst Subheader = ({ align = 'right', wrapContent = true, ...rest }: SubheaderProps): JSX.Element => (\n\t<SubheaderWrapper align={align} $wrapContent={wrapContent} {...rest} />\n);\n\nexport default Subheader;\n", "import styled from 'styled-components';\n\nconst Body = styled.div`\n\tdisplay: flex;\n\tflex-direction: column;\n`;\n\nexport default Body;\n", "import styled, { css } from 'styled-components';\n\n/* Hack when using layovers/menus that get clipped by overflow-x\n  when a table is responsive due to overflow-xy scroll spec stupidity.\n  Note: The parent element height must be set to 100%!\n  https://www.brunildo.org/test/Overflowxy2.html\n*/\n\nconst ResponsiveWrapper = styled.div<{\n\t$responsive: boolean;\n\t$fixedHeader?: boolean;\n\t$fixedHeaderScrollHeight?: string;\n}>`\n\tposition: relative;\n\twidth: 100%;\n\tborder-radius: inherit;\n\t${({ $responsive, $fixedHeader }) =>\n\t\t$responsive &&\n\t\tcss`\n\t\t\toverflow-x: auto;\n\n\t\t\t// hidden prevents vertical scrolling in firefox when fixedHeader is disabled\n\t\t\toverflow-y: ${$fixedHeader ? 'auto' : 'hidden'};\n\t\t\tmin-height: 0;\n\t\t`};\n\n\t${({ $fixedHeader = false, $fixedHeaderScrollHeight = '100vh' }) =>\n\t\t$fixedHeader &&\n\t\tcss`\n\t\t\tmax-height: ${$fixedHeaderScrollHeight};\n\t\t\t-webkit-overflow-scrolling: touch;\n\t\t`};\n\n\t${({ theme }) => theme.responsiveWrapper.style};\n`;\n\nexport default ResponsiveWrapper;\n", "import styled from 'styled-components';\n\nconst ProgressWrapper = styled.div`\n\tposition: relative;\n\tbox-sizing: border-box;\n\twidth: 100%;\n\theight: 100%;\n\t${props => props.theme.progress.style};\n`;\n\nexport default ProgressWrapper;\n", "import styled from 'styled-components';\n\nconst Wrapper = styled.div`\n\tposition: relative;\n\twidth: 100%;\n\t${({ theme }) => theme.tableWrapper.style};\n`;\n\nexport default Wrapper;\n", "import styled from 'styled-components';\nimport { CellBase } from './Cell';\n\nconst ColumnExpander = styled(CellBase)`\n\twhite-space: nowrap;\n\t${({ theme }) => theme.expanderCell.style};\n`;\n\nexport default ColumnExpander;\n", "import styled from 'styled-components';\n\nconst NoDataWrapper = styled.div`\n\tbox-sizing: border-box;\n\twidth: 100%;\n\theight: 100%;\n\t${({ theme }) => theme.noData.style};\n`;\n\nexport default NoDataWrapper;\n", "import React from 'react';\n\nconst DropdownIcon: React.FC = () => (\n\t<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\">\n\t\t<path d=\"M7 10l5 5 5-5z\" />\n\t\t<path d=\"M0 0h24v24H0z\" fill=\"none\" />\n\t</svg>\n);\n\nexport default DropdownIcon;\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport DropDownIcon from '../icons/Dropdown';\n\nconst SelectControl = styled.select`\n\tcursor: pointer;\n\theight: 24px;\n\tmax-width: 100%;\n\tuser-select: none;\n\tpadding-left: 8px;\n\tpadding-right: 24px;\n\tbox-sizing: content-box;\n\tfont-size: inherit;\n\tcolor: inherit;\n\tborder: none;\n\tbackground-color: transparent;\n\tappearance: none;\n\tdirection: ltr;\n\tflex-shrink: 0;\n\n\t&::-ms-expand {\n\t\tdisplay: none;\n\t}\n\n\t&:disabled::-ms-expand {\n\t\tbackground: #f60;\n\t}\n\n\toption {\n\t\tcolor: initial;\n\t}\n`;\n\nconst SelectWrapper = styled.div`\n\tposition: relative;\n\tflex-shrink: 0;\n\tfont-size: inherit;\n\tcolor: inherit;\n\tmargin-top: 1px;\n\n\tsvg {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tcolor: inherit;\n\t\tposition: absolute;\n\t\tfill: currentColor;\n\t\twidth: 24px;\n\t\theight: 24px;\n\t\tdisplay: inline-block;\n\t\tuser-select: none;\n\t\tpointer-events: none;\n\t}\n`;\n\ntype SelectProps = {\n\tonChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;\n\tdefaultValue: string | number;\n\tchildren: React.ReactNode;\n};\n\nconst Select = ({ defaultValue, onChange, ...rest }: SelectProps): JSX.Element => (\n\t<SelectWrapper>\n\t\t<SelectControl onChange={onChange} defaultValue={defaultValue} {...rest} />\n\t\t<DropDownIcon />\n\t</SelectWrapper>\n);\n\nexport default Select;\n", "// Credit: https://usehooks.com/useWindowSize/\nimport * as React from 'react';\n\ntype Hook = () => {\n\twidth: number | undefined;\n\theight: number | undefined;\n};\n\nconst useWindowSize: Hook = () => {\n\tconst isClient = typeof window === 'object';\n\n\tfunction getSize() {\n\t\treturn {\n\t\t\twidth: isClient ? window.innerWidth : undefined,\n\t\t\theight: isClient ? window.innerHeight : undefined,\n\t\t};\n\t}\n\n\tconst [windowSize, setWindowSize] = React.useState(getSize);\n\n\tReact.useEffect(() => {\n\t\tif (!isClient) {\n\t\t\treturn () => null;\n\t\t}\n\n\t\tfunction handleResize() {\n\t\t\tsetWindowSize(getSize());\n\t\t}\n\n\t\twindow.addEventListener('resize', handleResize);\n\t\treturn () => window.removeEventListener('resize', handleResize);\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, []);\n\n\treturn windowSize;\n};\n\nexport default useWindowSize;\n", "import React from 'react';\n\nconst FirstPage: React.FC = () => (\n\t<svg\n\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\twidth=\"24\"\n\t\theight=\"24\"\n\t\tviewBox=\"0 0 24 24\"\n\t\taria-hidden=\"true\"\n\t\trole=\"presentation\"\n\t>\n\t\t<path d=\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\" />\n\t\t<path fill=\"none\" d=\"M24 24H0V0h24v24z\" />\n\t</svg>\n);\n\nexport default FirstPage;\n", "import React from 'react';\n\nconst LastPage: React.FC = () => (\n\t<svg\n\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\twidth=\"24\"\n\t\theight=\"24\"\n\t\tviewBox=\"0 0 24 24\"\n\t\taria-hidden=\"true\"\n\t\trole=\"presentation\"\n\t>\n\t\t<path d=\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\" />\n\t\t<path fill=\"none\" d=\"M0 0h24v24H0V0z\" />\n\t</svg>\n);\n\nexport default LastPage;\n", "import React from 'react';\n\nconst Left: React.FC = () => (\n\t<svg\n\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\twidth=\"24\"\n\t\theight=\"24\"\n\t\tviewBox=\"0 0 24 24\"\n\t\taria-hidden=\"true\"\n\t\trole=\"presentation\"\n\t>\n\t\t<path d=\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\" />\n\t\t<path d=\"M0 0h24v24H0z\" fill=\"none\" />\n\t</svg>\n);\n\nexport default Left;\n", "import React from 'react';\n\nconst Right: React.FC = () => (\n\t<svg\n\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\twidth=\"24\"\n\t\theight=\"24\"\n\t\tviewBox=\"0 0 24 24\"\n\t\taria-hidden=\"true\"\n\t\trole=\"presentation\"\n\t>\n\t\t<path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\" />\n\t\t<path d=\"M0 0h24v24H0z\" fill=\"none\" />\n\t</svg>\n);\n\nexport default Right;\n", "import React from 'react';\n\nconst ExpanderCollapsedIcon: React.FC = () => (\n\t<svg fill=\"currentColor\" height=\"24\" viewBox=\"0 0 24 24\" width=\"24\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t<path d=\"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z\" />\n\t\t<path d=\"M0-.25h24v24H0z\" fill=\"none\" />\n\t</svg>\n);\n\nexport default ExpanderCollapsedIcon;\n", "import React from 'react';\n\nconst ExpanderExpandedIcon: React.FC = () => (\n\t<svg fill=\"currentColor\" height=\"24\" viewBox=\"0 0 24 24\" width=\"24\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t<path d=\"M7.41 7.84L12 12.42l4.59-4.58L18 9.25l-6 6-6-6z\" />\n\t\t<path d=\"M0-.75h24v24H0z\" fill=\"none\" />\n\t</svg>\n);\n\nexport default ExpanderExpandedIcon;\n", "import React from 'react';\nimport FirstPageIcon from '../icons/FirstPage';\nimport LastPageIcon from '../icons/LastPage';\nimport LeftIcon from '../icons/Left';\nimport RightIcon from '../icons/Right';\nimport ExpanderCollapsedIcon from '../icons/ExpanderCollapsedIcon';\nimport ExpanderExpandedIcon from '../icons/ExpanderExpandedIcon';\nimport { noop } from './util';\nimport { Alignment, Direction } from './constants';\n\nexport const defaultProps = {\n\tcolumns: [],\n\tdata: [],\n\ttitle: '',\n\tkeyField: 'id',\n\tselectableRows: false,\n\tselectableRowsHighlight: false,\n\tselectableRowsNoSelectAll: false,\n\tselectableRowSelected: null,\n\tselectableRowDisabled: null,\n\tselectableRowsComponent: 'input' as const,\n\tselectableRowsComponentProps: {},\n\tselectableRowsVisibleOnly: false,\n\tselectableRowsSingle: false,\n\tclearSelectedRows: false,\n\texpandableRows: false,\n\texpandableRowDisabled: null,\n\texpandableRowExpanded: null,\n\texpandOnRowClicked: false,\n\texpandableRowsHideExpander: false,\n\texpandOnRowDoubleClicked: false,\n\texpandableInheritConditionalStyles: false,\n\texpandableRowsComponent: function DefaultExpander(): JSX.Element {\n\t\treturn (\n\t\t\t<div>\n\t\t\t\tTo add an expander pass in a component instance via <strong>expandableRowsComponent</strong>. You can then\n\t\t\t\taccess props.data from this component.\n\t\t\t</div>\n\t\t);\n\t},\n\texpandableIcon: {\n\t\tcollapsed: <ExpanderCollapsedIcon />,\n\t\texpanded: <ExpanderExpandedIcon />,\n\t},\n\texpandableRowsComponentProps: {},\n\tprogressPending: false,\n\tprogressComponent: <div style={{ fontSize: '24px', fontWeight: 700, padding: '24px' }}>Loading...</div>,\n\tpersistTableHead: false,\n\tsortIcon: null,\n\tsortFunction: null,\n\tsortServer: false,\n\tstriped: false,\n\thighlightOnHover: false,\n\tpointerOnHover: false,\n\tnoContextMenu: false,\n\tcontextMessage: { singular: 'item', plural: 'items', message: 'selected' },\n\tactions: null,\n\tcontextActions: null,\n\tcontextComponent: null,\n\tdefaultSortFieldId: null,\n\tdefaultSortAsc: true,\n\tresponsive: true,\n\tnoDataComponent: <div style={{ padding: '24px' }}>There are no records to display</div>,\n\tdisabled: false,\n\tnoTableHead: false,\n\tnoHeader: false,\n\tsubHeader: false,\n\tsubHeaderAlign: Alignment.RIGHT,\n\tsubHeaderWrap: true,\n\tsubHeaderComponent: null,\n\tfixedHeader: false,\n\tfixedHeaderScrollHeight: '100vh',\n\tpagination: false,\n\tpaginationServer: false,\n\tpaginationServerOptions: {\n\t\tpersistSelectedOnSort: false,\n\t\tpersistSelectedOnPageChange: false,\n\t},\n\tpaginationDefaultPage: 1,\n\tpaginationResetDefaultPage: false,\n\tpaginationTotalRows: 0,\n\tpaginationPerPage: 10,\n\tpaginationRowsPerPageOptions: [10, 15, 20, 25, 30],\n\tpaginationComponent: null,\n\tpaginationComponentOptions: {},\n\tpaginationIconFirstPage: <FirstPageIcon />,\n\tpaginationIconLastPage: <LastPageIcon />,\n\tpaginationIconNext: <RightIcon />,\n\tpaginationIconPrevious: <LeftIcon />,\n\tdense: false,\n\tconditionalRowStyles: [],\n\ttheme: 'default' as const,\n\tcustomStyles: {},\n\tdirection: Direction.AUTO,\n\tonChangePage: noop,\n\tonChangeRowsPerPage: noop,\n\tonRowClicked: noop,\n\tonRowDoubleClicked: noop,\n\tonRowMouseEnter: noop,\n\tonRowMouseLeave: noop,\n\tonRowExpandToggled: noop,\n\tonSelectedRowsChange: noop,\n\tonSort: noop,\n\tonColumnOrderChange: noop,\n};\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport Select from './Select';\nimport { getNumberOfPages } from './util';\nimport useWindowSize from '../hooks/useWindowSize';\nimport useRTL from '../hooks/useRTL';\nimport { media, SMALL } from './media';\nimport { Direction } from './constants';\nimport { PaginationOptions } from './types';\nimport { defaultProps } from './defaultProps';\n\nconst defaultComponentOptions = {\n\trowsPerPageText: 'Rows per page:',\n\trangeSeparatorText: 'of',\n\tnoRowsPerPage: false,\n\tselectAllRowsItem: false,\n\tselectAllRowsItemText: 'All',\n};\n\nconst PaginationWrapper = styled.nav`\n\tdisplay: flex;\n\tflex: 1 1 auto;\n\tjustify-content: flex-end;\n\talign-items: center;\n\tbox-sizing: border-box;\n\tpadding-right: 8px;\n\tpadding-left: 8px;\n\twidth: 100%;\n\t${({ theme }) => theme.pagination.style};\n`;\n\nconst Button = styled.button<{\n\t$isRTL: boolean;\n}>`\n\tposition: relative;\n\tdisplay: block;\n\tuser-select: none;\n\tborder: none;\n\t${({ theme }) => theme.pagination.pageButtonsStyle};\n\t${({ $isRTL }) => $isRTL && 'transform: scale(-1, -1)'};\n`;\n\nconst PageList = styled.div`\n\tdisplay: flex;\n\talign-items: center;\n\tborder-radius: 4px;\n\twhite-space: nowrap;\n\t${media.sm`\n    width: 100%;\n    justify-content: space-around;\n  `};\n`;\n\nconst Span = styled.span`\n\tflex-shrink: 1;\n\tuser-select: none;\n`;\n\nconst Range = styled(Span)`\n\tmargin: 0 24px;\n`;\n\nconst RowLabel = styled(Span)`\n\tmargin: 0 4px;\n`;\n\ninterface PaginationProps {\n\trowsPerPage: number;\n\trowCount: number;\n\tcurrentPage: number;\n\tdirection?: Direction;\n\tpaginationRowsPerPageOptions?: number[];\n\tpaginationIconLastPage?: React.ReactNode;\n\tpaginationIconFirstPage?: React.ReactNode;\n\tpaginationIconNext?: React.ReactNode;\n\tpaginationIconPrevious?: React.ReactNode;\n\tpaginationComponentOptions?: PaginationOptions;\n\tonChangePage: (page: number) => void;\n\tonChangeRowsPerPage: (numRows: number, currentPage: number) => void;\n}\n\nfunction Pagination({\n\trowsPerPage,\n\trowCount,\n\tcurrentPage,\n\tdirection = defaultProps.direction,\n\tpaginationRowsPerPageOptions = defaultProps.paginationRowsPerPageOptions,\n\tpaginationIconLastPage = defaultProps.paginationIconLastPage,\n\tpaginationIconFirstPage = defaultProps.paginationIconFirstPage,\n\tpaginationIconNext = defaultProps.paginationIconNext,\n\tpaginationIconPrevious = defaultProps.paginationIconPrevious,\n\tpaginationComponentOptions = defaultProps.paginationComponentOptions,\n\tonChangeRowsPerPage = defaultProps.onChangeRowsPerPage,\n\tonChangePage = defaultProps.onChangePage,\n}: PaginationProps): JSX.Element {\n\tconst windowSize = useWindowSize();\n\tconst isRTL = useRTL(direction);\n\tconst shouldShow = windowSize.width && windowSize.width > SMALL;\n\t// const isRTL = detectRTL(direction);\n\tconst numPages = getNumberOfPages(rowCount, rowsPerPage);\n\tconst lastIndex = currentPage * rowsPerPage;\n\tconst firstIndex = lastIndex - rowsPerPage + 1;\n\tconst disabledLesser = currentPage === 1;\n\tconst disabledGreater = currentPage === numPages;\n\tconst options = { ...defaultComponentOptions, ...paginationComponentOptions };\n\tconst range =\n\t\tcurrentPage === numPages\n\t\t\t? `${firstIndex}-${rowCount} ${options.rangeSeparatorText} ${rowCount}`\n\t\t\t: `${firstIndex}-${lastIndex} ${options.rangeSeparatorText} ${rowCount}`;\n\n\tconst handlePrevious = React.useCallback(() => onChangePage(currentPage - 1), [currentPage, onChangePage]);\n\tconst handleNext = React.useCallback(() => onChangePage(currentPage + 1), [currentPage, onChangePage]);\n\tconst handleFirst = React.useCallback(() => onChangePage(1), [onChangePage]);\n\tconst handleLast = React.useCallback(\n\t\t() => onChangePage(getNumberOfPages(rowCount, rowsPerPage)),\n\t\t[onChangePage, rowCount, rowsPerPage],\n\t);\n\tconst handleRowsPerPage = React.useCallback(\n\t\t(e: React.ChangeEvent<HTMLSelectElement>) => onChangeRowsPerPage(Number(e.target.value), currentPage),\n\t\t[currentPage, onChangeRowsPerPage],\n\t);\n\n\tconst selectOptions = paginationRowsPerPageOptions.map((num: number) => (\n\t\t<option key={num} value={num}>\n\t\t\t{num}\n\t\t</option>\n\t));\n\n\tif (options.selectAllRowsItem) {\n\t\tselectOptions.push(\n\t\t\t<option key={-1} value={rowCount}>\n\t\t\t\t{options.selectAllRowsItemText}\n\t\t\t</option>,\n\t\t);\n\t}\n\n\tconst select = (\n\t\t<Select onChange={handleRowsPerPage} defaultValue={rowsPerPage} aria-label={options.rowsPerPageText}>\n\t\t\t{selectOptions}\n\t\t</Select>\n\t);\n\n\treturn (\n\t\t<PaginationWrapper className=\"rdt_Pagination\">\n\t\t\t{!options.noRowsPerPage && shouldShow && (\n\t\t\t\t<>\n\t\t\t\t\t<RowLabel>{options.rowsPerPageText}</RowLabel>\n\t\t\t\t\t{select}\n\t\t\t\t</>\n\t\t\t)}\n\t\t\t{shouldShow && <Range>{range}</Range>}\n\t\t\t<PageList>\n\t\t\t\t<Button\n\t\t\t\t\tid=\"pagination-first-page\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\taria-label=\"First Page\"\n\t\t\t\t\taria-disabled={disabledLesser}\n\t\t\t\t\tonClick={handleFirst}\n\t\t\t\t\tdisabled={disabledLesser}\n\t\t\t\t\t$isRTL={isRTL}\n\t\t\t\t>\n\t\t\t\t\t{paginationIconFirstPage}\n\t\t\t\t</Button>\n\n\t\t\t\t<Button\n\t\t\t\t\tid=\"pagination-previous-page\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\taria-label=\"Previous Page\"\n\t\t\t\t\taria-disabled={disabledLesser}\n\t\t\t\t\tonClick={handlePrevious}\n\t\t\t\t\tdisabled={disabledLesser}\n\t\t\t\t\t$isRTL={isRTL}\n\t\t\t\t>\n\t\t\t\t\t{paginationIconPrevious}\n\t\t\t\t</Button>\n\n\t\t\t\t{!options.noRowsPerPage && !shouldShow && select}\n\n\t\t\t\t<Button\n\t\t\t\t\tid=\"pagination-next-page\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\taria-label=\"Next Page\"\n\t\t\t\t\taria-disabled={disabledGreater}\n\t\t\t\t\tonClick={handleNext}\n\t\t\t\t\tdisabled={disabledGreater}\n\t\t\t\t\t$isRTL={isRTL}\n\t\t\t\t>\n\t\t\t\t\t{paginationIconNext}\n\t\t\t\t</Button>\n\n\t\t\t\t<Button\n\t\t\t\t\tid=\"pagination-last-page\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\taria-label=\"Last Page\"\n\t\t\t\t\taria-disabled={disabledGreater}\n\t\t\t\t\tonClick={handleLast}\n\t\t\t\t\tdisabled={disabledGreater}\n\t\t\t\t\t$isRTL={isRTL}\n\t\t\t\t>\n\t\t\t\t\t{paginationIconLastPage}\n\t\t\t\t</Button>\n\t\t\t</PageList>\n\t\t</PaginationWrapper>\n\t);\n}\n\nexport default React.memo(Pagination);\n", "import * as React from 'react';\n\ntype Hook = (fn: () => void, inputs: unknown[]) => void;\n\nconst useFirstUpdate: Hook = (fn, inputs) => {\n\tconst firstUpdate = React.useRef(true);\n\n\tReact.useEffect(() => {\n\t\tif (firstUpdate.current) {\n\t\t\tfirstUpdate.current = false;\n\t\t\treturn;\n\t\t}\n\n\t\tfn();\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, inputs);\n};\n\nexport default useFirstUpdate;\n", "'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction getMergeFunction(key, options) {\n\tif (!options.customMerge) {\n\t\treturn deepmerge\n\t}\n\tvar customMerge = options.customMerge(key);\n\treturn typeof customMerge === 'function' ? customMerge : deepmerge\n}\n\nfunction getEnumerableOwnPropertySymbols(target) {\n\treturn Object.getOwnPropertySymbols\n\t\t? Object.getOwnPropertySymbols(target).filter(function(symbol) {\n\t\t\treturn Object.propertyIsEnumerable.call(target, symbol)\n\t\t})\n\t\t: []\n}\n\nfunction getKeys(target) {\n\treturn Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))\n}\n\nfunction propertyIsOnObject(object, property) {\n\ttry {\n\t\treturn property in object\n\t} catch(_) {\n\t\treturn false\n\t}\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n\treturn propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n\t\t&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n\t\t\t&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tgetKeys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tgetKeys(source).forEach(function(key) {\n\t\tif (propertyIsUnsafe(target, key)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n\t\t\tdestination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\t// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n\t// implementations can use it. The caller may not replace it.\n\toptions.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n", "import merge from 'deepmerge';\nimport { Theme, Themes } from './types';\n\ntype ThemeMapping = {\n\t[propertyName: string]: Theme;\n};\n\nconst defaultTheme = {\n\ttext: {\n\t\tprimary: 'rgba(0, 0, 0, 0.87)',\n\t\tsecondary: 'rgba(0, 0, 0, 0.54)',\n\t\tdisabled: 'rgba(0, 0, 0, 0.38)',\n\t},\n\tbackground: {\n\t\tdefault: '#FFFFFF',\n\t},\n\tcontext: {\n\t\tbackground: '#e3f2fd',\n\t\ttext: 'rgba(0, 0, 0, 0.87)',\n\t},\n\tdivider: {\n\t\tdefault: 'rgba(0,0,0,.12)',\n\t},\n\tbutton: {\n\t\tdefault: 'rgba(0,0,0,.54)',\n\t\tfocus: 'rgba(0,0,0,.12)',\n\t\thover: 'rgba(0,0,0,.12)',\n\t\tdisabled: 'rgba(0, 0, 0, .18)',\n\t},\n\tselected: {\n\t\tdefault: '#e3f2fd',\n\t\ttext: 'rgba(0, 0, 0, 0.87)',\n\t},\n\thighlightOnHover: {\n\t\tdefault: '#EEEEEE',\n\t\ttext: 'rgba(0, 0, 0, 0.87)',\n\t},\n\tstriped: {\n\t\tdefault: '#FAFAFA',\n\t\ttext: 'rgba(0, 0, 0, 0.87)',\n\t},\n};\n\nexport const defaultThemes: ThemeMapping = {\n\tdefault: defaultTheme,\n\tlight: defaultTheme,\n\tdark: {\n\t\ttext: {\n\t\t\tprimary: '#FFFFFF',\n\t\t\tsecondary: 'rgba(255, 255, 255, 0.7)',\n\t\t\tdisabled: 'rgba(0,0,0,.12)',\n\t\t},\n\t\tbackground: {\n\t\t\tdefault: '#424242',\n\t\t},\n\t\tcontext: {\n\t\t\tbackground: '#E91E63',\n\t\t\ttext: '#FFFFFF',\n\t\t},\n\t\tdivider: {\n\t\t\tdefault: 'rgba(81, 81, 81, 1)',\n\t\t},\n\t\tbutton: {\n\t\t\tdefault: '#FFFFFF',\n\t\t\tfocus: 'rgba(255, 255, 255, .54)',\n\t\t\thover: 'rgba(255, 255, 255, .12)',\n\t\t\tdisabled: 'rgba(255, 255, 255, .18)',\n\t\t},\n\t\tselected: {\n\t\t\tdefault: 'rgba(0, 0, 0, .7)',\n\t\t\ttext: '#FFFFFF',\n\t\t},\n\t\thighlightOnHover: {\n\t\t\tdefault: 'rgba(0, 0, 0, .7)',\n\t\t\ttext: '#FFFFFF',\n\t\t},\n\t\tstriped: {\n\t\t\tdefault: 'rgba(0, 0, 0, .87)',\n\t\t\ttext: '#FFFFFF',\n\t\t},\n\t},\n};\n\nexport function createTheme<T>(name = 'default', customTheme?: T, inherit: Themes = 'default'): Theme {\n\tif (!defaultThemes[name]) {\n\t\tdefaultThemes[name] = merge(defaultThemes[inherit], customTheme || {});\n\t}\n\n\t// allow tweaking default or light themes if the theme passed in matches\n\tdefaultThemes[name] = merge(defaultThemes[name], customTheme || {});\n\n\treturn defaultThemes[name];\n}\n", "import merge from 'deepmerge';\nimport { defaultThemes } from './themes';\nimport { TableStyles, Theme, Themes } from './types';\n\nexport const defaultStyles = (theme: Theme): TableStyles => ({\n\ttable: {\n\t\tstyle: {\n\t\t\tcolor: theme.text.primary,\n\t\t\tbackgroundColor: theme.background.default,\n\t\t},\n\t},\n\ttableWrapper: {\n\t\tstyle: {\n\t\t\tdisplay: 'table',\n\t\t},\n\t},\n\tresponsiveWrapper: {\n\t\tstyle: {},\n\t},\n\theader: {\n\t\tstyle: {\n\t\t\tfontSize: '22px',\n\t\t\tcolor: theme.text.primary,\n\t\t\tbackgroundColor: theme.background.default,\n\t\t\tminHeight: '56px',\n\t\t\tpaddingLeft: '16px',\n\t\t\tpaddingRight: '8px',\n\t\t},\n\t},\n\tsubHeader: {\n\t\tstyle: {\n\t\t\tbackgroundColor: theme.background.default,\n\t\t\tminHeight: '52px',\n\t\t},\n\t},\n\thead: {\n\t\tstyle: {\n\t\t\tcolor: theme.text.primary,\n\t\t\tfontSize: '12px',\n\t\t\tfontWeight: 500,\n\t\t},\n\t},\n\theadRow: {\n\t\tstyle: {\n\t\t\tbackgroundColor: theme.background.default,\n\t\t\tminHeight: '52px',\n\t\t\tborderBottomWidth: '1px',\n\t\t\tborderBottomColor: theme.divider.default,\n\t\t\tborderBottomStyle: 'solid',\n\t\t},\n\t\tdenseStyle: {\n\t\t\tminHeight: '32px',\n\t\t},\n\t},\n\theadCells: {\n\t\tstyle: {\n\t\t\tpaddingLeft: '16px',\n\t\t\tpaddingRight: '16px',\n\t\t},\n\t\tdraggingStyle: {\n\t\t\tcursor: 'move',\n\t\t},\n\t},\n\tcontextMenu: {\n\t\tstyle: {\n\t\t\tbackgroundColor: theme.context.background,\n\t\t\tfontSize: '18px',\n\t\t\tfontWeight: 400,\n\t\t\tcolor: theme.context.text,\n\t\t\tpaddingLeft: '16px',\n\t\t\tpaddingRight: '8px',\n\t\t\ttransform: 'translate3d(0, -100%, 0)',\n\t\t\ttransitionDuration: '125ms',\n\t\t\ttransitionTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)',\n\t\t\twillChange: 'transform',\n\t\t},\n\t\tactiveStyle: {\n\t\t\ttransform: 'translate3d(0, 0, 0)',\n\t\t},\n\t},\n\tcells: {\n\t\tstyle: {\n\t\t\tpaddingLeft: '16px',\n\t\t\tpaddingRight: '16px',\n\t\t\twordBreak: 'break-word',\n\t\t},\n\t\tdraggingStyle: {},\n\t},\n\trows: {\n\t\tstyle: {\n\t\t\tfontSize: '13px',\n\t\t\tfontWeight: 400,\n\t\t\tcolor: theme.text.primary,\n\t\t\tbackgroundColor: theme.background.default,\n\t\t\tminHeight: '48px',\n\t\t\t'&:not(:last-of-type)': {\n\t\t\t\tborderBottomStyle: 'solid',\n\t\t\t\tborderBottomWidth: '1px',\n\t\t\t\tborderBottomColor: theme.divider.default,\n\t\t\t},\n\t\t},\n\t\tdenseStyle: {\n\t\t\tminHeight: '32px',\n\t\t},\n\t\tselectedHighlightStyle: {\n\t\t\t// use nth-of-type(n) to override other nth selectors\n\t\t\t'&:nth-of-type(n)': {\n\t\t\t\tcolor: theme.selected.text,\n\t\t\t\tbackgroundColor: theme.selected.default,\n\t\t\t\tborderBottomColor: theme.background.default,\n\t\t\t},\n\t\t},\n\t\thighlightOnHoverStyle: {\n\t\t\tcolor: theme.highlightOnHover.text,\n\t\t\tbackgroundColor: theme.highlightOnHover.default,\n\t\t\ttransitionDuration: '0.15s',\n\t\t\ttransitionProperty: 'background-color',\n\t\t\tborderBottomColor: theme.background.default,\n\t\t\toutlineStyle: 'solid',\n\t\t\toutlineWidth: '1px',\n\t\t\toutlineColor: theme.background.default,\n\t\t},\n\t\tstripedStyle: {\n\t\t\tcolor: theme.striped.text,\n\t\t\tbackgroundColor: theme.striped.default,\n\t\t},\n\t},\n\texpanderRow: {\n\t\tstyle: {\n\t\t\tcolor: theme.text.primary,\n\t\t\tbackgroundColor: theme.background.default,\n\t\t},\n\t},\n\texpanderCell: {\n\t\tstyle: {\n\t\t\tflex: '0 0 48px',\n\t\t},\n\t},\n\texpanderButton: {\n\t\tstyle: {\n\t\t\tcolor: theme.button.default,\n\t\t\tfill: theme.button.default,\n\t\t\tbackgroundColor: 'transparent',\n\t\t\tborderRadius: '2px',\n\t\t\ttransition: '0.25s',\n\t\t\theight: '100%',\n\t\t\twidth: '100%',\n\t\t\t'&:hover:enabled': {\n\t\t\t\tcursor: 'pointer',\n\t\t\t},\n\t\t\t'&:disabled': {\n\t\t\t\tcolor: theme.button.disabled,\n\t\t\t},\n\t\t\t'&:hover:not(:disabled)': {\n\t\t\t\tcursor: 'pointer',\n\t\t\t\tbackgroundColor: theme.button.hover,\n\t\t\t},\n\t\t\t'&:focus': {\n\t\t\t\toutline: 'none',\n\t\t\t\tbackgroundColor: theme.button.focus,\n\t\t\t},\n\t\t\tsvg: {\n\t\t\t\tmargin: 'auto',\n\t\t\t},\n\t\t},\n\t},\n\tpagination: {\n\t\tstyle: {\n\t\t\tcolor: theme.text.secondary,\n\t\t\tfontSize: '13px',\n\t\t\tminHeight: '56px',\n\t\t\tbackgroundColor: theme.background.default,\n\t\t\tborderTopStyle: 'solid',\n\t\t\tborderTopWidth: '1px',\n\t\t\tborderTopColor: theme.divider.default,\n\t\t},\n\t\tpageButtonsStyle: {\n\t\t\tborderRadius: '50%',\n\t\t\theight: '40px',\n\t\t\twidth: '40px',\n\t\t\tpadding: '8px',\n\t\t\tmargin: 'px',\n\t\t\tcursor: 'pointer',\n\t\t\ttransition: '0.4s',\n\t\t\tcolor: theme.button.default,\n\t\t\tfill: theme.button.default,\n\t\t\tbackgroundColor: 'transparent',\n\t\t\t'&:disabled': {\n\t\t\t\tcursor: 'unset',\n\t\t\t\tcolor: theme.button.disabled,\n\t\t\t\tfill: theme.button.disabled,\n\t\t\t},\n\t\t\t'&:hover:not(:disabled)': {\n\t\t\t\tbackgroundColor: theme.button.hover,\n\t\t\t},\n\t\t\t'&:focus': {\n\t\t\t\toutline: 'none',\n\t\t\t\tbackgroundColor: theme.button.focus,\n\t\t\t},\n\t\t},\n\t},\n\tnoData: {\n\t\tstyle: {\n\t\t\tdisplay: 'flex',\n\t\t\talignItems: 'center',\n\t\t\tjustifyContent: 'center',\n\t\t\tcolor: theme.text.primary,\n\t\t\tbackgroundColor: theme.background.default,\n\t\t},\n\t},\n\tprogress: {\n\t\tstyle: {\n\t\t\tdisplay: 'flex',\n\t\t\talignItems: 'center',\n\t\t\tjustifyContent: 'center',\n\t\t\tcolor: theme.text.primary,\n\t\t\tbackgroundColor: theme.background.default,\n\t\t},\n\t},\n});\n\nexport const createStyles = (\n\tcustomStyles: TableStyles = {},\n\tthemeName = 'default',\n\tinherit: Themes = 'default',\n): TableStyles => {\n\tconst themeType = defaultThemes[themeName] ? themeName : inherit;\n\n\treturn merge(defaultStyles(defaultThemes[themeType]), customStyles);\n};\n", "import * as React from 'react';\nimport { decorateColumns, findColumnIndexById, getSortDirection } from '../DataTable/util';\nimport useDidUpdateEffect from '../hooks/useDidUpdateEffect';\nimport { SortOrder, TableColumn } from '../DataTable/types';\n\ntype ColumnsHook<T> = {\n\ttableColumns: TableColumn<T>[];\n\tdraggingColumnId: string;\n\thandleDragStart: (e: React.DragEvent<HTMLDivElement>) => void;\n\thandleDragEnter: (e: React.DragEvent<HTMLDivElement>) => void;\n\thandleDragOver: (e: React.DragEvent<HTMLDivElement>) => void;\n\thandleDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;\n\thandleDragEnd: (e: React.DragEvent<HTMLDivElement>) => void;\n\tdefaultSortDirection: SortOrder;\n\tdefaultSortColumn: TableColumn<T>;\n};\n\nfunction useColumns<T>(\n\tcolumns: TableColumn<T>[],\n\tonColumnOrderChange: (nextOrder: TableColumn<T>[]) => void,\n\tdefaultSortFieldId: string | number | null | undefined,\n\tdefaultSortAsc: boolean,\n): ColumnsHook<T> {\n\tconst [tableColumns, setTableColumns] = React.useState<TableColumn<T>[]>(() => decorateColumns(columns));\n\tconst [draggingColumnId, setDraggingColumn] = React.useState('');\n\tconst sourceColumnId = React.useRef('');\n\n\tuseDidUpdateEffect(() => {\n\t\tsetTableColumns(decorateColumns(columns));\n\t}, [columns]);\n\n\tconst handleDragStart = React.useCallback(\n\t\t(e: React.DragEvent<HTMLDivElement>) => {\n\t\t\tconst { attributes } = e.target as HTMLDivElement;\n\t\t\tconst id = attributes.getNamedItem('data-column-id')?.value;\n\n\t\t\tif (id) {\n\t\t\t\tsourceColumnId.current = tableColumns[findColumnIndexById(tableColumns, id)]?.id?.toString() || '';\n\n\t\t\t\tsetDraggingColumn(sourceColumnId.current);\n\t\t\t}\n\t\t},\n\t\t[tableColumns],\n\t);\n\n\tconst handleDragEnter = React.useCallback(\n\t\t(e: React.DragEvent<HTMLDivElement>) => {\n\t\t\tconst { attributes } = e.target as HTMLDivElement;\n\t\t\tconst id = attributes.getNamedItem('data-column-id')?.value;\n\n\t\t\tif (id && sourceColumnId.current && id !== sourceColumnId.current) {\n\t\t\t\tconst selectedColIndex = findColumnIndexById(tableColumns, sourceColumnId.current);\n\t\t\t\tconst targetColIndex = findColumnIndexById(tableColumns, id);\n\t\t\t\tconst reorderedCols = [...tableColumns];\n\n\t\t\t\treorderedCols[selectedColIndex] = tableColumns[targetColIndex];\n\t\t\t\treorderedCols[targetColIndex] = tableColumns[selectedColIndex];\n\n\t\t\t\tsetTableColumns(reorderedCols);\n\n\t\t\t\tonColumnOrderChange(reorderedCols);\n\t\t\t}\n\t\t},\n\t\t[onColumnOrderChange, tableColumns],\n\t);\n\n\tconst handleDragOver = React.useCallback((e: React.DragEvent<HTMLDivElement>) => {\n\t\te.preventDefault();\n\t}, []);\n\n\tconst handleDragLeave = React.useCallback((e: React.DragEvent<HTMLDivElement>) => {\n\t\te.preventDefault();\n\t}, []);\n\n\tconst handleDragEnd = React.useCallback((e: React.DragEvent<HTMLDivElement>) => {\n\t\te.preventDefault();\n\n\t\tsourceColumnId.current = '';\n\n\t\tsetDraggingColumn('');\n\t}, []);\n\n\tconst defaultSortDirection = getSortDirection(defaultSortAsc);\n\tconst defaultSortColumn = React.useMemo(\n\t\t() => tableColumns[findColumnIndexById(tableColumns, defaultSortFieldId?.toString())] || {},\n\t\t[defaultSortFieldId, tableColumns],\n\t);\n\n\treturn {\n\t\ttableColumns,\n\t\tdraggingColumnId,\n\t\thandleDragStart,\n\t\thandleDragEnter,\n\t\thandleDragOver,\n\t\thandleDragLeave,\n\t\thandleDragEnd,\n\t\tdefaultSortDirection,\n\t\tdefaultSortColumn,\n\t};\n}\n\nexport default useColumns;\n", "import * as React from 'react';\nimport { ThemeProvider } from 'styled-components';\nimport { tableReducer } from './tableReducer';\nimport Table from './Table';\nimport Head from './TableHead';\nimport HeadRow from './TableHeadRow';\nimport Row from './TableRow';\nimport Column from './TableCol';\nimport ColumnCheckbox from './TableColCheckbox';\nimport Header from './TableHeader';\nimport Subheader from './TableSubheader';\nimport Body from './TableBody';\nimport ResponsiveWrapper from './ResponsiveWrapper';\nimport ProgressWrapper from './ProgressWrapper';\nimport Wrapper from './TableWrapper';\nimport ColumnExpander from './TableColExpander';\nimport { CellBase } from './Cell';\nimport NoData from './NoDataWrapper';\nimport NativePagination from './Pagination';\nimport useDidUpdateEffect from '../hooks/useDidUpdateEffect';\nimport { prop, getNumberOfPages, sort, isEmpty, isRowSelected, recalculatePage } from './util';\nimport { defaultProps } from './defaultProps';\nimport { createStyles } from './styles';\nimport {\n\tAction,\n\tAllRowsAction,\n\tSingleRowAction,\n\tTableRow,\n\tSortAction,\n\tTableProps,\n\tTableState,\n\tSortOrder,\n} from './types';\nimport useColumns from '../hooks/useColumns';\n\nfunction DataTable<T>(props: TableProps<T>): JSX.Element {\n\tconst {\n\t\tdata = defaultProps.data,\n\t\tcolumns = defaultProps.columns,\n\t\ttitle = defaultProps.title,\n\t\tactions = defaultProps.actions,\n\t\tkeyField = defaultProps.keyField,\n\t\tstriped = defaultProps.striped,\n\t\thighlightOnHover = defaultProps.highlightOnHover,\n\t\tpointerOnHover = defaultProps.pointerOnHover,\n\t\tdense = defaultProps.dense,\n\t\tselectableRows = defaultProps.selectableRows,\n\t\tselectableRowsSingle = defaultProps.selectableRowsSingle,\n\t\tselectableRowsHighlight = defaultProps.selectableRowsHighlight,\n\t\tselectableRowsNoSelectAll = defaultProps.selectableRowsNoSelectAll,\n\t\tselectableRowsVisibleOnly = defaultProps.selectableRowsVisibleOnly,\n\t\tselectableRowSelected = defaultProps.selectableRowSelected,\n\t\tselectableRowDisabled = defaultProps.selectableRowDisabled,\n\t\tselectableRowsComponent = defaultProps.selectableRowsComponent,\n\t\tselectableRowsComponentProps = defaultProps.selectableRowsComponentProps,\n\t\tonRowExpandToggled = defaultProps.onRowExpandToggled,\n\t\tonSelectedRowsChange = defaultProps.onSelectedRowsChange,\n\t\texpandableIcon = defaultProps.expandableIcon,\n\t\tonChangeRowsPerPage = defaultProps.onChangeRowsPerPage,\n\t\tonChangePage = defaultProps.onChangePage,\n\t\tpaginationServer = defaultProps.paginationServer,\n\t\tpaginationServerOptions = defaultProps.paginationServerOptions,\n\t\tpaginationTotalRows = defaultProps.paginationTotalRows,\n\t\tpaginationDefaultPage = defaultProps.paginationDefaultPage,\n\t\tpaginationResetDefaultPage = defaultProps.paginationResetDefaultPage,\n\t\tpaginationPerPage = defaultProps.paginationPerPage,\n\t\tpaginationRowsPerPageOptions = defaultProps.paginationRowsPerPageOptions,\n\t\tpaginationIconLastPage = defaultProps.paginationIconLastPage,\n\t\tpaginationIconFirstPage = defaultProps.paginationIconFirstPage,\n\t\tpaginationIconNext = defaultProps.paginationIconNext,\n\t\tpaginationIconPrevious = defaultProps.paginationIconPrevious,\n\t\tpaginationComponent = defaultProps.paginationComponent,\n\t\tpaginationComponentOptions = defaultProps.paginationComponentOptions,\n\t\tresponsive = defaultProps.responsive,\n\t\tprogressPending = defaultProps.progressPending,\n\t\tprogressComponent = defaultProps.progressComponent,\n\t\tpersistTableHead = defaultProps.persistTableHead,\n\t\tnoDataComponent = defaultProps.noDataComponent,\n\t\tdisabled = defaultProps.disabled,\n\t\tnoTableHead = defaultProps.noTableHead,\n\t\tnoHeader = defaultProps.noHeader,\n\t\tfixedHeader = defaultProps.fixedHeader,\n\t\tfixedHeaderScrollHeight = defaultProps.fixedHeaderScrollHeight,\n\t\tpagination = defaultProps.pagination,\n\t\tsubHeader = defaultProps.subHeader,\n\t\tsubHeaderAlign = defaultProps.subHeaderAlign,\n\t\tsubHeaderWrap = defaultProps.subHeaderWrap,\n\t\tsubHeaderComponent = defaultProps.subHeaderComponent,\n\t\tnoContextMenu = defaultProps.noContextMenu,\n\t\tcontextMessage = defaultProps.contextMessage,\n\t\tcontextActions = defaultProps.contextActions,\n\t\tcontextComponent = defaultProps.contextComponent,\n\t\texpandableRows = defaultProps.expandableRows,\n\t\tonRowClicked = defaultProps.onRowClicked,\n\t\tonRowDoubleClicked = defaultProps.onRowDoubleClicked,\n\t\tonRowMouseEnter = defaultProps.onRowMouseEnter,\n\t\tonRowMouseLeave = defaultProps.onRowMouseLeave,\n\t\tsortIcon = defaultProps.sortIcon,\n\t\tonSort = defaultProps.onSort,\n\t\tsortFunction = defaultProps.sortFunction,\n\t\tsortServer = defaultProps.sortServer,\n\t\texpandableRowsComponent = defaultProps.expandableRowsComponent,\n\t\texpandableRowsComponentProps = defaultProps.expandableRowsComponentProps,\n\t\texpandableRowDisabled = defaultProps.expandableRowDisabled,\n\t\texpandableRowsHideExpander = defaultProps.expandableRowsHideExpander,\n\t\texpandOnRowClicked = defaultProps.expandOnRowClicked,\n\t\texpandOnRowDoubleClicked = defaultProps.expandOnRowDoubleClicked,\n\t\texpandableRowExpanded = defaultProps.expandableRowExpanded,\n\t\texpandableInheritConditionalStyles = defaultProps.expandableInheritConditionalStyles,\n\t\tdefaultSortFieldId = defaultProps.defaultSortFieldId,\n\t\tdefaultSortAsc = defaultProps.defaultSortAsc,\n\t\tclearSelectedRows = defaultProps.clearSelectedRows,\n\t\tconditionalRowStyles = defaultProps.conditionalRowStyles,\n\t\ttheme = defaultProps.theme,\n\t\tcustomStyles = defaultProps.customStyles,\n\t\tdirection = defaultProps.direction,\n\t\tonColumnOrderChange = defaultProps.onColumnOrderChange,\n\t\tclassName,\n\t\tariaLabel,\n\t} = props;\n\n\tconst {\n\t\ttableColumns,\n\t\tdraggingColumnId,\n\t\thandleDragStart,\n\t\thandleDragEnter,\n\t\thandleDragOver,\n\t\thandleDragLeave,\n\t\thandleDragEnd,\n\t\tdefaultSortDirection,\n\t\tdefaultSortColumn,\n\t} = useColumns(columns, onColumnOrderChange, defaultSortFieldId, defaultSortAsc);\n\n\tconst [\n\t\t{\n\t\t\trowsPerPage,\n\t\t\tcurrentPage,\n\t\t\tselectedRows,\n\t\t\tallSelected,\n\t\t\tselectedCount,\n\t\t\tselectedColumn,\n\t\t\tsortDirection,\n\t\t\ttoggleOnSelectedRowsChange,\n\t\t},\n\t\tdispatch,\n\t] = React.useReducer<React.Reducer<TableState<T>, Action<T>>>(tableReducer, {\n\t\tallSelected: false,\n\t\tselectedCount: 0,\n\t\tselectedRows: [],\n\t\tselectedColumn: defaultSortColumn,\n\t\ttoggleOnSelectedRowsChange: false,\n\t\tsortDirection: defaultSortDirection,\n\t\tcurrentPage: paginationDefaultPage,\n\t\trowsPerPage: paginationPerPage,\n\t\tselectedRowsFlag: false,\n\t\tcontextMessage: defaultProps.contextMessage,\n\t});\n\n\tconst { persistSelectedOnSort = false, persistSelectedOnPageChange = false } = paginationServerOptions;\n\tconst mergeSelections = !!(paginationServer && (persistSelectedOnPageChange || persistSelectedOnSort));\n\tconst enabledPagination = pagination && !progressPending && data.length > 0;\n\tconst Pagination = paginationComponent || NativePagination;\n\n\tconst currentTheme = React.useMemo(() => createStyles(customStyles, theme), [customStyles, theme]);\n\tconst wrapperProps = React.useMemo(() => ({ ...(direction !== 'auto' && { dir: direction }) }), [direction]);\n\n\tconst sortedData = React.useMemo(() => {\n\t\t// server-side sorting bypasses internal sorting\n\t\tif (sortServer) {\n\t\t\treturn data;\n\t\t}\n\n\t\tif (selectedColumn?.sortFunction && typeof selectedColumn.sortFunction === 'function') {\n\t\t\tconst sortFn = selectedColumn.sortFunction;\n\t\t\tconst customSortFunction = sortDirection === SortOrder.ASC ? sortFn : (a: T, b: T) => sortFn(a, b) * -1;\n\n\t\t\treturn [...data].sort(customSortFunction);\n\t\t}\n\n\t\treturn sort(data, selectedColumn?.selector, sortDirection, sortFunction);\n\t}, [sortServer, selectedColumn, sortDirection, data, sortFunction]);\n\n\tconst tableRows = React.useMemo(() => {\n\t\tif (pagination && !paginationServer) {\n\t\t\t// when using client-side pagination we can just slice the rows set\n\t\t\tconst lastIndex = currentPage * rowsPerPage;\n\t\t\tconst firstIndex = lastIndex - rowsPerPage;\n\n\t\t\treturn sortedData.slice(firstIndex, lastIndex);\n\t\t}\n\n\t\treturn sortedData;\n\t}, [currentPage, pagination, paginationServer, rowsPerPage, sortedData]);\n\n\tconst handleSort = React.useCallback((action: SortAction<T>) => {\n\t\tdispatch(action);\n\t}, []);\n\n\tconst handleSelectAllRows = React.useCallback((action: AllRowsAction<T>) => {\n\t\tdispatch(action);\n\t}, []);\n\n\tconst handleSelectedRow = React.useCallback((action: SingleRowAction<T>) => {\n\t\tdispatch(action);\n\t}, []);\n\n\tconst handleRowClicked = React.useCallback(\n\t\t(row: T, e: React.MouseEvent<Element, MouseEvent>) => onRowClicked(row, e),\n\t\t[onRowClicked],\n\t);\n\n\tconst handleRowDoubleClicked = React.useCallback(\n\t\t(row: T, e: React.MouseEvent<Element, MouseEvent>) => onRowDoubleClicked(row, e),\n\t\t[onRowDoubleClicked],\n\t);\n\n\tconst handleRowMouseEnter = React.useCallback(\n\t\t(row: T, e: React.MouseEvent<Element, MouseEvent>) => onRowMouseEnter(row, e),\n\t\t[onRowMouseEnter],\n\t);\n\n\tconst handleRowMouseLeave = React.useCallback(\n\t\t(row: T, e: React.MouseEvent<Element, MouseEvent>) => onRowMouseLeave(row, e),\n\t\t[onRowMouseLeave],\n\t);\n\n\tconst handleChangePage = React.useCallback(\n\t\t(page: number) =>\n\t\t\tdispatch({\n\t\t\t\ttype: 'CHANGE_PAGE',\n\t\t\t\tpage,\n\t\t\t\tpaginationServer,\n\t\t\t\tvisibleOnly: selectableRowsVisibleOnly,\n\t\t\t\tpersistSelectedOnPageChange,\n\t\t\t}),\n\t\t[paginationServer, persistSelectedOnPageChange, selectableRowsVisibleOnly],\n\t);\n\n\tconst handleChangeRowsPerPage = React.useCallback(\n\t\t(newRowsPerPage: number) => {\n\t\t\tconst rowCount = paginationTotalRows || tableRows.length;\n\t\t\tconst updatedPage = getNumberOfPages(rowCount, newRowsPerPage);\n\t\t\tconst recalculatedPage = recalculatePage(currentPage, updatedPage);\n\n\t\t\t// update the currentPage for client-side pagination\n\t\t\t// server - side should be handled by onChangeRowsPerPage\n\t\t\tif (!paginationServer) {\n\t\t\t\thandleChangePage(recalculatedPage);\n\t\t\t}\n\n\t\t\tdispatch({ type: 'CHANGE_ROWS_PER_PAGE', page: recalculatedPage, rowsPerPage: newRowsPerPage });\n\t\t},\n\t\t[currentPage, handleChangePage, paginationServer, paginationTotalRows, tableRows.length],\n\t);\n\n\tconst showTableHead = () => {\n\t\tif (noTableHead) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (persistTableHead) {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn sortedData.length > 0 && !progressPending;\n\t};\n\n\tconst showHeader = () => {\n\t\tif (noHeader) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (title) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (actions) {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t};\n\n\t// recalculate the pagination and currentPage if the rows length changes\n\tif (pagination && !paginationServer && sortedData.length > 0 && tableRows.length === 0) {\n\t\tconst updatedPage = getNumberOfPages(sortedData.length, rowsPerPage);\n\t\tconst recalculatedPage = recalculatePage(currentPage, updatedPage);\n\n\t\thandleChangePage(recalculatedPage);\n\t}\n\n\tuseDidUpdateEffect(() => {\n\t\tonSelectedRowsChange({ allSelected, selectedCount, selectedRows: selectedRows.slice(0) });\n\t\t// onSelectedRowsChange trigger is controlled by toggleOnSelectedRowsChange state\n\t}, [toggleOnSelectedRowsChange]);\n\n\tuseDidUpdateEffect(() => {\n\t\tonSort(selectedColumn, sortDirection, sortedData.slice(0));\n\t\t// do not update on sortedData\n\t}, [selectedColumn, sortDirection]);\n\n\tuseDidUpdateEffect(() => {\n\t\tonChangePage(currentPage, paginationTotalRows || sortedData.length);\n\t}, [currentPage]);\n\n\tuseDidUpdateEffect(() => {\n\t\tonChangeRowsPerPage(rowsPerPage, currentPage);\n\t}, [rowsPerPage]);\n\n\tuseDidUpdateEffect(() => {\n\t\thandleChangePage(paginationDefaultPage);\n\t}, [paginationDefaultPage, paginationResetDefaultPage]);\n\n\tuseDidUpdateEffect(() => {\n\t\tif (pagination && paginationServer && paginationTotalRows > 0) {\n\t\t\tconst updatedPage = getNumberOfPages(paginationTotalRows, rowsPerPage);\n\t\t\tconst recalculatedPage = recalculatePage(currentPage, updatedPage);\n\n\t\t\tif (currentPage !== recalculatedPage) {\n\t\t\t\thandleChangePage(recalculatedPage);\n\t\t\t}\n\t\t}\n\t}, [paginationTotalRows]);\n\n\tReact.useEffect(() => {\n\t\tdispatch({ type: 'CLEAR_SELECTED_ROWS', selectedRowsFlag: clearSelectedRows });\n\t}, [selectableRowsSingle, clearSelectedRows]);\n\n\tReact.useEffect(() => {\n\t\tif (!selectableRowSelected) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst preSelectedRows = sortedData.filter(row => selectableRowSelected(row));\n\t\t// if selectableRowsSingle mode then return the first match\n\t\tconst selected = selectableRowsSingle ? preSelectedRows.slice(0, 1) : preSelectedRows;\n\n\t\tdispatch({\n\t\t\ttype: 'SELECT_MULTIPLE_ROWS',\n\t\t\tkeyField,\n\t\t\tselectedRows: selected,\n\t\t\ttotalRows: sortedData.length,\n\t\t\tmergeSelections,\n\t\t});\n\n\t\t// We only want to update the selectedRowState if data changes\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, [data, selectableRowSelected]);\n\n\tconst visibleRows = selectableRowsVisibleOnly ? tableRows : sortedData;\n\tconst showSelectAll = persistSelectedOnPageChange || selectableRowsSingle || selectableRowsNoSelectAll;\n\n\treturn (\n\t\t<ThemeProvider theme={currentTheme}>\n\t\t\t{showHeader() && (\n\t\t\t\t<Header\n\t\t\t\t\ttitle={title}\n\t\t\t\t\tactions={actions}\n\t\t\t\t\tshowMenu={!noContextMenu}\n\t\t\t\t\tselectedCount={selectedCount}\n\t\t\t\t\tdirection={direction}\n\t\t\t\t\tcontextActions={contextActions}\n\t\t\t\t\tcontextComponent={contextComponent}\n\t\t\t\t\tcontextMessage={contextMessage}\n\t\t\t\t/>\n\t\t\t)}\n\n\t\t\t{subHeader && (\n\t\t\t\t<Subheader align={subHeaderAlign} wrapContent={subHeaderWrap}>\n\t\t\t\t\t{subHeaderComponent}\n\t\t\t\t</Subheader>\n\t\t\t)}\n\n\t\t\t<ResponsiveWrapper\n\t\t\t\t$responsive={responsive}\n\t\t\t\t$fixedHeader={fixedHeader}\n\t\t\t\t$fixedHeaderScrollHeight={fixedHeaderScrollHeight}\n\t\t\t\tclassName={className}\n\t\t\t\t{...wrapperProps}\n\t\t\t>\n\t\t\t\t<Wrapper>\n\t\t\t\t\t{progressPending && !persistTableHead && <ProgressWrapper>{progressComponent}</ProgressWrapper>}\n\n\t\t\t\t\t<Table disabled={disabled} className=\"rdt_Table\" role=\"table\" {...(ariaLabel && { 'aria-label': ariaLabel })}>\n\t\t\t\t\t\t{showTableHead() && (\n\t\t\t\t\t\t\t<Head className=\"rdt_TableHead\" role=\"rowgroup\" $fixedHeader={fixedHeader}>\n\t\t\t\t\t\t\t\t<HeadRow className=\"rdt_TableHeadRow\" role=\"row\" $dense={dense}>\n\t\t\t\t\t\t\t\t\t{selectableRows &&\n\t\t\t\t\t\t\t\t\t\t(showSelectAll ? (\n\t\t\t\t\t\t\t\t\t\t\t<CellBase style={{ flex: '0 0 48px' }} />\n\t\t\t\t\t\t\t\t\t\t) : (\n\t\t\t\t\t\t\t\t\t\t\t<ColumnCheckbox\n\t\t\t\t\t\t\t\t\t\t\t\tallSelected={allSelected}\n\t\t\t\t\t\t\t\t\t\t\t\tselectedRows={selectedRows}\n\t\t\t\t\t\t\t\t\t\t\t\tselectableRowsComponent={selectableRowsComponent}\n\t\t\t\t\t\t\t\t\t\t\t\tselectableRowsComponentProps={selectableRowsComponentProps}\n\t\t\t\t\t\t\t\t\t\t\t\tselectableRowDisabled={selectableRowDisabled}\n\t\t\t\t\t\t\t\t\t\t\t\trowData={visibleRows}\n\t\t\t\t\t\t\t\t\t\t\t\tkeyField={keyField}\n\t\t\t\t\t\t\t\t\t\t\t\tmergeSelections={mergeSelections}\n\t\t\t\t\t\t\t\t\t\t\t\tonSelectAllRows={handleSelectAllRows}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t\t{expandableRows && !expandableRowsHideExpander && <ColumnExpander />}\n\t\t\t\t\t\t\t\t\t{tableColumns.map(column => (\n\t\t\t\t\t\t\t\t\t\t<Column\n\t\t\t\t\t\t\t\t\t\t\tkey={column.id}\n\t\t\t\t\t\t\t\t\t\t\tcolumn={column}\n\t\t\t\t\t\t\t\t\t\t\tselectedColumn={selectedColumn}\n\t\t\t\t\t\t\t\t\t\t\tdisabled={progressPending || sortedData.length === 0}\n\t\t\t\t\t\t\t\t\t\t\tpagination={pagination}\n\t\t\t\t\t\t\t\t\t\t\tpaginationServer={paginationServer}\n\t\t\t\t\t\t\t\t\t\t\tpersistSelectedOnSort={persistSelectedOnSort}\n\t\t\t\t\t\t\t\t\t\t\tselectableRowsVisibleOnly={selectableRowsVisibleOnly}\n\t\t\t\t\t\t\t\t\t\t\tsortDirection={sortDirection}\n\t\t\t\t\t\t\t\t\t\t\tsortIcon={sortIcon}\n\t\t\t\t\t\t\t\t\t\t\tsortServer={sortServer}\n\t\t\t\t\t\t\t\t\t\t\tonSort={handleSort}\n\t\t\t\t\t\t\t\t\t\t\tonDragStart={handleDragStart}\n\t\t\t\t\t\t\t\t\t\t\tonDragOver={handleDragOver}\n\t\t\t\t\t\t\t\t\t\t\tonDragEnd={handleDragEnd}\n\t\t\t\t\t\t\t\t\t\t\tonDragEnter={handleDragEnter}\n\t\t\t\t\t\t\t\t\t\t\tonDragLeave={handleDragLeave}\n\t\t\t\t\t\t\t\t\t\t\tdraggingColumnId={draggingColumnId}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t</HeadRow>\n\t\t\t\t\t\t\t</Head>\n\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t{!sortedData.length && !progressPending && <NoData>{noDataComponent}</NoData>}\n\n\t\t\t\t\t\t{progressPending && persistTableHead && <ProgressWrapper>{progressComponent}</ProgressWrapper>}\n\n\t\t\t\t\t\t{!progressPending && sortedData.length > 0 && (\n\t\t\t\t\t\t\t<Body className=\"rdt_TableBody\" role=\"rowgroup\">\n\t\t\t\t\t\t\t\t{tableRows.map((row, i) => {\n\t\t\t\t\t\t\t\t\tconst key = prop(row as TableRow, keyField) as string | number;\n\t\t\t\t\t\t\t\t\tconst id = isEmpty(key) ? i : key;\n\t\t\t\t\t\t\t\t\tconst selected = isRowSelected(row, selectedRows, keyField);\n\t\t\t\t\t\t\t\t\tconst expanderExpander = !!(expandableRows && expandableRowExpanded && expandableRowExpanded(row));\n\t\t\t\t\t\t\t\t\tconst expanderDisabled = !!(expandableRows && expandableRowDisabled && expandableRowDisabled(row));\n\n\t\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\t\t<Row\n\t\t\t\t\t\t\t\t\t\t\tid={id}\n\t\t\t\t\t\t\t\t\t\t\tkey={id}\n\t\t\t\t\t\t\t\t\t\t\tkeyField={keyField}\n\t\t\t\t\t\t\t\t\t\t\tdata-row-id={id}\n\t\t\t\t\t\t\t\t\t\t\tcolumns={tableColumns}\n\t\t\t\t\t\t\t\t\t\t\trow={row}\n\t\t\t\t\t\t\t\t\t\t\trowCount={sortedData.length}\n\t\t\t\t\t\t\t\t\t\t\trowIndex={i}\n\t\t\t\t\t\t\t\t\t\t\tselectableRows={selectableRows}\n\t\t\t\t\t\t\t\t\t\t\texpandableRows={expandableRows}\n\t\t\t\t\t\t\t\t\t\t\texpandableIcon={expandableIcon}\n\t\t\t\t\t\t\t\t\t\t\thighlightOnHover={highlightOnHover}\n\t\t\t\t\t\t\t\t\t\t\tpointerOnHover={pointerOnHover}\n\t\t\t\t\t\t\t\t\t\t\tdense={dense}\n\t\t\t\t\t\t\t\t\t\t\texpandOnRowClicked={expandOnRowClicked}\n\t\t\t\t\t\t\t\t\t\t\texpandOnRowDoubleClicked={expandOnRowDoubleClicked}\n\t\t\t\t\t\t\t\t\t\t\texpandableRowsComponent={expandableRowsComponent}\n\t\t\t\t\t\t\t\t\t\t\texpandableRowsComponentProps={expandableRowsComponentProps}\n\t\t\t\t\t\t\t\t\t\t\texpandableRowsHideExpander={expandableRowsHideExpander}\n\t\t\t\t\t\t\t\t\t\t\tdefaultExpanderDisabled={expanderDisabled}\n\t\t\t\t\t\t\t\t\t\t\tdefaultExpanded={expanderExpander}\n\t\t\t\t\t\t\t\t\t\t\texpandableInheritConditionalStyles={expandableInheritConditionalStyles}\n\t\t\t\t\t\t\t\t\t\t\tconditionalRowStyles={conditionalRowStyles}\n\t\t\t\t\t\t\t\t\t\t\tselected={selected}\n\t\t\t\t\t\t\t\t\t\t\tselectableRowsHighlight={selectableRowsHighlight}\n\t\t\t\t\t\t\t\t\t\t\tselectableRowsComponent={selectableRowsComponent}\n\t\t\t\t\t\t\t\t\t\t\tselectableRowsComponentProps={selectableRowsComponentProps}\n\t\t\t\t\t\t\t\t\t\t\tselectableRowDisabled={selectableRowDisabled}\n\t\t\t\t\t\t\t\t\t\t\tselectableRowsSingle={selectableRowsSingle}\n\t\t\t\t\t\t\t\t\t\t\tstriped={striped}\n\t\t\t\t\t\t\t\t\t\t\tonRowExpandToggled={onRowExpandToggled}\n\t\t\t\t\t\t\t\t\t\t\tonRowClicked={handleRowClicked}\n\t\t\t\t\t\t\t\t\t\t\tonRowDoubleClicked={handleRowDoubleClicked}\n\t\t\t\t\t\t\t\t\t\t\tonRowMouseEnter={handleRowMouseEnter}\n\t\t\t\t\t\t\t\t\t\t\tonRowMouseLeave={handleRowMouseLeave}\n\t\t\t\t\t\t\t\t\t\t\tonSelectedRow={handleSelectedRow}\n\t\t\t\t\t\t\t\t\t\t\tdraggingColumnId={draggingColumnId}\n\t\t\t\t\t\t\t\t\t\t\tonDragStart={handleDragStart}\n\t\t\t\t\t\t\t\t\t\t\tonDragOver={handleDragOver}\n\t\t\t\t\t\t\t\t\t\t\tonDragEnd={handleDragEnd}\n\t\t\t\t\t\t\t\t\t\t\tonDragEnter={handleDragEnter}\n\t\t\t\t\t\t\t\t\t\t\tonDragLeave={handleDragLeave}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t})}\n\t\t\t\t\t\t\t</Body>\n\t\t\t\t\t\t)}\n\t\t\t\t\t</Table>\n\t\t\t\t</Wrapper>\n\t\t\t</ResponsiveWrapper>\n\n\t\t\t{enabledPagination && (\n\t\t\t\t<div>\n\t\t\t\t\t<Pagination\n\t\t\t\t\t\tonChangePage={handleChangePage}\n\t\t\t\t\t\tonChangeRowsPerPage={handleChangeRowsPerPage}\n\t\t\t\t\t\trowCount={paginationTotalRows || sortedData.length}\n\t\t\t\t\t\tcurrentPage={currentPage}\n\t\t\t\t\t\trowsPerPage={rowsPerPage}\n\t\t\t\t\t\tdirection={direction}\n\t\t\t\t\t\tpaginationRowsPerPageOptions={paginationRowsPerPageOptions}\n\t\t\t\t\t\tpaginationIconLastPage={paginationIconLastPage}\n\t\t\t\t\t\tpaginationIconFirstPage={paginationIconFirstPage}\n\t\t\t\t\t\tpaginationIconNext={paginationIconNext}\n\t\t\t\t\t\tpaginationIconPrevious={paginationIconPrevious}\n\t\t\t\t\t\tpaginationComponentOptions={paginationComponentOptions}\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t)}\n\t\t</ThemeProvider>\n\t);\n}\n\nexport default React.memo(DataTable) as typeof DataTable;\n"], "names": ["css", "styled", "React", "Checkbox", "Direction", "Alignment", "Media", "TableCellExpander", "ExpanderRow", "Title", "DropDownIcon", "FirstPageIcon", "LastPageIcon", "RightIcon", "LeftIcon", "useDidUpdateEffect", "ThemeProvider", "Table", "NoData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAY,SAGX,CAAA;AAHD,CAAA,UAAY,SAAS,EAAA;AACpB,IAAA,SAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,SAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACd,CAAC,EAHW,SAAS,KAAT,SAAS,GAGpB,EAAA,CAAA,CAAA;;ACHe,SAAA,IAAI,CAAuB,GAAM,EAAE,GAAM,EAAA;AACxD,IAAA,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;AACjB,CAAC;AAEe,SAAA,OAAO,CAAC,KAAA,GAAqC,EAAE,EAAA;AAC9D,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC9B,QAAA,OAAO,KAAK,CAAC;KACb;IAED,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;AACrC,CAAC;AAEK,SAAU,IAAI,CACnB,IAAS,EACT,QAAwC,EACxC,SAAoB,EACpB,MAA+B,EAAA;IAE/B,IAAI,CAAC,QAAQ,EAAE;AACd,QAAA,OAAO,IAAI,CAAC;KACZ;AAED,IAAA,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;AAE3C,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;KAClD;AAED,IAAA,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAI,EAAE,CAAI,KAAI;AACxC,QAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC3B,QAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAE3B,QAAA,IAAI,SAAS,KAAK,KAAK,EAAE;AACxB,YAAA,IAAI,MAAM,GAAG,MAAM,EAAE;gBACpB,OAAO,CAAC,CAAC,CAAC;aACV;AAED,YAAA,IAAI,MAAM,GAAG,MAAM,EAAE;AACpB,gBAAA,OAAO,CAAC,CAAC;aACT;SACD;AAED,QAAA,IAAI,SAAS,KAAK,MAAM,EAAE;AACzB,YAAA,IAAI,MAAM,GAAG,MAAM,EAAE;gBACpB,OAAO,CAAC,CAAC,CAAC;aACV;AAED,YAAA,IAAI,MAAM,GAAG,MAAM,EAAE;AACpB,gBAAA,OAAO,CAAC,CAAC;aACT;SACD;AAED,QAAA,OAAO,CAAC,CAAC;AACV,KAAC,CAAC,CAAC;AACJ,CAAC;AAEK,SAAU,WAAW,CAC1B,GAAM,EAEN,QAAwC,EACxC,MAAoC,EACpC,QAAgB,EAAA;IAEhB,IAAI,CAAC,QAAQ,EAAE;AACd,QAAA,OAAO,IAAI,CAAC;KACZ;AAGD,IAAA,IAAI,MAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;AAC3C,QAAA,OAAO,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;KAC7B;AAED,IAAA,OAAO,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AAChC,CAAC;AAEK,SAAU,UAAU,CAAI,KAAa,GAAA,EAAE,EAAE,IAAO,EAAE,KAAK,GAAG,CAAC,EAAA;IAChE,OAAO,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AAChE,CAAC;AAEK,SAAU,UAAU,CAAI,KAAa,GAAA,EAAE,EAAE,IAAO,EAAE,QAAQ,GAAG,IAAI,EAAA;AACtE,IAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;IAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAgB,EAAE,QAAQ,CAAC,CAAC;IAEpD,IAAI,UAAU,EAAE;QACf,QAAQ,CAAC,MAAM,CACd,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAI,KAAI;YAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,CAAa,EAAE,QAAQ,CAAC,CAAC;YAEjD,OAAO,UAAU,KAAK,UAAU,CAAC;AAClC,SAAC,CAAC,EACF,CAAC,CACD,CAAC;KACF;SAAM;AACN,QAAA,QAAQ,CAAC,MAAM,CACd,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,EACnC,CAAC,CACD,CAAC;KACF;AAED,IAAA,OAAO,QAAQ,CAAC;AACjB,CAAC;AAGK,SAAU,eAAe,CAAI,OAAyB,EAAA;IAC3D,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,KAAI;AACpC,QAAA,MAAM,eAAe,GACjB,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,MAAM,KACT,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC,YAAY,IAAI,SAAS,GAC/D,CAAC;AAEF,QAAA,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;AACf,YAAA,eAAe,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC;AAE/B,YAAA,OAAO,eAAe,CAAC;SACvB;AAED,QAAA,OAAO,eAAe,CAAC;AACxB,KAAC,CAAC,CAAC;AACJ,CAAC;AAEe,SAAA,gBAAgB,CAAC,YAAA,GAAoC,KAAK,EAAA;AACzE,IAAA,OAAO,YAAY,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC;AACtD,CAAC;SAEe,mBAAmB,CAClC,MAAkC,EAClC,GAAG,IAAe,EAAA;AAElB,IAAA,IAAI,SAAS,CAAC;AAEd,IAAA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;SACjB,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;AACnB,SAAA,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;QACzB,MAAM,SAAS,GAAG,MAAM,CAAC;AAEzB,QAAA,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAChC,SAAS,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,SAAS,CAAE,EAAA,EAAA,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,EAAA,CAAE,CAAC;SAE3E;AACF,KAAC,CAAC,CAAC;IAEJ,OAAO,SAAS,IAAI,MAAM,CAAC;AAC5B,CAAC;AAEe,SAAA,gBAAgB,CAAC,QAAgB,EAAE,WAAmB,EAAA;IACrE,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,CAAC;AAC1C,CAAC;AAEe,SAAA,eAAe,CAAC,QAAgB,EAAE,QAAgB,EAAA;IACjE,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACrC,CAAC;AAEM,MAAM,IAAI,GAAG,MAAY,IAAI,CAAC;AAE/B,SAAU,mBAAmB,CAClC,GAAM,EACN,oBAA+C,GAAA,EAAE,EACjD,cAAA,GAA2B,EAAE,EAAA;IAE7B,IAAI,QAAQ,GAAG,EAAE,CAAC;AAClB,IAAA,IAAI,UAAU,GAAa,CAAC,GAAG,cAAc,CAAC,CAAC;AAE/C,IAAA,IAAI,oBAAoB,CAAC,MAAM,EAAE;AAChC,QAAA,oBAAoB,CAAC,OAAO,CAAC,GAAG,IAAG;AAClC,YAAA,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,UAAU,EAAE;AAChD,gBAAA,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;aAC/F;AAGD,YAAA,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AAClB,gBAAA,QAAQ,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;AAE3B,gBAAA,IAAI,GAAG,CAAC,UAAU,EAAE;oBACnB,UAAU,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC;iBAChD;AAED,gBAAA,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,UAAU,EAAE;oBACpC,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;iBAChC;aACD;AACF,SAAC,CAAC,CAAC;KACH;AAED,IAAA,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;AACzE,CAAC;AAEK,SAAU,aAAa,CAAI,GAAM,EAAE,eAAoB,EAAE,EAAE,QAAQ,GAAG,IAAI,EAAA;IAE/E,MAAM,UAAU,GAAG,IAAI,CAAC,GAAe,EAAE,QAAQ,CAAC,CAAC;IAEnD,IAAI,UAAU,EAAE;AACf,QAAA,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC,IAAG;YAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,CAAa,EAAE,QAAQ,CAAC,CAAC;YAEjD,OAAO,UAAU,KAAK,UAAU,CAAC;AAClC,SAAC,CAAC,CAAC;KACH;AAED,IAAA,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AAC1C,CAAC;AAEK,SAAU,KAAK,CAAC,GAAW,EAAA;AAChC,IAAA,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACtB,CAAC;AAEe,SAAA,mBAAmB,CAAI,OAAyB,EAAE,EAAsB,EAAA;IACvF,IAAI,CAAC,EAAE,EAAE;QACR,OAAO,CAAC,CAAC,CAAC;KACV;AAED,IAAA,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,IAAG;QAC5B,OAAO,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC7B,KAAC,CAAC,CAAC;AACJ,CAAC;AAEe,SAAA,UAAU,CAAC,CAA8B,EAAE,CAA8B,EAAA;IACxF,OAAO,CAAC,IAAI,CAAC,CAAC;AACf;;ACxNgB,SAAA,YAAY,CAAI,KAAoB,EAAE,MAAiB,EAAA;AACtE,IAAA,MAAM,0BAA0B,GAAG,CAAC,KAAK,CAAC,0BAA0B,CAAC;AAErE,IAAA,QAAQ,MAAM,CAAC,IAAI;QAClB,KAAK,iBAAiB,EAAE;YACvB,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC;AAC7D,YAAA,MAAM,UAAU,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC;AACtC,YAAA,MAAM,0BAA0B,GAAG,CAAC,KAAK,CAAC,0BAA0B,CAAC;YAErE,IAAI,eAAe,EAAE;gBACpB,MAAM,UAAU,GAAG,UAAU;AAC5B,sBAAE,CAAC,GAAG,KAAK,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;sBACjG,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;AAEzE,gBAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACI,KAAK,CACR,EAAA,EAAA,WAAW,EAAE,UAAU,EACvB,aAAa,EAAE,UAAU,CAAC,MAAM,EAChC,YAAY,EAAE,UAAU,EACxB,0BAA0B,EACzB,CAAA,CAAA;aACF;AAED,YAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACI,KAAK,CAAA,EAAA,EACR,WAAW,EAAE,UAAU,EACvB,aAAa,EAAE,UAAU,GAAG,QAAQ,GAAG,CAAC,EACxC,YAAY,EAAE,UAAU,GAAG,IAAI,GAAG,EAAE,EACpC,0BAA0B,EACzB,CAAA,CAAA;SACF;QAED,KAAK,mBAAmB,EAAE;AACzB,YAAA,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC;YAGrE,IAAI,YAAY,EAAE;gBACjB,IAAI,UAAU,EAAE;AACf,oBAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACI,KAAK,CAAA,EAAA,EACR,aAAa,EAAE,CAAC,EAChB,WAAW,EAAE,KAAK,EAClB,YAAY,EAAE,EAAE,EAChB,0BAA0B,EACzB,CAAA,CAAA;iBACF;AAED,gBAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACI,KAAK,CACR,EAAA,EAAA,aAAa,EAAE,CAAC,EAChB,WAAW,EAAE,KAAK,EAClB,YAAY,EAAE,CAAC,GAAG,CAAC,EACnB,0BAA0B,EACzB,CAAA,CAAA;aACF;YAGD,IAAI,UAAU,EAAE;gBACf,OACI,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAK,KACR,aAAa,EAAE,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAChF,WAAW,EAAE,KAAK,EAClB,YAAY,EAAE,UAAU,CAAC,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE,QAAQ,CAAC,EAC3D,0BAA0B,EACzB,CAAA,CAAA;aACF;AAED,YAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACI,KAAK,CACR,EAAA,EAAA,aAAa,EAAE,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAC5C,WAAW,EAAE,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,KAAK,QAAQ,EACvD,YAAY,EAAE,UAAU,CAAC,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,EACjD,0BAA0B,EACzB,CAAA,CAAA;SACF;QAED,KAAK,sBAAsB,EAAE;YAC5B,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC;YAEtE,IAAI,eAAe,EAAE;AACpB,gBAAA,MAAM,UAAU,GAAG;oBAClB,GAAG,KAAK,CAAC,YAAY;AACrB,oBAAA,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;iBAChF,CAAC;AAEF,gBAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACI,KAAK,CACR,EAAA,EAAA,aAAa,EAAE,UAAU,CAAC,MAAM,EAChC,WAAW,EAAE,KAAK,EAClB,YAAY,EAAE,UAAU,EACxB,0BAA0B,EACzB,CAAA,CAAA;aACF;AAED,YAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACI,KAAK,CACR,EAAA,EAAA,aAAa,EAAE,YAAY,CAAC,MAAM,EAClC,WAAW,EAAE,YAAY,CAAC,MAAM,KAAK,SAAS,EAC9C,YAAY;AACZ,gBAAA,0BAA0B,EACzB,CAAA,CAAA;SACF;QAED,KAAK,qBAAqB,EAAE;AAC3B,YAAA,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,CAAC;AAEpC,YAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACI,KAAK,CAAA,EAAA,EACR,WAAW,EAAE,KAAK,EAClB,aAAa,EAAE,CAAC,EAChB,YAAY,EAAE,EAAE,EAChB,gBAAgB,EACf,CAAA,CAAA;SACF;QAED,KAAK,aAAa,EAAE;YACnB,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,mBAAmB,EAAE,GAAG,MAAM,CAAC;YAEtE,OACI,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAK,KACR,cAAc;AACd,gBAAA,aAAa,EACb,WAAW,EAAE,CAAC,EAEX,CAAA,GAAC,mBAAmB,IAAI;AAC1B,gBAAA,WAAW,EAAE,KAAK;AAClB,gBAAA,aAAa,EAAE,CAAC;AAChB,gBAAA,YAAY,EAAE,EAAE;gBAChB,0BAA0B;AAC1B,aAAA,EACA,CAAA;SACF;QAED,KAAK,aAAa,EAAE;YACnB,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,2BAA2B,EAAE,GAAG,MAAM,CAAC;AACpF,YAAA,MAAM,eAAe,GAAG,gBAAgB,IAAI,2BAA2B,CAAC;YACxE,MAAM,mBAAmB,GAAG,CAAC,gBAAgB,IAAI,CAAC,2BAA2B,KAAK,WAAW,CAAC;YAE9F,OACI,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAK,KACR,WAAW,EAAE,IAAI,EACd,CAAA,GAAC,eAAe,IAAI;AACtB,gBAAA,WAAW,EAAE,KAAK;aAClB,EAAC,GAEE,mBAAmB,IAAI;AAC1B,gBAAA,WAAW,EAAE,KAAK;AAClB,gBAAA,aAAa,EAAE,CAAC;AAChB,gBAAA,YAAY,EAAE,EAAE;gBAChB,0BAA0B;AAC1B,aAAA,EACA,CAAA;SACF;QAED,KAAK,sBAAsB,EAAE;AAC5B,YAAA,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;AAErC,YAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACI,KAAK,CACR,EAAA,EAAA,WAAW,EAAE,IAAI,EACjB,WAAW,EACV,CAAA,CAAA;SACF;KACD;AACF;;ACrKA,MAAM,WAAW,GAAGA,UAAG,CAAA,CAAA;;;CAGtB,CAAC;AAEF,MAAM,UAAU,GAAGC,0BAAM,CAAC,GAAG,CAE3B,CAAA;;;;;;;;GAQC,CAAC,EAAE,QAAQ,EAAE,KAAK,QAAQ,IAAI,WAAW,CAAA;GACzC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,KAAK,CAAC,KAAK,CAAA;CAClC;;ACjBD,MAAM,QAAQ,GAAGD,UAAG,CAAA,CAAA;;;;;CAKnB,CAAC;AAEF,MAAM,IAAI,GAAGC,0BAAM,CAAC,GAAG,CAErB,CAAA;;;GAGC,CAAC,EAAE,YAAY,EAAE,KAAK,YAAY,IAAI,QAAQ,CAAA;GAC9C,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC,KAAK,CAAA;CACjC;;ACdD,MAAM,OAAO,GAAGA,0BAAM,CAAC,GAAG,CAGxB,CAAA;;;;GAIC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,KAAK,CAAA;AAClC,CAAA,EAAA,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAA;CAC3D;;ACTM,MAAM,KAAK,GAAG,GAAG,CAAC;AAClB,MAAM,MAAM,GAAG,GAAG,CAAC;AACnB,MAAM,KAAK,GAAG,IAAI,CAAC;AAEnB,MAAM,KAAK,GAAG;IACpB,EAAE,EAAE,CAAC,QAA8B,EAAE,GAAG,IAAiB,KAAsBD,UAAG,CAAA,CAAA;kCACjD,KAAK,CAAA;AAClC,GAAA,EAAAA,UAAG,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAA;;AAEzB,CAAA,CAAA;IACD,EAAE,EAAE,CAAC,QAA8B,EAAE,GAAG,IAAiB,KAAsBA,UAAG,CAAA,CAAA;kCACjD,MAAM,CAAA;AACnC,GAAA,EAAAA,UAAG,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAA;;AAEzB,CAAA,CAAA;IACD,EAAE,EAAE,CAAC,QAA8B,EAAE,GAAG,IAAiB,KAAsBA,UAAG,CAAA,CAAA;kCACjD,KAAK,CAAA;AAClC,GAAA,EAAAA,UAAG,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAA;;AAEzB,CAAA,CAAA;AACD,IAAA,MAAM,EACL,CAAC,KAAa,KACd,CAAC,QAA8B,EAAE,GAAG,IAAiB,KAAsBA,UAAG,CAAA,CAAA;mCAC7C,KAAK,CAAA;AAClC,IAAA,EAAAA,UAAG,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAA;;AAEzB,EAAA,CAAA;CACF;;ACzBM,MAAM,QAAQ,GAAGC,0BAAM,CAAC,GAAG,CAGhC,CAAA;;;;;;GAMC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,KAAK,CAAC,SAAS,GAAG,WAAW,GAAG,OAAO,CAAC,CAAC,KAAK,CAAA;GACxE,CAAC,EAAE,UAAU,EAAE,KAAK,UAAU,IAAI,YAAY,CAAA;CAChD,CAAC;AAQK,MAAM,YAAY,GAAGA,0BAAM,CAAC,QAAQ,CAAC,CAAW,CAAA;cACzC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,IAAI,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAA;;;cAG5D,CAAC,EAAE,QAAQ,EAAE,KAAK,QAAQ,IAAI,MAAM,CAAA;cACpC,CAAC,EAAE,QAAQ,EAAE,KAAK,QAAQ,IAAI,OAAO,CAAA;AAChD,CAAA,EAAA,CAAC,EAAE,KAAK,EAAE,KACX,KAAK;AACL,IAAAD,UAAG,CAAA,CAAA;gBACW,KAAK,CAAA;gBACL,KAAK,CAAA;AAClB,EAAA,CAAA,CAAA;GACA,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,IAAI,2BAA2B,CAAA;AACnD,CAAA,EAAA,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,MAAM,KAAK,yBAAyB,CAAA;AACvE,CAAA,EAAA,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,OAAO,IAAI,MAAM,KAAK,YAAY,CAAA;;;AAG5D,CAAA,EAAA,CAAC,EAAE,IAAI,EAAE,KACV,IAAI;AACJ,IAAA,IAAI,KAAK,IAAI;IACb,KAAK,CAAC,EAAE,CAAA,CAAA;;AAEP,EAAA,CAAA,CAAA;AACA,CAAA,EAAA,CAAC,EAAE,IAAI,EAAE,KACV,IAAI;AACJ,IAAA,IAAI,KAAK,IAAI;IACb,KAAK,CAAC,EAAE,CAAA,CAAA;;AAEP,EAAA,CAAA,CAAA;AACA,CAAA,EAAA,CAAC,EAAE,IAAI,EAAE,KACV,IAAI;AACJ,IAAA,IAAI,KAAK,IAAI;IACb,KAAK,CAAC,EAAE,CAAA,CAAA;;AAEP,EAAA,CAAA,CAAA;AACA,CAAA,EAAA,CAAC,EAAE,IAAI,EAAE,KACV,IAAI;AACJ,IAAA,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;AACtB,IAAA,KAAK,CAAC,MAAM,CAAC,IAAc,CAAC,CAAA,CAAA;;AAE3B,EAAA,CAAA,CAAA;CACF;;AClDD,MAAM,WAAW,GAAGA,UAAG,CAAgB,CAAA;;AAEtB,eAAA,EAAA,CAAC,EAAE,SAAS,EAAE,MAAM,SAAS,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAA;AACvD,YAAA,EAAA,CAAC,EAAE,cAAc,EAAE,MAAM,cAAc,GAAG,SAAS,GAAG,QAAQ,CAAC,CAAA;;;CAG5E,CAAC;AAEF,MAAM,SAAS,GAAGC,0BAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,KAAK,KAAK;IACtD,KAAK,EAAE,KAAK,CAAC,KAAK;AAClB,CAAA,CAAC,CAAC,CAAgB,CAAA;GAChB,CAAC,EAAE,aAAa,EAAE,KAAK,CAAC,aAAa,IAAI,WAAW,CAAA;AACpD,CAAA,EAAA,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,WAAW,IAAI,KAAK,CAAC,KAAK,CAAC,aAAa,CAAA;AACpE,CAAA,EAAA,CAAC,EAAE,UAAU,EAAE,KAAK,UAAU,CAAA;CAChC,CAAC;AAgBF,SAAS,IAAI,CAAI,EAChB,EAAE,EACF,MAAM,EACN,GAAG,EACH,QAAQ,EACR,OAAO,EACP,UAAU,EACV,WAAW,EACX,UAAU,EACV,SAAS,EACT,WAAW,EACX,WAAW,GACG,EAAA;AACd,IAAA,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,GAAG,mBAAmB,CAAC,GAAG,EAAE,MAAM,CAAC,qBAAqB,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;IAEnH,QACCC,gBAAC,CAAA,aAAA,CAAA,SAAS,EACT,EAAA,EAAE,EAAE,EAAE,EACU,gBAAA,EAAA,MAAM,CAAC,EAAE,EACzB,IAAI,EAAC,MAAM,EACX,SAAS,EAAE,UAAU,EAAA,UAAA,EACX,OAAO,EAAA,YAAA,EACL,MAAM,CAAC,KAAK,EAAA,eAAA,EACT,CAAC,CAAC,MAAM,CAAC,IAAI,EACZ,gBAAA,EAAA,MAAM,CAAC,aAAa,EACpC,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,OAAO,EAAE,MAAM,CAAC,OAAO,EACvB,IAAI,EAAE,MAAM,CAAC,IAAI,EACjB,IAAI,EAAE,MAAM,CAAC,IAAI,EACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ,EACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ,EACzB,KAAK,EAAE,MAAM,CAAC,KAAK,EACnB,KAAK,EAAE,MAAM,CAAC,KAAK,eACR,MAAM,CAAC,IAAI,EACtB,KAAK,EAAE,gBAAuC,EAAA,aAAA,EACjC,UAAU,EACvB,WAAW,EAAE,WAAW,EACxB,UAAU,EAAE,UAAU,EACtB,SAAS,EAAE,SAAS,EACpB,WAAW,EAAE,WAAW,EACxB,WAAW,EAAE,WAAW,EAAA;QAEvB,CAAC,MAAM,CAAC,IAAI,IAAIA,oDAAe,OAAO,EAAA,EAAG,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAO;AAC1G,QAAA,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,CAC3C,EACX;AACH,CAAC;AAED,gBAAeA,gBAAK,CAAC,IAAI,CAAC,IAAI,CAAgB;;AC1F9C,MAAM,oBAAoB,GAAG,OAAO,CAAC;AAErC,MAAM,kBAAkB,GAAG,CAAC,QAAiB,MAAK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EACjD,QAAQ,EAAE,MAAM,EACb,GAAC,CAAC,QAAQ,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,EACtC,EAAA,EAAA,OAAO,EAAE,CAAC,EACV,SAAS,EAAE,KAAK,EAChB,aAAa,EAAE,QAAQ,EACvB,QAAQ,EAAE,UAAU,IACnB,CAAC;AAaH,SAAS,QAAQ,CAAC,EACjB,IAAI,EACJ,SAAS,GAAG,oBAAoB,EAChC,gBAAgB,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,EAChC,aAAa,GAAG,KAAK,EACrB,OAAO,GAAG,KAAK,EACf,QAAQ,GAAG,KAAK,EAChB,OAAO,GAAG,IAAI,GACC,EAAA;AACf,IAAA,MAAM,cAAc,GAAG,CAAC,QAA0B,KAAI;QACrD,IAAI,QAAQ,EAAE;AAEb,YAAA,QAAQ,CAAC,aAAa,GAAG,aAAa,CAAC;SACvC;AACF,KAAC,CAAC;IAEF,MAAM,OAAO,GAAG,SAAS,CAAC;AAC1B,IAAA,MAAM,SAAS,GAAG,OAAO,KAAK,oBAAoB,GAAG,gBAAgB,CAAC,KAAK,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAC3G,MAAM,wBAAwB,GAAGA,gBAAK,CAAC,OAAO,CAC7C,MAAM,mBAAmB,CAAC,gBAAgB,EAAE,aAAa,CAAC,EAC1D,CAAC,gBAAgB,EAAE,aAAa,CAAC,CACjC,CAAC;IAEF,QACCA,+BAAC,OAAO,EAAA,MAAA,CAAA,MAAA,CAAA,EAEP,IAAI,EAAC,UAAU,EACf,GAAG,EAAE,cAAc,EACnB,KAAK,EAAE,SAAS,EAChB,OAAO,EAAE,QAAQ,GAAG,IAAI,GAAG,OAAO,EAClC,IAAI,EAAE,IAAI,EACE,YAAA,EAAA,IAAI,EAChB,OAAO,EAAE,OAAO,EAChB,QAAQ,EAAE,QAAQ,EAAA,EACd,wBAAwB,EAAA,EAC5B,QAAQ,EAAE,IAAI,EACb,CAAA,CAAA,EACD;AACH,CAAC;AAED,iBAAeA,gBAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;;AC3DnC,MAAM,sBAAsB,GAAGD,0BAAM,CAAC,QAAQ,CAAC,CAAA,CAAA;;;;;;;CAO9C,CAAC;AAeF,SAAS,iBAAiB,CAAI,EAC7B,IAAI,EACJ,QAAQ,EACR,GAAG,EACH,QAAQ,EACR,QAAQ,EACR,uBAAuB,EACvB,4BAA4B,EAC5B,oBAAoB,EACpB,qBAAqB,EACrB,aAAa,GACc,EAAA;AAC3B,IAAA,MAAM,QAAQ,GAAG,CAAC,EAAE,qBAAqB,IAAI,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;IAEzE,MAAM,mBAAmB,GAAG,MAAK;AAChC,QAAA,aAAa,CAAC;AACb,YAAA,IAAI,EAAE,mBAAmB;YACzB,GAAG;AACH,YAAA,UAAU,EAAE,QAAQ;YACpB,QAAQ;YACR,QAAQ;AACR,YAAA,YAAY,EAAE,oBAAoB;AAClC,SAAA,CAAC,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,QACCC,gBAAC,CAAA,aAAA,CAAA,sBAAsB,IAAC,OAAO,EAAE,CAAC,CAAmB,KAAK,CAAC,CAAC,eAAe,EAAE,EAAE,SAAS,EAAC,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA;AACvG,QAAAA,gBAAA,CAAA,aAAA,CAACC,UAAQ,EAAA,EACR,IAAI,EAAE,IAAI,EACV,SAAS,EAAE,uBAAuB,EAClC,gBAAgB,EAAE,4BAA4B,EAC9C,OAAO,EAAE,QAAQ,EACH,cAAA,EAAA,QAAQ,EACtB,OAAO,EAAE,mBAAmB,EAC5B,QAAQ,EAAE,QAAQ,EACjB,CAAA,CACsB,EACxB;AACH;;AC9DA,MAAM,WAAW,GAAGF,0BAAM,CAAC,MAAM,CAAA,CAAA;;;;;;;GAO9B,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,cAAc,CAAC,KAAK,CAAA;CAC3C,CAAC;AAWF,SAAS,cAAc,CAAI,EAC1B,QAAQ,GAAG,KAAK,EAChB,QAAQ,GAAG,KAAK,EAChB,cAAc,EACd,EAAE,EACF,GAAG,EACH,SAAS,GACe,EAAA;AACxB,IAAA,MAAM,IAAI,GAAG,QAAQ,GAAG,cAAc,CAAC,QAAQ,GAAG,cAAc,CAAC,SAAS,CAAC;IAC3E,MAAM,YAAY,GAAG,MAAM,SAAS,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;AAEvD,IAAA,QACCC,gBAAC,CAAA,aAAA,CAAA,WAAW,EACI,EAAA,eAAA,EAAA,QAAQ,EACvB,OAAO,EAAE,YAAY,EAAA,aAAA,EACR,mBAAmB,EAAE,CAAA,CAAE,EACpC,QAAQ,EAAE,QAAQ,EACN,YAAA,EAAA,QAAQ,GAAG,cAAc,GAAG,YAAY,EACpD,IAAI,EAAC,QAAQ,EACb,IAAI,EAAC,QAAQ,IAEZ,IAAI,CACQ,EACb;AACH;;ACzCA,MAAM,iBAAiB,GAAGD,0BAAM,CAAC,QAAQ,CAAC,CAAA,CAAA;;;;GAIvC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,CAAA;CACzC,CAAC;AAWF,SAAS,YAAY,CAAI,EACxB,GAAG,EACH,QAAQ,GAAG,KAAK,EAChB,cAAc,EACd,EAAE,EACF,SAAS,EACT,QAAQ,GAAG,KAAK,GACM,EAAA;AACtB,IAAA,QACCC,gBAAA,CAAA,aAAA,CAAC,iBAAiB,EAAA,EAAC,OAAO,EAAE,CAAC,CAAmB,KAAK,CAAC,CAAC,eAAe,EAAE,EAAA,YAAA,EAAA,IAAA,EAAA;AACvE,QAAAA,gBAAA,CAAA,aAAA,CAAC,cAAc,EAAA,EACd,EAAE,EAAE,EAAE,EACN,GAAG,EAAE,GAAG,EACR,QAAQ,EAAE,QAAQ,EAClB,cAAc,EAAE,cAAc,EAC9B,QAAQ,EAAE,QAAQ,EAClB,SAAS,EAAE,SAAS,EAAA,CACnB,CACiB,EACnB;AACH;;ACtCA,MAAM,gBAAgB,GAAGD,0BAAM,CAAC,GAAG,CAEjC,CAAA;;;GAGC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,WAAW,CAAC,KAAK,CAAA;AACtC,CAAA,EAAA,CAAC,EAAE,iBAAiB,EAAE,KAAK,iBAAiB,CAAA;CAC9C,CAAC;AAUF,SAAS,WAAW,CAAI,EACvB,IAAI,EACJ,iBAAiB,EACjB,sBAAsB,EACtB,gBAAgB,EAChB,kBAAkB,GACG,EAAA;AAErB,IAAA,MAAM,eAAe,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,cAAc,CAAC,CAAC;AACxF,IAAA,MAAM,UAAU,GAAG,CAAC,iBAAiB,EAAE,GAAG,eAAe,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAErE,QACCC,+BAAC,gBAAgB,EAAA,EAAC,SAAS,EAAE,UAAU,uBAAqB,gBAA6B,EAAA;QACxFA,gBAAC,CAAA,aAAA,CAAA,iBAAiB,EAAC,MAAA,CAAA,MAAA,CAAA,EAAA,IAAI,EAAE,IAAI,IAAM,sBAAsB,CAAA,CAAI,CAC3C,EAClB;AACH,CAAC;AAED,oBAAeA,gBAAK,CAAC,IAAI,CAAC,WAAW,CAAuB;;ACvCrD,MAAM,aAAa,GAAG,iBAAiB;AAElCE,2BAIX;AAJD,CAAA,UAAY,SAAS,EAAA;AACpB,IAAA,SAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,SAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,SAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACd,CAAC,EAJWA,iBAAS,KAATA,iBAAS,GAIpB,EAAA,CAAA,CAAA,CAAA;AAEWC,2BAIX;AAJD,CAAA,UAAY,SAAS,EAAA;AACpB,IAAA,SAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb,IAAA,SAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf,IAAA,SAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AAClB,CAAC,EAJWA,iBAAS,KAATA,iBAAS,GAIpB,EAAA,CAAA,CAAA,CAAA;AAEWC,uBAIX;AAJD,CAAA,UAAY,KAAK,EAAA;AAChB,IAAA,KAAA,CAAA,IAAA,CAAA,GAAA,IAAS,CAAA;AACT,IAAA,KAAA,CAAA,IAAA,CAAA,GAAA,IAAS,CAAA;AACT,IAAA,KAAA,CAAA,IAAA,CAAA,GAAA,IAAS,CAAA;AACV,CAAC,EAJWA,aAAK,KAALA,aAAK,GAIhB,EAAA,CAAA,CAAA;;ACPD,MAAM,YAAY,GAAGN,UAAG,CAEtB,CAAA;;AAEE,EAAA,EAAA,CAAC,EAAE,iBAAiB,EAAE,KAAK,EAAE,KAAK,iBAAiB,IAAI,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAA;;CAE1F,CAAC;AAEF,MAAM,UAAU,GAAGA,UAAG,CAAA,CAAA;;;;CAIrB,CAAC;AAEF,MAAM,aAAa,GAAGC,0BAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK;IAChD,KAAK,EAAE,KAAK,CAAC,KAAK;AAClB,CAAA,CAAC,CAAC,CAOD,CAAA;;;;;;GAMC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC,KAAK,CAAA;AAC/B,CAAA,EAAA,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAA;AACtD,CAAA,EAAA,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAA;GAC5D,CAAC,EAAE,iBAAiB,EAAE,KAAK,iBAAiB,IAAI,YAAY,CAAA;GAC5D,CAAC,EAAE,eAAe,EAAE,KAAK,eAAe,IAAI,UAAU,CAAA;AACtD,CAAA,EAAA,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAA;AACxE,CAAA,EAAA,CAAC,EAAE,iBAAiB,EAAE,KAAK,iBAAiB,CAAA;CAC9C,CAAC;AAkDF,SAAS,GAAG,CAAI,EACf,OAAO,GAAG,EAAE,EACZ,oBAAoB,GAAG,EAAE,EACzB,eAAe,GAAG,KAAK,EACvB,uBAAuB,GAAG,KAAK,EAC/B,KAAK,GAAG,KAAK,EACb,cAAc,EACd,cAAc,GAAG,KAAK,EACtB,uBAAuB,EACvB,4BAA4B,EAC5B,0BAA0B,EAC1B,kBAAkB,GAAG,KAAK,EAC1B,wBAAwB,GAAG,KAAK,EAChC,gBAAgB,GAAG,KAAK,EACxB,EAAE,EACF,kCAAkC,EAClC,QAAQ,EACR,YAAY,GAAG,IAAI,EACnB,kBAAkB,GAAG,IAAI,EACzB,eAAe,GAAG,IAAI,EACtB,eAAe,GAAG,IAAI,EACtB,kBAAkB,GAAG,IAAI,EACzB,aAAa,GAAG,IAAI,EACpB,cAAc,GAAG,KAAK,EACtB,GAAG,EACH,QAAQ,EACR,QAAQ,EACR,qBAAqB,GAAG,IAAI,EAC5B,cAAc,GAAG,KAAK,EACtB,uBAAuB,EACvB,4BAA4B,EAC5B,uBAAuB,GAAG,KAAK,EAC/B,oBAAoB,GAAG,KAAK,EAC5B,QAAQ,EACR,OAAO,GAAG,KAAK,EACf,gBAAgB,EAChB,WAAW,EACX,UAAU,EACV,SAAS,EACT,WAAW,EACX,WAAW,GACO,EAAA;AAClB,IAAA,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAGC,gBAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;AAEhE,IAAAA,gBAAK,CAAC,SAAS,CAAC,MAAK;QACpB,WAAW,CAAC,eAAe,CAAC,CAAC;AAC9B,KAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;AAEtB,IAAA,MAAM,cAAc,GAAGA,gBAAK,CAAC,WAAW,CAAC,MAAK;AAC7C,QAAA,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC;AACvB,QAAA,kBAAkB,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;KACnC,EAAE,CAAC,QAAQ,EAAE,kBAAkB,EAAE,GAAG,CAAC,CAAC,CAAC;AAExC,IAAA,MAAM,WAAW,GAAG,cAAc,KAAK,cAAc,KAAK,kBAAkB,IAAI,wBAAwB,CAAC,CAAC,CAAC;IAE3G,MAAM,cAAc,GAAGA,gBAAK,CAAC,WAAW,CACvC,CAAC,CAAmC,KAAI;AAEvC,QAAA,MAAM,MAAM,GAAG,CAAC,CAAC,MAAwB,CAAC;QAE1C,IAAI,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,aAAa,EAAE;AACtD,YAAA,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAErB,YAAA,IAAI,CAAC,uBAAuB,IAAI,cAAc,IAAI,kBAAkB,EAAE;AACrE,gBAAA,cAAc,EAAE,CAAC;aACjB;SACD;AACF,KAAC,EACD,CAAC,uBAAuB,EAAE,kBAAkB,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG,CAAC,CAChG,CAAC;IAEF,MAAM,oBAAoB,GAAGA,gBAAK,CAAC,WAAW,CAC7C,CAAC,CAAmC,KAAI;AACvC,QAAA,MAAM,MAAM,GAAG,CAAC,CAAC,MAAwB,CAAC;QAE1C,IAAI,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,aAAa,EAAE;AACtD,YAAA,kBAAkB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC3B,YAAA,IAAI,CAAC,uBAAuB,IAAI,cAAc,IAAI,wBAAwB,EAAE;AAC3E,gBAAA,cAAc,EAAE,CAAC;aACjB;SACD;AACF,KAAC,EACD,CAAC,uBAAuB,EAAE,wBAAwB,EAAE,cAAc,EAAE,cAAc,EAAE,kBAAkB,EAAE,GAAG,CAAC,CAC5G,CAAC;IAEF,MAAM,mBAAmB,GAAGA,gBAAK,CAAC,WAAW,CAC5C,CAAC,CAAwC,KAAI;AAC5C,QAAA,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACzB,KAAC,EACD,CAAC,eAAe,EAAE,GAAG,CAAC,CACtB,CAAC;IAEF,MAAM,mBAAmB,GAAGA,gBAAK,CAAC,WAAW,CAC5C,CAAC,CAAwC,KAAI;AAC5C,QAAA,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACzB,KAAC,EACD,CAAC,eAAe,EAAE,GAAG,CAAC,CACtB,CAAC;IAEF,MAAM,WAAW,GAAG,IAAI,CAAC,GAAe,EAAE,QAAQ,CAAC,CAAC;AACpD,IAAA,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,GAAG,mBAAmB,CAAC,GAAG,EAAE,oBAAoB,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;AAC1G,IAAA,MAAM,iBAAiB,GAAG,uBAAuB,IAAI,QAAQ,CAAC;IAC9D,MAAM,aAAa,GAAG,kCAAkC,GAAG,gBAAgB,GAAG,EAAE,CAAC;IACjF,MAAM,SAAS,GAAG,OAAO,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;AAE7C,IAAA,QACCA,gBAAA,CAAA,aAAA,CAAAA,gBAAA,CAAA,QAAA,EAAA,IAAA;QACCA,gBAAC,CAAA,aAAA,CAAA,aAAa,IACb,EAAE,EAAE,OAAO,EAAE,CAAA,CAAE,EACf,IAAI,EAAC,KAAK,EACA,UAAA,EAAA,SAAS,uBACA,gBAAgB,EAAA,iBAAA,EAClB,CAAC,uBAAuB,IAAI,WAAW,EAChD,QAAA,EAAA,KAAK,EACb,OAAO,EAAE,cAAc,EACvB,aAAa,EAAE,oBAAoB,EACnC,YAAY,EAAE,mBAAmB,EACjC,YAAY,EAAE,mBAAmB,EACjC,SAAS,EAAE,UAAU,EAAA,WAAA,EACV,iBAAiB,EAAA,mBAAA,EACT,gBAAgB,EAAA;YAElC,cAAc,KACdA,gBAAC,CAAA,aAAA,CAAA,iBAAiB,IACjB,IAAI,EAAE,cAAc,WAAW,CAAA,CAAE,EACjC,QAAQ,EAAE,QAAQ,EAClB,GAAG,EAAE,GAAG,EACR,QAAQ,EAAE,QAAQ,EAClB,QAAQ,EAAE,QAAQ,EAClB,uBAAuB,EAAE,uBAAuB,EAChD,4BAA4B,EAAE,4BAA4B,EAC1D,qBAAqB,EAAE,qBAAqB,EAC5C,oBAAoB,EAAE,oBAAoB,EAC1C,aAAa,EAAE,aAAa,EAAA,CAC3B,CACF;AAEA,YAAA,cAAc,IAAI,CAAC,0BAA0B,KAC7CA,gBAAC,CAAA,aAAA,CAAAK,YAAiB,EACjB,EAAA,EAAE,EAAE,WAAqB,EACzB,cAAc,EAAE,cAAc,EAC9B,QAAQ,EAAE,QAAQ,EAClB,GAAG,EAAE,GAAG,EACR,SAAS,EAAE,cAAc,EACzB,QAAQ,EAAE,uBAAuB,GAChC,CACF;AAEA,YAAA,OAAO,CAAC,GAAG,CAAC,MAAM,IAAG;AACrB,gBAAA,IAAI,MAAM,CAAC,IAAI,EAAE;AAChB,oBAAA,OAAO,IAAI,CAAC;iBACZ;AAED,gBAAA,QACCL,gBAAC,CAAA,aAAA,CAAA,SAAS,IACT,EAAE,EAAE,QAAQ,MAAM,CAAC,EAAE,CAAA,CAAA,EAAI,WAAW,CAAE,CAAA,EACtC,GAAG,EAAE,CAAA,KAAA,EAAQ,MAAM,CAAC,EAAE,CAAI,CAAA,EAAA,WAAW,EAAE,EAEvC,OAAO,EAAE,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,MAAM,GAAG,IAAI,GAAG,aAAa,EACtE,MAAM,EAAE,MAAM,EACd,GAAG,EAAE,GAAG,EACR,QAAQ,EAAE,QAAQ,EAClB,UAAU,EAAE,UAAU,CAAC,gBAAgB,EAAE,MAAM,CAAC,EAAE,CAAC,EACnD,WAAW,EAAE,WAAW,EACxB,UAAU,EAAE,UAAU,EACtB,SAAS,EAAE,SAAS,EACpB,WAAW,EAAE,WAAW,EACxB,WAAW,EAAE,WAAW,EAAA,CACvB,EACD;AACH,aAAC,CAAC,CACa;AAEf,QAAA,cAAc,IAAI,QAAQ,KAC1BA,gBAAA,CAAA,aAAA,CAACM,aAAW,EACX,EAAA,GAAG,EAAE,CAAA,SAAA,EAAY,WAAW,CAAE,CAAA,EAC9B,IAAI,EAAE,GAAG,EACT,gBAAgB,EAAE,aAAa,EAC/B,kBAAkB,EAAE,UAAU,EAC9B,iBAAiB,EAAE,uBAAuB,EAC1C,sBAAsB,EAAE,4BAA4B,EAAA,CACnD,CACF,CACC,EACF;AACH;;ACtRA,MAAM,IAAI,GAAGP,0BAAM,CAAC,IAAI,CAGtB,CAAA;;;;;AAKC,CAAA,EAAA,CAAC,EAAE,WAAW,EAAE,MAAM,WAAW,GAAG,YAAY,GAAG,YAAY,CAAC,CAAA;GAChE,CAAC,EAAE,cAAc,EAAE,KAAK,cAAc,KAAK,MAAM,IAAI,2BAA2B,CAAA;CAClF,CAAC;AAOF,MAAM,cAAc,GAAkC,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,MACnFC,yBAAA,CAAA,aAAA,CAAC,IAAI,EAAc,EAAA,aAAA,EAAA,UAAU,oBAAkB,aAAa,EAAA,EAAA,QAAA,CAErD,CACP;;ACTD,MAAM,YAAY,GAAGD,0BAAM,CAAC,YAAY,CAAC,CAAkB,CAAA;GACxD,CAAC,EAAE,MAAM,EAAE,KAAK,MAAM,IAAI,oBAAoB,CAAA;AAC9C,CAAA,EAAA,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,WAAW,IAAI,KAAK,CAAC,SAAS,CAAC,aAAa,CAAA;CAC1E,CAAC;AAOF,MAAM,WAAW,GAAGD,UAAG,CAAqB,CAAA;;;;;;AAMvC,GAAA,EAAA,CAAC,EAAE,WAAW,EAAE,MAAM,WAAW,GAAG,YAAY,GAAG,YAAY,CAAC,CAAA;;;;;;;;;;;;;;;;;AAiBlE,CAAA,EAAA,CAAC,EAAE,WAAW,EAAE,KACjB,CAAC,WAAW;AACZ,IAAAA,UAAG,CAAA,CAAA;;;;;;;;;;AAUF,EAAA,CAAA,CAAA;CACF,CAAC;AAEF,MAAM,cAAc,GAAGC,0BAAM,CAAC,GAAG,CAAqB,CAAA;;;;;;;;;GASnD,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,WAAW,CAAA;CAC5C,CAAC;AAEF,MAAM,UAAU,GAAGA,0BAAM,CAAC,GAAG,CAAA,CAAA;;;;CAI5B,CAAC;AAsBF,SAAS,QAAQ,CAAI,EACpB,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,cAAc,GAAG,EAAE,EACnB,aAAa,EACb,QAAQ,EACR,UAAU,EACV,UAAU,EACV,gBAAgB,EAChB,qBAAqB,EACrB,yBAAyB,EACzB,MAAM,EACN,WAAW,EACX,UAAU,EACV,SAAS,EACT,WAAW,EACX,WAAW,GACO,EAAA;AAClB,IAAAC,gBAAK,CAAC,SAAS,CAAC,MAAK;AACpB,QAAA,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACxC,OAAO,CAAC,KAAK,CACZ,CAAA,SAAA,EAAY,MAAM,CAAC,QAAQ,CAA6J,2JAAA,CAAA,CACxL,CAAC;SACF;KAED,EAAE,EAAE,CAAC,CAAC;AAEP,IAAA,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAGA,gBAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC5D,MAAM,SAAS,GAAGA,gBAAK,CAAC,MAAM,CAAwB,IAAI,CAAC,CAAC;AAE5D,IAAAA,gBAAK,CAAC,SAAS,CAAC,MAAK;AACpB,QAAA,IAAI,SAAS,CAAC,OAAO,EAAE;AACtB,YAAA,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SAC9E;AACF,KAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;AAElB,IAAA,IAAI,MAAM,CAAC,IAAI,EAAE;AAChB,QAAA,OAAO,IAAI,CAAC;KACZ;IAED,MAAM,gBAAgB,GAAG,MAAK;QAC7B,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YACzC,OAAO;SACP;QAED,IAAI,SAAS,GAAG,aAAa,CAAC;QAE9B,IAAI,UAAU,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE;AAC7C,YAAA,SAAS,GAAG,aAAa,KAAK,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC;SAC7E;AAED,QAAA,MAAM,CAAC;AACN,YAAA,IAAI,EAAE,aAAa;AACnB,YAAA,aAAa,EAAE,SAAS;AACxB,YAAA,cAAc,EAAE,MAAM;AACtB,YAAA,mBAAmB,EAClB,CAAC,UAAU,IAAI,gBAAgB,IAAI,CAAC,qBAAqB,KAAK,UAAU,IAAI,yBAAyB;AACtG,SAAA,CAAC,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,MAAM,cAAc,GAAG,CAAC,KAA0C,KAAI;AACrE,QAAA,IAAI,KAAK,CAAC,GAAG,KAAK,OAAO,EAAE;AAC1B,YAAA,gBAAgB,EAAE,CAAC;SACnB;AACF,KAAC,CAAC;IAEF,MAAM,oBAAoB,GAAG,CAAC,UAAmB,MAChDA,gBAAC,CAAA,aAAA,CAAA,cAAc,IAAC,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAA,CAAI,CACxE,CAAC;IAEF,MAAM,oBAAoB,GAAG,OAC5BA,gBAAA,CAAA,aAAA,CAAA,MAAA,EAAA,EAAM,SAAS,EAAE,CAAC,aAAa,EAAE,0BAA0B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAG,EAAA,QAAQ,CAAQ,CACzF,CAAC;IAEF,MAAM,UAAU,GAAG,CAAC,EAAE,MAAM,CAAC,QAAQ,IAAI,UAAU,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IACnF,MAAM,WAAW,GAAG,CAAC,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC;AACjD,IAAA,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;AACzE,IAAA,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC;AACzE,IAAA,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;IACxE,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,IAAI,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC;AAExE,IAAA,QACCA,gBAAC,CAAA,aAAA,CAAA,YAAY,EACI,EAAA,gBAAA,EAAA,MAAM,CAAC,EAAE,EACzB,SAAS,EAAC,cAAc,EAExB,WAAA,EAAA,IAAA,EAAA,aAAa,EAAE,MAAM,CAAC,aAAa,EACnC,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,OAAO,EAAE,MAAM,CAAC,OAAO,EACvB,IAAI,EAAE,MAAM,CAAC,IAAI,EACjB,IAAI,EAAE,MAAM,CAAC,IAAI,EACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ,EACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ,EACzB,KAAK,EAAE,MAAM,CAAC,KAAK,EACnB,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,KAAK,EAAE,MAAM,CAAC,KAAK,EACnB,SAAS,EAAE,MAAM,CAAC,OAAO,EAAA,aAAA,EACZ,UAAU,CAAC,MAAM,CAAC,EAAE,EAAE,gBAAgB,CAAC,EACpD,WAAW,EAAE,WAAW,EACxB,UAAU,EAAE,UAAU,EACtB,SAAS,EAAE,SAAS,EACpB,WAAW,EAAE,WAAW,EACxB,WAAW,EAAE,WAAW,EAAA,EAEvB,MAAM,CAAC,IAAI,KACXA,gBAAA,CAAA,aAAA,CAAC,cAAc,EACE,EAAA,gBAAA,EAAA,MAAM,CAAC,EAAE,kBACX,MAAM,CAAC,EAAE,EACvB,IAAI,EAAC,cAAc,EACnB,QAAQ,EAAE,CAAC,EACX,SAAS,EAAC,uBAAuB,EACjC,OAAO,EAAE,CAAC,WAAW,GAAG,gBAAgB,GAAG,SAAS,EACpD,UAAU,EAAE,CAAC,WAAW,GAAG,cAAc,GAAG,SAAS,EAAA,aAAA,EACxC,CAAC,WAAW,IAAI,UAAU,EACvC,QAAQ,EAAE,WAAW,EAAA;AAEpB,QAAA,CAAC,WAAW,IAAI,mBAAmB,IAAI,oBAAoB,EAAE;AAC7D,QAAA,CAAC,WAAW,IAAI,mBAAmB,IAAI,oBAAoB,CAAC,UAAU,CAAC;QAEvE,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,IAC/BA,gBAAA,CAAA,aAAA,CAAC,UAAU,EAAC,EAAA,KAAK,EAAE,WAAW,GAAG,MAAM,CAAC,IAAI,GAAG,SAAS,EAAE,GAAG,EAAE,SAAS,oBAAkB,MAAM,CAAC,EAAE,EACjG,EAAA,MAAM,CAAC,IAAI,CACA,KAEb,MAAM,CAAC,IAAI,CACX;AAEA,QAAA,CAAC,WAAW,IAAI,kBAAkB,IAAI,oBAAoB,EAAE;AAC5D,QAAA,CAAC,WAAW,IAAI,kBAAkB,IAAI,oBAAoB,CAAC,UAAU,CAAC,CACvD,CACjB,CACa,EACd;AACH,CAAC;AAED,aAAeA,gBAAK,CAAC,IAAI,CAAC,QAAQ,CAAoB;;ACxOtD,MAAM,WAAW,GAAGD,0BAAM,CAAC,QAAQ,CAAC,CAAA,CAAA;;;;;;;CAOnC,CAAC;AAeF,SAAS,cAAc,CAAI,EAC1B,QAAQ,GAAG,IAAI,EACf,OAAO,EACP,QAAQ,EACR,WAAW,EACX,eAAe,EACf,YAAY,EACZ,uBAAuB,EACvB,4BAA4B,EAC5B,qBAAqB,EACrB,eAAe,GACS,EAAA;IACxB,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC;IAC9D,MAAM,IAAI,GAAG,qBAAqB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAM,KAAK,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;AACvG,IAAA,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;AAErC,IAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAEvD,MAAM,eAAe,GAAG,MAAK;AAC5B,QAAA,eAAe,CAAC;AACf,YAAA,IAAI,EAAE,iBAAiB;YACvB,IAAI;YACJ,QAAQ;YACR,eAAe;YACf,QAAQ;AACR,SAAA,CAAC,CAAC;AACJ,KAAC,CAAC;IAEF,QACCC,+BAAC,WAAW,EAAA,EAAC,SAAS,EAAC,cAAc,eAAY,QAAQ,EAAA,YAAA,EAAA,IAAA,EAAA;AACxD,QAAAA,gBAAA,CAAA,aAAA,CAACC,UAAQ,EAAA,EACR,IAAI,EAAC,iBAAiB,EACtB,SAAS,EAAE,uBAAuB,EAClC,gBAAgB,EAAE,4BAA4B,EAC9C,OAAO,EAAE,eAAe,EACxB,OAAO,EAAE,WAAW,EACpB,aAAa,EAAE,aAAa,EAC5B,QAAQ,EAAE,UAAU,EACnB,CAAA,CACW,EACb;AACH;;AClEA,SAAS,MAAM,CAAC,SAAuB,GAAAC,iBAAS,CAAC,IAAI,EAAA;AACpD,IAAA,MAAM,QAAQ,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC;AAE5C,IAAA,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAGF,gBAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAEhD,IAAAA,gBAAK,CAAC,SAAS,CAAC,MAAK;QACpB,IAAI,CAAC,QAAQ,EAAE;YACd,OAAO;SACP;AAED,QAAA,IAAI,SAAS,KAAK,MAAM,EAAE;AACzB,YAAA,MAAM,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YACpE,MAAM,OAAO,GAAsB,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5E,MAAM,OAAO,GAAsB,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5E,YAAA,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC;AAE9D,YAAA,QAAQ,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;YAE3B,OAAO;SACP;AAED,QAAA,QAAQ,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC;AAC/B,KAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;AAE1B,IAAA,OAAO,KAAK,CAAC;AACd;;ACtBA,MAAMO,OAAK,GAAGR,0BAAM,CAAC,GAAG,CAAA,CAAA;;;;;UAKd,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,WAAW,CAAC,SAAS,CAAA;cACtC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAA;;CAEtD,CAAC;AAEF,MAAM,cAAc,GAAGA,0BAAM,CAAC,GAAG,CAAA,CAAA;;;;;CAKhC,CAAC;AAEF,MAAM,gBAAgB,GAAGA,0BAAM,CAAC,GAAG,CAGjC,CAAA;;;;;;;;;;;GAWC,CAAC,EAAE,IAAI,EAAE,KAAK,IAAI,IAAI,gBAAgB,CAAA;GACtC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,WAAW,CAAC,KAAK,CAAA;AACtC,CAAA,EAAA,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,CAAC,WAAW,CAAA;CACpE,CAAC;AAEF,MAAM,2BAA2B,GAAG,CAAC,cAA8B,EAAE,aAAqB,EAAE,GAAY,KAAI;AAC3G,IAAA,IAAI,aAAa,KAAK,CAAC,EAAE;AACxB,QAAA,OAAO,IAAI,CAAC;KACZ;AAED,IAAA,MAAM,SAAS,GAAG,aAAa,KAAK,CAAC,GAAG,cAAc,CAAC,QAAQ,GAAG,cAAc,CAAC,MAAM,CAAC;IAGxF,IAAI,GAAG,EAAE;QACR,OAAO,CAAA,EAAG,aAAa,CAAA,CAAA,EAAI,cAAc,CAAC,OAAO,IAAI,EAAE,CAAA,CAAA,EAAI,SAAS,CAAA,CAAE,CAAC;KACvE;IAED,OAAO,CAAA,EAAG,aAAa,CAAA,CAAA,EAAI,SAAS,CAAA,CAAA,EAAI,cAAc,CAAC,OAAO,IAAI,EAAE,CAAA,CAAE,CAAC;AACxE,CAAC,CAAC;AAUF,SAAS,WAAW,CAAC,EACpB,cAAc,EACd,cAAc,EACd,gBAAgB,EAChB,aAAa,EACb,SAAS,GACS,EAAA;AAClB,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAChC,IAAA,MAAM,OAAO,GAAG,aAAa,GAAG,CAAC,CAAC;IAElC,IAAI,gBAAgB,EAAE;AACrB,QAAA,QACCC,gBAAC,CAAA,aAAA,CAAA,gBAAgB,gBAAW,OAAO,EAAA,EACjCA,gBAAK,CAAC,YAAY,CAAC,gBAAsC,EAAE,EAAE,aAAa,EAAE,CAAC,CAC5D,EAClB;KACF;AAED,IAAA,QACCA,gBAAC,CAAA,aAAA,CAAA,gBAAgB,EAAW,EAAA,UAAA,EAAA,OAAO,UAAQ,KAAK,EAAA;QAC/CA,gBAAC,CAAA,aAAA,CAAAO,OAAK,EAAE,IAAA,EAAA,2BAA2B,CAAC,cAAc,EAAE,aAAa,EAAE,KAAK,CAAC,CAAS;AAClF,QAAAP,gBAAA,CAAA,aAAA,CAAC,cAAc,EAAE,IAAA,EAAA,cAAc,CAAkB,CAC/B,EAClB;AACH;;ACnFA,MAAM,WAAW,GAAGD,0BAAM,CAAC,GAAG,CAAA,CAAA;;;;;;;;;;GAU3B,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,KAAK,CAAA;CACnC,CAAC;AAEF,MAAM,KAAK,GAAGA,0BAAM,CAAC,GAAG,CAAA,CAAA;;UAEd,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,SAAS,CAAA;cACjC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAA;;CAEjD,CAAC;AAEF,MAAM,OAAO,GAAGA,0BAAM,CAAC,GAAG,CAAA,CAAA;;;;;;;;;CASzB,CAAC;AAaF,MAAM,MAAM,GAAG,CAAC,EACf,KAAK,EACL,OAAO,GAAG,IAAI,EACd,cAAc,EACd,cAAc,EACd,gBAAgB,EAChB,aAAa,EACb,SAAS,EACT,QAAQ,GAAG,IAAI,GACF,MACbC,gBAAC,CAAA,aAAA,CAAA,WAAW,EAAC,EAAA,SAAS,EAAC,iBAAiB,EAAC,IAAI,EAAC,SAAS,gBAAa,CAAC,EAAA;IACpEA,gBAAC,CAAA,aAAA,CAAA,KAAK,EAAE,IAAA,EAAA,KAAK,CAAS;AACrB,IAAA,OAAO,IAAIA,gBAAA,CAAA,aAAA,CAAC,OAAO,EAAA,IAAA,EAAE,OAAO,CAAW;AAEvC,IAAA,QAAQ,KACRA,gBAAC,CAAA,aAAA,CAAA,WAAW,EACX,EAAA,cAAc,EAAE,cAAc,EAC9B,cAAc,EAAE,cAAc,EAC9B,gBAAgB,EAAE,gBAAgB,EAClC,SAAS,EAAE,SAAS,EACpB,aAAa,EAAE,aAAa,EAC3B,CAAA,CACF,CACY,CACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrED,MAAM,QAAQ,GAAG;AAChB,IAAA,IAAI,EAAE,YAAY;AAClB,IAAA,KAAK,EAAE,UAAU;AACjB,IAAA,MAAM,EAAE,QAAQ;CAChB,CAAC;AAIF,MAAM,gBAAgB,GAAGD,0BAAM,CAAC,MAAM,CAGpC,CAAA;;;;;;;;oBAQkB,CAAC,EAAE,KAAK,EAAE,KAAK,QAAQ,CAAC,KAAK,CAAC,CAAA;AACpC,YAAA,EAAA,CAAC,EAAE,YAAY,EAAE,MAAM,YAAY,GAAG,MAAM,GAAG,QAAQ,CAAC,CAAA;GACnE,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC,KAAK,CAAA;CACtC,CAAC;AAQF,MAAM,SAAS,GAAG,CAAC,EAAgE,KAAiB;AAAjF,IAAA,IAAA,EAAE,KAAK,GAAG,OAAO,EAAE,WAAW,GAAG,IAAI,EAAA,GAAA,EAA2B,EAAtB,IAAI,GAA9C,MAAA,CAAA,EAAA,EAAA,CAAA,OAAA,EAAA,aAAA,CAAgD,CAAF,CAAA;AAAoC,IAAA,QACpGC,gBAAA,CAAA,aAAA,CAAC,gBAAgB,EAAA,MAAA,CAAA,MAAA,CAAA,EAAC,KAAK,EAAE,KAAK,EAAA,cAAA,EAAgB,WAAW,EAAA,EAAM,IAAI,CAAA,CAAI,EACvE;CAAA;;ACjCD,MAAM,IAAI,GAAGD,0BAAM,CAAC,GAAG,CAAA,CAAA;;;CAGtB;;ACGD,MAAM,iBAAiB,GAAGA,0BAAM,CAAC,GAAG,CAIlC,CAAA;;;;GAIC,CAAC,EAAE,WAAW,EAAE,YAAY,EAAE,KAC/B,WAAW;AACX,IAAAD,UAAG,CAAA,CAAA;;;;AAIY,eAAA,EAAA,YAAY,GAAG,MAAM,GAAG,QAAQ,CAAA;;AAE9C,EAAA,CAAA,CAAA;;AAEA,CAAA,EAAA,CAAC,EAAE,YAAY,GAAG,KAAK,EAAE,wBAAwB,GAAG,OAAO,EAAE,KAC9D,YAAY;AACZ,IAAAA,UAAG,CAAA,CAAA;iBACY,wBAAwB,CAAA;;AAEtC,EAAA,CAAA,CAAA;;GAEA,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAA;CAC9C;;AChCD,MAAM,eAAe,GAAGC,0BAAM,CAAC,GAAG,CAAA,CAAA;;;;;GAK/B,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAA;CACrC;;ACND,MAAM,OAAO,GAAGA,0BAAM,CAAC,GAAG,CAAA,CAAA;;;GAGvB,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,CAAA;CACzC;;ACHD,MAAM,cAAc,GAAGA,0BAAM,CAAC,QAAQ,CAAC,CAAA,CAAA;;GAEpC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,CAAA;CACzC;;ACJD,MAAM,aAAa,GAAGA,0BAAM,CAAC,GAAG,CAAA,CAAA;;;;GAI7B,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,KAAK,CAAA;CACnC;;ACLD,MAAM,YAAY,GAAa,OAC9BC,yBAAA,CAAA,aAAA,CAAA,KAAA,EAAA,EAAK,KAAK,EAAC,4BAA4B,EAAC,KAAK,EAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,OAAO,EAAC,WAAW,EAAA;IACjFA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,CAAC,EAAC,gBAAgB,EAAG,CAAA;IAC3BA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,CAAC,EAAC,eAAe,EAAC,IAAI,EAAC,MAAM,EAAA,CAAG,CACjC,CACN;;ACHD,MAAM,aAAa,GAAGD,0BAAM,CAAC,MAAM,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BlC,CAAC;AAEF,MAAM,aAAa,GAAGA,0BAAM,CAAC,GAAG,CAAA,CAAA;;;;;;;;;;;;;;;;;;;CAmB/B,CAAC;AAQF,MAAM,MAAM,GAAG,CAAC,EAAgD,KAAiB;QAAjE,EAAE,YAAY,EAAE,QAAQ,EAAA,GAAA,EAAwB,EAAnB,IAAI,GAAA,MAAA,CAAA,EAAA,EAAjC,4BAAmC,CAAF,CAAA;IAAiC,QACjFC,+BAAC,aAAa,EAAA,IAAA;QACbA,gBAAC,CAAA,aAAA,CAAA,aAAa,EAAC,MAAA,CAAA,MAAA,CAAA,EAAA,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAM,EAAA,IAAI,CAAI,CAAA;AAC3E,QAAAA,gBAAA,CAAA,aAAA,CAACQ,YAAY,EAAA,IAAA,CAAG,CACD,EAChB;CAAA;;ACzDD,MAAM,aAAa,GAAS,MAAK;AAChC,IAAA,MAAM,QAAQ,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC;AAE5C,IAAA,SAAS,OAAO,GAAA;QACf,OAAO;YACN,KAAK,EAAE,QAAQ,GAAG,MAAM,CAAC,UAAU,GAAG,SAAS;YAC/C,MAAM,EAAE,QAAQ,GAAG,MAAM,CAAC,WAAW,GAAG,SAAS;SACjD,CAAC;KACF;AAED,IAAA,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAGR,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAE5D,IAAAA,gBAAK,CAAC,SAAS,CAAC,MAAK;QACpB,IAAI,CAAC,QAAQ,EAAE;AACd,YAAA,OAAO,MAAM,IAAI,CAAC;SAClB;AAED,QAAA,SAAS,YAAY,GAAA;AACpB,YAAA,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;SACzB;AAED,QAAA,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAChD,OAAO,MAAM,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;KAEhE,EAAE,EAAE,CAAC,CAAC;AAEP,IAAA,OAAO,UAAU,CAAC;AACnB,CAAC;;ACjCD,MAAM,SAAS,GAAa,OAC3BA,yBAAA,CAAA,aAAA,CAAA,KAAA,EAAA,EACC,KAAK,EAAC,4BAA4B,EAClC,KAAK,EAAC,IAAI,EACV,MAAM,EAAC,IAAI,EACX,OAAO,EAAC,WAAW,EAAA,aAAA,EACP,MAAM,EAClB,IAAI,EAAC,cAAc,EAAA;IAEnBA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,CAAC,EAAC,4DAA4D,EAAG,CAAA;IACvEA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,IAAI,EAAC,MAAM,EAAC,CAAC,EAAC,mBAAmB,EAAA,CAAG,CACrC,CACN;;ACZD,MAAM,QAAQ,GAAa,OAC1BA,yBAAA,CAAA,aAAA,CAAA,KAAA,EAAA,EACC,KAAK,EAAC,4BAA4B,EAClC,KAAK,EAAC,IAAI,EACV,MAAM,EAAC,IAAI,EACX,OAAO,EAAC,WAAW,EAAA,aAAA,EACP,MAAM,EAClB,IAAI,EAAC,cAAc,EAAA;IAEnBA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,CAAC,EAAC,4DAA4D,EAAG,CAAA;IACvEA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,IAAI,EAAC,MAAM,EAAC,CAAC,EAAC,iBAAiB,EAAA,CAAG,CACnC,CACN;;ACZD,MAAM,IAAI,GAAa,OACtBA,yBAAA,CAAA,aAAA,CAAA,KAAA,EAAA,EACC,KAAK,EAAC,4BAA4B,EAClC,KAAK,EAAC,IAAI,EACV,MAAM,EAAC,IAAI,EACX,OAAO,EAAC,WAAW,EAAA,aAAA,EACP,MAAM,EAClB,IAAI,EAAC,cAAc,EAAA;IAEnBA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,CAAC,EAAC,+CAA+C,EAAG,CAAA;IAC1DA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,CAAC,EAAC,eAAe,EAAC,IAAI,EAAC,MAAM,EAAA,CAAG,CACjC,CACN;;ACZD,MAAM,KAAK,GAAa,OACvBA,yBAAA,CAAA,aAAA,CAAA,KAAA,EAAA,EACC,KAAK,EAAC,4BAA4B,EAClC,KAAK,EAAC,IAAI,EACV,MAAM,EAAC,IAAI,EACX,OAAO,EAAC,WAAW,EAAA,aAAA,EACP,MAAM,EAClB,IAAI,EAAC,cAAc,EAAA;IAEnBA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,CAAC,EAAC,gDAAgD,EAAG,CAAA;IAC3DA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,CAAC,EAAC,eAAe,EAAC,IAAI,EAAC,MAAM,EAAA,CAAG,CACjC,CACN;;ACZD,MAAM,qBAAqB,GAAa,OACvCA,yBAAK,CAAA,aAAA,CAAA,KAAA,EAAA,EAAA,IAAI,EAAC,cAAc,EAAC,MAAM,EAAC,IAAI,EAAC,OAAO,EAAC,WAAW,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,4BAA4B,EAAA;IACrGA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,CAAC,EAAC,kDAAkD,EAAG,CAAA;IAC7DA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,CAAC,EAAC,iBAAiB,EAAC,IAAI,EAAC,MAAM,EAAA,CAAG,CACnC,CACN;;ACLD,MAAM,oBAAoB,GAAa,OACtCA,yBAAK,CAAA,aAAA,CAAA,KAAA,EAAA,EAAA,IAAI,EAAC,cAAc,EAAC,MAAM,EAAC,IAAI,EAAC,OAAO,EAAC,WAAW,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,4BAA4B,EAAA;IACrGA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,CAAC,EAAC,iDAAiD,EAAG,CAAA;IAC5DA,yBAAM,CAAA,aAAA,CAAA,MAAA,EAAA,EAAA,CAAC,EAAC,iBAAiB,EAAC,IAAI,EAAC,MAAM,EAAA,CAAG,CACnC,CACN;;ACGM,MAAM,YAAY,GAAG;AAC3B,IAAA,OAAO,EAAE,EAAE;AACX,IAAA,IAAI,EAAE,EAAE;AACR,IAAA,KAAK,EAAE,EAAE;AACT,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,cAAc,EAAE,KAAK;AACrB,IAAA,uBAAuB,EAAE,KAAK;AAC9B,IAAA,yBAAyB,EAAE,KAAK;AAChC,IAAA,qBAAqB,EAAE,IAAI;AAC3B,IAAA,qBAAqB,EAAE,IAAI;AAC3B,IAAA,uBAAuB,EAAE,OAAgB;AACzC,IAAA,4BAA4B,EAAE,EAAE;AAChC,IAAA,yBAAyB,EAAE,KAAK;AAChC,IAAA,oBAAoB,EAAE,KAAK;AAC3B,IAAA,iBAAiB,EAAE,KAAK;AACxB,IAAA,cAAc,EAAE,KAAK;AACrB,IAAA,qBAAqB,EAAE,IAAI;AAC3B,IAAA,qBAAqB,EAAE,IAAI;AAC3B,IAAA,kBAAkB,EAAE,KAAK;AACzB,IAAA,0BAA0B,EAAE,KAAK;AACjC,IAAA,wBAAwB,EAAE,KAAK;AAC/B,IAAA,kCAAkC,EAAE,KAAK;IACzC,uBAAuB,EAAE,SAAS,eAAe,GAAA;AAChD,QAAA,QACCA,yBAAA,CAAA,aAAA,CAAA,KAAA,EAAA,IAAA;;YACqDA,yBAAwC,CAAA,aAAA,CAAA,QAAA,EAAA,IAAA,EAAA,yBAAA,CAAA;AAEvF,YAAA,uDAAA,CAAA,EACL;KACF;AACD,IAAA,cAAc,EAAE;QACf,SAAS,EAAEA,yBAAC,CAAA,aAAA,CAAA,qBAAqB,EAAG,IAAA,CAAA;QACpC,QAAQ,EAAEA,yBAAC,CAAA,aAAA,CAAA,oBAAoB,EAAG,IAAA,CAAA;AAClC,KAAA;AACD,IAAA,4BAA4B,EAAE,EAAE;AAChC,IAAA,eAAe,EAAE,KAAK;AACtB,IAAA,iBAAiB,EAAEA,yBAAK,CAAA,aAAA,CAAA,KAAA,EAAA,EAAA,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,EAAkB,EAAA,YAAA,CAAA;AACvG,IAAA,gBAAgB,EAAE,KAAK;AACvB,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,YAAY,EAAE,IAAI;AAClB,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,OAAO,EAAE,KAAK;AACd,IAAA,gBAAgB,EAAE,KAAK;AACvB,IAAA,cAAc,EAAE,KAAK;AACrB,IAAA,aAAa,EAAE,KAAK;AACpB,IAAA,cAAc,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE;AAC1E,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,cAAc,EAAE,IAAI;AACpB,IAAA,gBAAgB,EAAE,IAAI;AACtB,IAAA,kBAAkB,EAAE,IAAI;AACxB,IAAA,cAAc,EAAE,IAAI;AACpB,IAAA,UAAU,EAAE,IAAI;IAChB,eAAe,EAAEA,iDAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EAAuC,EAAA,iCAAA,CAAA;AACvF,IAAA,QAAQ,EAAE,KAAK;AACf,IAAA,WAAW,EAAE,KAAK;AAClB,IAAA,QAAQ,EAAE,KAAK;AACf,IAAA,SAAS,EAAE,KAAK;IAChB,cAAc,EAAEG,iBAAS,CAAC,KAAK;AAC/B,IAAA,aAAa,EAAE,IAAI;AACnB,IAAA,kBAAkB,EAAE,IAAI;AACxB,IAAA,WAAW,EAAE,KAAK;AAClB,IAAA,uBAAuB,EAAE,OAAO;AAChC,IAAA,UAAU,EAAE,KAAK;AACjB,IAAA,gBAAgB,EAAE,KAAK;AACvB,IAAA,uBAAuB,EAAE;AACxB,QAAA,qBAAqB,EAAE,KAAK;AAC5B,QAAA,2BAA2B,EAAE,KAAK;AAClC,KAAA;AACD,IAAA,qBAAqB,EAAE,CAAC;AACxB,IAAA,0BAA0B,EAAE,KAAK;AACjC,IAAA,mBAAmB,EAAE,CAAC;AACtB,IAAA,iBAAiB,EAAE,EAAE;IACrB,4BAA4B,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAClD,IAAA,mBAAmB,EAAE,IAAI;AACzB,IAAA,0BAA0B,EAAE,EAAE;IAC9B,uBAAuB,EAAEH,yBAAC,CAAA,aAAA,CAAAS,SAAa,EAAG,IAAA,CAAA;IAC1C,sBAAsB,EAAET,yBAAC,CAAA,aAAA,CAAAU,QAAY,EAAG,IAAA,CAAA;IACxC,kBAAkB,EAAEV,yBAAC,CAAA,aAAA,CAAAW,KAAS,EAAG,IAAA,CAAA;IACjC,sBAAsB,EAAEX,yBAAC,CAAA,aAAA,CAAAY,IAAQ,EAAG,IAAA,CAAA;AACpC,IAAA,KAAK,EAAE,KAAK;AACZ,IAAA,oBAAoB,EAAE,EAAE;AACxB,IAAA,KAAK,EAAE,SAAkB;AACzB,IAAA,YAAY,EAAE,EAAE;IAChB,SAAS,EAAEV,iBAAS,CAAC,IAAI;AACzB,IAAA,YAAY,EAAE,IAAI;AAClB,IAAA,mBAAmB,EAAE,IAAI;AACzB,IAAA,YAAY,EAAE,IAAI;AAClB,IAAA,kBAAkB,EAAE,IAAI;AACxB,IAAA,eAAe,EAAE,IAAI;AACrB,IAAA,eAAe,EAAE,IAAI;AACrB,IAAA,kBAAkB,EAAE,IAAI;AACxB,IAAA,oBAAoB,EAAE,IAAI;AAC1B,IAAA,MAAM,EAAE,IAAI;AACZ,IAAA,mBAAmB,EAAE,IAAI;CACzB;;AC7FD,MAAM,uBAAuB,GAAG;AAC/B,IAAA,eAAe,EAAE,gBAAgB;AACjC,IAAA,kBAAkB,EAAE,IAAI;AACxB,IAAA,aAAa,EAAE,KAAK;AACpB,IAAA,iBAAiB,EAAE,KAAK;AACxB,IAAA,qBAAqB,EAAE,KAAK;CAC5B,CAAC;AAEF,MAAM,iBAAiB,GAAGH,0BAAM,CAAC,GAAG,CAAA,CAAA;;;;;;;;;GASjC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,UAAU,CAAC,KAAK,CAAA;CACvC,CAAC;AAEF,MAAM,MAAM,GAAGA,0BAAM,CAAC,MAAM,CAE1B,CAAA;;;;;GAKC,CAAC,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC,UAAU,CAAC,gBAAgB,CAAA;GAChD,CAAC,EAAE,MAAM,EAAE,KAAK,MAAM,IAAI,0BAA0B,CAAA;CACtD,CAAC;AAEF,MAAM,QAAQ,GAAGA,0BAAM,CAAC,GAAG,CAAA,CAAA;;;;;GAKxB,KAAK,CAAC,EAAE,CAAA,CAAA;;;AAGR,EAAA,CAAA,CAAA;CACF,CAAC;AAEF,MAAM,IAAI,GAAGA,0BAAM,CAAC,IAAI,CAAA,CAAA;;;CAGvB,CAAC;AAEF,MAAM,KAAK,GAAGA,0BAAM,CAAC,IAAI,CAAC,CAAA,CAAA;;CAEzB,CAAC;AAEF,MAAM,QAAQ,GAAGA,0BAAM,CAAC,IAAI,CAAC,CAAA,CAAA;;CAE5B,CAAC;AAiBF,SAAS,UAAU,CAAC,EACnB,WAAW,EACX,QAAQ,EACR,WAAW,EACX,SAAS,GAAG,YAAY,CAAC,SAAS,EAClC,4BAA4B,GAAG,YAAY,CAAC,4BAA4B,EACxE,sBAAsB,GAAG,YAAY,CAAC,sBAAsB,EAC5D,uBAAuB,GAAG,YAAY,CAAC,uBAAuB,EAC9D,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,EACpD,sBAAsB,GAAG,YAAY,CAAC,sBAAsB,EAC5D,0BAA0B,GAAG,YAAY,CAAC,0BAA0B,EACpE,mBAAmB,GAAG,YAAY,CAAC,mBAAmB,EACtD,YAAY,GAAG,YAAY,CAAC,YAAY,GACvB,EAAA;AACjB,IAAA,MAAM,UAAU,GAAG,aAAa,EAAE,CAAC;AACnC,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;IAChC,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;IAEhE,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AACzD,IAAA,MAAM,SAAS,GAAG,WAAW,GAAG,WAAW,CAAC;AAC5C,IAAA,MAAM,UAAU,GAAG,SAAS,GAAG,WAAW,GAAG,CAAC,CAAC;AAC/C,IAAA,MAAM,cAAc,GAAG,WAAW,KAAK,CAAC,CAAC;AACzC,IAAA,MAAM,eAAe,GAAG,WAAW,KAAK,QAAQ,CAAC;AACjD,IAAA,MAAM,OAAO,GAAQ,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,uBAAuB,CAAK,EAAA,0BAA0B,CAAE,CAAC;AAC9E,IAAA,MAAM,KAAK,GACV,WAAW,KAAK,QAAQ;UACrB,CAAG,EAAA,UAAU,CAAI,CAAA,EAAA,QAAQ,CAAI,CAAA,EAAA,OAAO,CAAC,kBAAkB,CAAI,CAAA,EAAA,QAAQ,CAAE,CAAA;AACvE,UAAE,CAAA,EAAG,UAAU,CAAA,CAAA,EAAI,SAAS,CAAA,CAAA,EAAI,OAAO,CAAC,kBAAkB,CAAA,CAAA,EAAI,QAAQ,CAAA,CAAE,CAAC;IAE3E,MAAM,cAAc,GAAGC,gBAAK,CAAC,WAAW,CAAC,MAAM,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;IAC3G,MAAM,UAAU,GAAGA,gBAAK,CAAC,WAAW,CAAC,MAAM,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;AACvG,IAAA,MAAM,WAAW,GAAGA,gBAAK,CAAC,WAAW,CAAC,MAAM,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;IAC7E,MAAM,UAAU,GAAGA,gBAAK,CAAC,WAAW,CACnC,MAAM,YAAY,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,EAC3D,CAAC,YAAY,EAAE,QAAQ,EAAE,WAAW,CAAC,CACrC,CAAC;AACF,IAAA,MAAM,iBAAiB,GAAGA,gBAAK,CAAC,WAAW,CAC1C,CAAC,CAAuC,KAAK,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,EACrG,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAClC,CAAC;IAEF,MAAM,aAAa,GAAG,4BAA4B,CAAC,GAAG,CAAC,CAAC,GAAW,MAClEA,2CAAQ,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAA,EAC1B,GAAG,CACI,CACT,CAAC,CAAC;AAEH,IAAA,IAAI,OAAO,CAAC,iBAAiB,EAAE;AAC9B,QAAA,aAAa,CAAC,IAAI,CACjBA,2CAAQ,GAAG,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,QAAQ,EAC9B,EAAA,OAAO,CAAC,qBAAqB,CACtB,CACT,CAAC;KACF;IAED,MAAM,MAAM,IACXA,gBAAA,CAAA,aAAA,CAAC,MAAM,EAAC,EAAA,QAAQ,EAAE,iBAAiB,EAAE,YAAY,EAAE,WAAW,gBAAc,OAAO,CAAC,eAAe,EACjG,EAAA,aAAa,CACN,CACT,CAAC;AAEF,IAAA,QACCA,gBAAC,CAAA,aAAA,CAAA,iBAAiB,EAAC,EAAA,SAAS,EAAC,gBAAgB,EAAA;AAC3C,QAAA,CAAC,OAAO,CAAC,aAAa,IAAI,UAAU,KACpCA,gBAAA,CAAA,aAAA,CAAAA,gBAAA,CAAA,QAAA,EAAA,IAAA;AACC,YAAAA,gBAAA,CAAA,aAAA,CAAC,QAAQ,EAAA,IAAA,EAAE,OAAO,CAAC,eAAe,CAAY;AAC7C,YAAA,MAAM,CACL,CACH;AACA,QAAA,UAAU,IAAIA,gBAAA,CAAA,aAAA,CAAC,KAAK,EAAA,IAAA,EAAE,KAAK,CAAS;AACrC,QAAAA,gBAAA,CAAA,aAAA,CAAC,QAAQ,EAAA,IAAA;YACRA,gBAAC,CAAA,aAAA,CAAA,MAAM,EACN,EAAA,EAAE,EAAC,uBAAuB,EAC1B,IAAI,EAAC,QAAQ,EAAA,YAAA,EACF,YAAY,EAAA,eAAA,EACR,cAAc,EAC7B,OAAO,EAAE,WAAW,EACpB,QAAQ,EAAE,cAAc,EAChB,QAAA,EAAA,KAAK,EAEZ,EAAA,uBAAuB,CAChB;YAETA,gBAAC,CAAA,aAAA,CAAA,MAAM,EACN,EAAA,EAAE,EAAC,0BAA0B,EAC7B,IAAI,EAAC,QAAQ,EAAA,YAAA,EACF,eAAe,EAAA,eAAA,EACX,cAAc,EAC7B,OAAO,EAAE,cAAc,EACvB,QAAQ,EAAE,cAAc,EAChB,QAAA,EAAA,KAAK,EAEZ,EAAA,sBAAsB,CACf;AAER,YAAA,CAAC,OAAO,CAAC,aAAa,IAAI,CAAC,UAAU,IAAI,MAAM;YAEhDA,gBAAC,CAAA,aAAA,CAAA,MAAM,EACN,EAAA,EAAE,EAAC,sBAAsB,EACzB,IAAI,EAAC,QAAQ,EAAA,YAAA,EACF,WAAW,EAAA,eAAA,EACP,eAAe,EAC9B,OAAO,EAAE,UAAU,EACnB,QAAQ,EAAE,eAAe,EACjB,QAAA,EAAA,KAAK,EAEZ,EAAA,kBAAkB,CACX;AAET,YAAAA,gBAAA,CAAA,aAAA,CAAC,MAAM,EAAA,EACN,EAAE,EAAC,sBAAsB,EACzB,IAAI,EAAC,QAAQ,EAAA,YAAA,EACF,WAAW,EAAA,eAAA,EACP,eAAe,EAC9B,OAAO,EAAE,UAAU,EACnB,QAAQ,EAAE,eAAe,EAAA,QAAA,EACjB,KAAK,EAAA,EAEZ,sBAAsB,CACf,CACC,CACQ,EACnB;AACH,CAAC;AAED,uBAAeA,gBAAK,CAAC,IAAI,CAAC,UAAU,CAAC;;AC1MrC,MAAM,cAAc,GAAS,CAAC,EAAE,EAAE,MAAM,KAAI;IAC3C,MAAM,WAAW,GAAGA,gBAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAEvC,IAAAA,gBAAK,CAAC,SAAS,CAAC,MAAK;AACpB,QAAA,IAAI,WAAW,CAAC,OAAO,EAAE;AACxB,YAAA,WAAW,CAAC,OAAO,GAAG,KAAK,CAAC;YAC5B,OAAO;SACP;AAED,QAAA,EAAE,EAAE,CAAC;KAEL,EAAE,MAAM,CAAC,CAAC;AACZ,CAAC;;;;;;ACdD,IAAI,iBAAiB,GAAG,SAAS,iBAAiB,CAAC,KAAK,EAAE;AAC1D,CAAC,OAAO,eAAe,CAAC,KAAK,CAAC;AAC9B,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;AACtB,CAAC,CAAC;AACF;AACA,SAAS,eAAe,CAAC,KAAK,EAAE;AAChC,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ;AAC5C,CAAC;AACD;AACA,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,CAAC,IAAI,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzD;AACA,CAAC,OAAO,WAAW,KAAK,iBAAiB;AACzC,KAAK,WAAW,KAAK,eAAe;AACpC,KAAK,cAAc,CAAC,KAAK,CAAC;AAC1B,CAAC;AACD;AACA;AACA,IAAI,YAAY,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,GAAG,CAAC;AAC9D,IAAI,kBAAkB,GAAG,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC;AAC7E;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,CAAC,OAAO,KAAK,CAAC,QAAQ,KAAK,kBAAkB;AAC7C,CAAC;AACD;AACA,SAAS,WAAW,CAAC,GAAG,EAAE;AAC1B,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE;AACpC,CAAC;AACD;AACA,SAAS,6BAA6B,CAAC,KAAK,EAAE,OAAO,EAAE;AACvD,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,IAAI,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC;AACpE,IAAI,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC;AACjD,IAAI,KAAK;AACT,CAAC;AACD;AACA,SAAS,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE;AACpD,CAAC,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,OAAO,EAAE;AACpD,EAAE,OAAO,6BAA6B,CAAC,OAAO,EAAE,OAAO,CAAC;AACxD,EAAE,CAAC;AACH,CAAC;AACD;AACA,SAAS,gBAAgB,CAAC,GAAG,EAAE,OAAO,EAAE;AACxC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;AAC3B,EAAE,OAAO,SAAS;AAClB,EAAE;AACF,CAAC,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAC5C,CAAC,OAAO,OAAO,WAAW,KAAK,UAAU,GAAG,WAAW,GAAG,SAAS;AACnE,CAAC;AACD;AACA,SAAS,+BAA+B,CAAC,MAAM,EAAE;AACjD,CAAC,OAAO,MAAM,CAAC,qBAAqB;AACpC,IAAI,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,SAAS,MAAM,EAAE;AACjE,GAAG,OAAO,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1D,GAAG,CAAC;AACJ,IAAI,EAAE;AACN,CAAC;AACD;AACA,SAAS,OAAO,CAAC,MAAM,EAAE;AACzB,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,+BAA+B,CAAC,MAAM,CAAC,CAAC;AAC3E,CAAC;AACD;AACA,SAAS,kBAAkB,CAAC,MAAM,EAAE,QAAQ,EAAE;AAC9C,CAAC,IAAI;AACL,EAAE,OAAO,QAAQ,IAAI,MAAM;AAC3B,EAAE,CAAC,MAAM,CAAC,EAAE;AACZ,EAAE,OAAO,KAAK;AACd,EAAE;AACF,CAAC;AACD;AACA;AACA,SAAS,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;AACvC,CAAC,OAAO,kBAAkB,CAAC,MAAM,EAAE,GAAG,CAAC;AACvC,KAAK,EAAE,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC;AAC9C,MAAM,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AACpD,CAAC;AACD;AACA,SAAS,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE;AAC9C,CAAC,IAAI,WAAW,GAAG,EAAE,CAAC;AACtB,CAAC,IAAI,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE;AACxC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,GAAG,EAAE;AACxC,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,6BAA6B,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;AAC1E,GAAG,CAAC,CAAC;AACL,EAAE;AACF,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,GAAG,EAAE;AACvC,EAAE,IAAI,gBAAgB,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;AACrC,GAAG,MAAM;AACT,GAAG;AACH;AACA,EAAE,IAAI,kBAAkB,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;AACjF,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;AACxF,GAAG,MAAM;AACT,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,6BAA6B,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;AAC1E,GAAG;AACH,EAAE,CAAC,CAAC;AACJ,CAAC,OAAO,WAAW;AACnB,CAAC;AACD;AACA,SAAS,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE;AAC5C,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;AACzB,CAAC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,iBAAiB,CAAC;AAC9D,CAAC,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,IAAI,iBAAiB,CAAC;AAC5E;AACA;AACA,CAAC,OAAO,CAAC,6BAA6B,GAAG,6BAA6B,CAAC;AACvE;AACA,CAAC,IAAI,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3C,CAAC,IAAI,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3C,CAAC,IAAI,yBAAyB,GAAG,aAAa,KAAK,aAAa,CAAC;AACjE;AACA,CAAC,IAAI,CAAC,yBAAyB,EAAE;AACjC,EAAE,OAAO,6BAA6B,CAAC,MAAM,EAAE,OAAO,CAAC;AACvD,EAAE,MAAM,IAAI,aAAa,EAAE;AAC3B,EAAE,OAAO,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;AACpD,EAAE,MAAM;AACR,EAAE,OAAO,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;AAC7C,EAAE;AACF,CAAC;AACD;AACA,SAAS,CAAC,GAAG,GAAG,SAAS,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE;AACtD,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC5B,EAAE,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC;AACtD,EAAE;AACF;AACA,CAAC,OAAO,KAAK,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE;AAC1C,EAAE,OAAO,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC;AACvC,EAAE,EAAE,EAAE,CAAC;AACP,CAAC,CAAC;AACF;AACA,IAAI,WAAW,GAAG,SAAS,CAAC;AAC5B;AACA,IAAA,GAAc,GAAG,WAAW,CAAA;;;;AC7H5B,MAAM,YAAY,GAAG;AACpB,IAAA,IAAI,EAAE;AACL,QAAA,OAAO,EAAE,qBAAqB;AAC9B,QAAA,SAAS,EAAE,qBAAqB;AAChC,QAAA,QAAQ,EAAE,qBAAqB;AAC/B,KAAA;AACD,IAAA,UAAU,EAAE;AACX,QAAA,OAAO,EAAE,SAAS;AAClB,KAAA;AACD,IAAA,OAAO,EAAE;AACR,QAAA,UAAU,EAAE,SAAS;AACrB,QAAA,IAAI,EAAE,qBAAqB;AAC3B,KAAA;AACD,IAAA,OAAO,EAAE;AACR,QAAA,OAAO,EAAE,iBAAiB;AAC1B,KAAA;AACD,IAAA,MAAM,EAAE;AACP,QAAA,OAAO,EAAE,iBAAiB;AAC1B,QAAA,KAAK,EAAE,iBAAiB;AACxB,QAAA,KAAK,EAAE,iBAAiB;AACxB,QAAA,QAAQ,EAAE,oBAAoB;AAC9B,KAAA;AACD,IAAA,QAAQ,EAAE;AACT,QAAA,OAAO,EAAE,SAAS;AAClB,QAAA,IAAI,EAAE,qBAAqB;AAC3B,KAAA;AACD,IAAA,gBAAgB,EAAE;AACjB,QAAA,OAAO,EAAE,SAAS;AAClB,QAAA,IAAI,EAAE,qBAAqB;AAC3B,KAAA;AACD,IAAA,OAAO,EAAE;AACR,QAAA,OAAO,EAAE,SAAS;AAClB,QAAA,IAAI,EAAE,qBAAqB;AAC3B,KAAA;CACD,CAAC;AAEW,MAAA,aAAa,GAAiB;AAC1C,IAAA,OAAO,EAAE,YAAY;AACrB,IAAA,KAAK,EAAE,YAAY;AACnB,IAAA,IAAI,EAAE;AACL,QAAA,IAAI,EAAE;AACL,YAAA,OAAO,EAAE,SAAS;AAClB,YAAA,SAAS,EAAE,0BAA0B;AACrC,YAAA,QAAQ,EAAE,iBAAiB;AAC3B,SAAA;AACD,QAAA,UAAU,EAAE;AACX,YAAA,OAAO,EAAE,SAAS;AAClB,SAAA;AACD,QAAA,OAAO,EAAE;AACR,YAAA,UAAU,EAAE,SAAS;AACrB,YAAA,IAAI,EAAE,SAAS;AACf,SAAA;AACD,QAAA,OAAO,EAAE;AACR,YAAA,OAAO,EAAE,qBAAqB;AAC9B,SAAA;AACD,QAAA,MAAM,EAAE;AACP,YAAA,OAAO,EAAE,SAAS;AAClB,YAAA,KAAK,EAAE,0BAA0B;AACjC,YAAA,KAAK,EAAE,0BAA0B;AACjC,YAAA,QAAQ,EAAE,0BAA0B;AACpC,SAAA;AACD,QAAA,QAAQ,EAAE;AACT,YAAA,OAAO,EAAE,mBAAmB;AAC5B,YAAA,IAAI,EAAE,SAAS;AACf,SAAA;AACD,QAAA,gBAAgB,EAAE;AACjB,YAAA,OAAO,EAAE,mBAAmB;AAC5B,YAAA,IAAI,EAAE,SAAS;AACf,SAAA;AACD,QAAA,OAAO,EAAE;AACR,YAAA,OAAO,EAAE,oBAAoB;AAC7B,YAAA,IAAI,EAAE,SAAS;AACf,SAAA;AACD,KAAA;EACA;AAEI,SAAU,WAAW,CAAI,IAAI,GAAG,SAAS,EAAE,WAAe,EAAE,OAAA,GAAkB,SAAS,EAAA;AAC5F,IAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AACzB,QAAA,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,WAAW,IAAI,EAAE,CAAC,CAAC;KACvE;AAGD,IAAA,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,WAAW,IAAI,EAAE,CAAC,CAAC;AAEpE,IAAA,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;AAC5B;;ACxFO,MAAM,aAAa,GAAG,CAAC,KAAY,MAAmB;AAC5D,IAAA,KAAK,EAAE;AACN,QAAA,KAAK,EAAE;AACN,YAAA,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO;AACzB,YAAA,eAAe,EAAE,KAAK,CAAC,UAAU,CAAC,OAAO;AACzC,SAAA;AACD,KAAA;AACD,IAAA,YAAY,EAAE;AACb,QAAA,KAAK,EAAE;AACN,YAAA,OAAO,EAAE,OAAO;AAChB,SAAA;AACD,KAAA;AACD,IAAA,iBAAiB,EAAE;AAClB,QAAA,KAAK,EAAE,EAAE;AACT,KAAA;AACD,IAAA,MAAM,EAAE;AACP,QAAA,KAAK,EAAE;AACN,YAAA,QAAQ,EAAE,MAAM;AAChB,YAAA,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO;AACzB,YAAA,eAAe,EAAE,KAAK,CAAC,UAAU,CAAC,OAAO;AACzC,YAAA,SAAS,EAAE,MAAM;AACjB,YAAA,WAAW,EAAE,MAAM;AACnB,YAAA,YAAY,EAAE,KAAK;AACnB,SAAA;AACD,KAAA;AACD,IAAA,SAAS,EAAE;AACV,QAAA,KAAK,EAAE;AACN,YAAA,eAAe,EAAE,KAAK,CAAC,UAAU,CAAC,OAAO;AACzC,YAAA,SAAS,EAAE,MAAM;AACjB,SAAA;AACD,KAAA;AACD,IAAA,IAAI,EAAE;AACL,QAAA,KAAK,EAAE;AACN,YAAA,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO;AACzB,YAAA,QAAQ,EAAE,MAAM;AAChB,YAAA,UAAU,EAAE,GAAG;AACf,SAAA;AACD,KAAA;AACD,IAAA,OAAO,EAAE;AACR,QAAA,KAAK,EAAE;AACN,YAAA,eAAe,EAAE,KAAK,CAAC,UAAU,CAAC,OAAO;AACzC,YAAA,SAAS,EAAE,MAAM;AACjB,YAAA,iBAAiB,EAAE,KAAK;AACxB,YAAA,iBAAiB,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO;AACxC,YAAA,iBAAiB,EAAE,OAAO;AAC1B,SAAA;AACD,QAAA,UAAU,EAAE;AACX,YAAA,SAAS,EAAE,MAAM;AACjB,SAAA;AACD,KAAA;AACD,IAAA,SAAS,EAAE;AACV,QAAA,KAAK,EAAE;AACN,YAAA,WAAW,EAAE,MAAM;AACnB,YAAA,YAAY,EAAE,MAAM;AACpB,SAAA;AACD,QAAA,aAAa,EAAE;AACd,YAAA,MAAM,EAAE,MAAM;AACd,SAAA;AACD,KAAA;AACD,IAAA,WAAW,EAAE;AACZ,QAAA,KAAK,EAAE;AACN,YAAA,eAAe,EAAE,KAAK,CAAC,OAAO,CAAC,UAAU;AACzC,YAAA,QAAQ,EAAE,MAAM;AAChB,YAAA,UAAU,EAAE,GAAG;AACf,YAAA,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI;AACzB,YAAA,WAAW,EAAE,MAAM;AACnB,YAAA,YAAY,EAAE,KAAK;AACnB,YAAA,SAAS,EAAE,0BAA0B;AACrC,YAAA,kBAAkB,EAAE,OAAO;AAC3B,YAAA,wBAAwB,EAAE,4BAA4B;AACtD,YAAA,UAAU,EAAE,WAAW;AACvB,SAAA;AACD,QAAA,WAAW,EAAE;AACZ,YAAA,SAAS,EAAE,sBAAsB;AACjC,SAAA;AACD,KAAA;AACD,IAAA,KAAK,EAAE;AACN,QAAA,KAAK,EAAE;AACN,YAAA,WAAW,EAAE,MAAM;AACnB,YAAA,YAAY,EAAE,MAAM;AACpB,YAAA,SAAS,EAAE,YAAY;AACvB,SAAA;AACD,QAAA,aAAa,EAAE,EAAE;AACjB,KAAA;AACD,IAAA,IAAI,EAAE;AACL,QAAA,KAAK,EAAE;AACN,YAAA,QAAQ,EAAE,MAAM;AAChB,YAAA,UAAU,EAAE,GAAG;AACf,YAAA,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO;AACzB,YAAA,eAAe,EAAE,KAAK,CAAC,UAAU,CAAC,OAAO;AACzC,YAAA,SAAS,EAAE,MAAM;AACjB,YAAA,sBAAsB,EAAE;AACvB,gBAAA,iBAAiB,EAAE,OAAO;AAC1B,gBAAA,iBAAiB,EAAE,KAAK;AACxB,gBAAA,iBAAiB,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO;AACxC,aAAA;AACD,SAAA;AACD,QAAA,UAAU,EAAE;AACX,YAAA,SAAS,EAAE,MAAM;AACjB,SAAA;AACD,QAAA,sBAAsB,EAAE;AAEvB,YAAA,kBAAkB,EAAE;AACnB,gBAAA,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI;AAC1B,gBAAA,eAAe,EAAE,KAAK,CAAC,QAAQ,CAAC,OAAO;AACvC,gBAAA,iBAAiB,EAAE,KAAK,CAAC,UAAU,CAAC,OAAO;AAC3C,aAAA;AACD,SAAA;AACD,QAAA,qBAAqB,EAAE;AACtB,YAAA,KAAK,EAAE,KAAK,CAAC,gBAAgB,CAAC,IAAI;AAClC,YAAA,eAAe,EAAE,KAAK,CAAC,gBAAgB,CAAC,OAAO;AAC/C,YAAA,kBAAkB,EAAE,OAAO;AAC3B,YAAA,kBAAkB,EAAE,kBAAkB;AACtC,YAAA,iBAAiB,EAAE,KAAK,CAAC,UAAU,CAAC,OAAO;AAC3C,YAAA,YAAY,EAAE,OAAO;AACrB,YAAA,YAAY,EAAE,KAAK;AACnB,YAAA,YAAY,EAAE,KAAK,CAAC,UAAU,CAAC,OAAO;AACtC,SAAA;AACD,QAAA,YAAY,EAAE;AACb,YAAA,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI;AACzB,YAAA,eAAe,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO;AACtC,SAAA;AACD,KAAA;AACD,IAAA,WAAW,EAAE;AACZ,QAAA,KAAK,EAAE;AACN,YAAA,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO;AACzB,YAAA,eAAe,EAAE,KAAK,CAAC,UAAU,CAAC,OAAO;AACzC,SAAA;AACD,KAAA;AACD,IAAA,YAAY,EAAE;AACb,QAAA,KAAK,EAAE;AACN,YAAA,IAAI,EAAE,UAAU;AAChB,SAAA;AACD,KAAA;AACD,IAAA,cAAc,EAAE;AACf,QAAA,KAAK,EAAE;AACN,YAAA,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO;AAC3B,YAAA,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO;AAC1B,YAAA,eAAe,EAAE,aAAa;AAC9B,YAAA,YAAY,EAAE,KAAK;AACnB,YAAA,UAAU,EAAE,OAAO;AACnB,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,KAAK,EAAE,MAAM;AACb,YAAA,iBAAiB,EAAE;AAClB,gBAAA,MAAM,EAAE,SAAS;AACjB,aAAA;AACD,YAAA,YAAY,EAAE;AACb,gBAAA,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ;AAC5B,aAAA;AACD,YAAA,wBAAwB,EAAE;AACzB,gBAAA,MAAM,EAAE,SAAS;AACjB,gBAAA,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK;AACnC,aAAA;AACD,YAAA,SAAS,EAAE;AACV,gBAAA,OAAO,EAAE,MAAM;AACf,gBAAA,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK;AACnC,aAAA;AACD,YAAA,GAAG,EAAE;AACJ,gBAAA,MAAM,EAAE,MAAM;AACd,aAAA;AACD,SAAA;AACD,KAAA;AACD,IAAA,UAAU,EAAE;AACX,QAAA,KAAK,EAAE;AACN,YAAA,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS;AAC3B,YAAA,QAAQ,EAAE,MAAM;AAChB,YAAA,SAAS,EAAE,MAAM;AACjB,YAAA,eAAe,EAAE,KAAK,CAAC,UAAU,CAAC,OAAO;AACzC,YAAA,cAAc,EAAE,OAAO;AACvB,YAAA,cAAc,EAAE,KAAK;AACrB,YAAA,cAAc,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO;AACrC,SAAA;AACD,QAAA,gBAAgB,EAAE;AACjB,YAAA,YAAY,EAAE,KAAK;AACnB,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,KAAK,EAAE,MAAM;AACb,YAAA,OAAO,EAAE,KAAK;AACd,YAAA,MAAM,EAAE,IAAI;AACZ,YAAA,MAAM,EAAE,SAAS;AACjB,YAAA,UAAU,EAAE,MAAM;AAClB,YAAA,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO;AAC3B,YAAA,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO;AAC1B,YAAA,eAAe,EAAE,aAAa;AAC9B,YAAA,YAAY,EAAE;AACb,gBAAA,MAAM,EAAE,OAAO;AACf,gBAAA,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ;AAC5B,gBAAA,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ;AAC3B,aAAA;AACD,YAAA,wBAAwB,EAAE;AACzB,gBAAA,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK;AACnC,aAAA;AACD,YAAA,SAAS,EAAE;AACV,gBAAA,OAAO,EAAE,MAAM;AACf,gBAAA,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK;AACnC,aAAA;AACD,SAAA;AACD,KAAA;AACD,IAAA,MAAM,EAAE;AACP,QAAA,KAAK,EAAE;AACN,YAAA,OAAO,EAAE,MAAM;AACf,YAAA,UAAU,EAAE,QAAQ;AACpB,YAAA,cAAc,EAAE,QAAQ;AACxB,YAAA,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO;AACzB,YAAA,eAAe,EAAE,KAAK,CAAC,UAAU,CAAC,OAAO;AACzC,SAAA;AACD,KAAA;AACD,IAAA,QAAQ,EAAE;AACT,QAAA,KAAK,EAAE;AACN,YAAA,OAAO,EAAE,MAAM;AACf,YAAA,UAAU,EAAE,QAAQ;AACpB,YAAA,cAAc,EAAE,QAAQ;AACxB,YAAA,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO;AACzB,YAAA,eAAe,EAAE,KAAK,CAAC,UAAU,CAAC,OAAO;AACzC,SAAA;AACD,KAAA;AACD,CAAA,CAAC,CAAC;AAEI,MAAM,YAAY,GAAG,CAC3B,YAA4B,GAAA,EAAE,EAC9B,SAAS,GAAG,SAAS,EACrB,OAAkB,GAAA,SAAS,KACX;AAChB,IAAA,MAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC;AAEjE,IAAA,OAAO,KAAK,CAAC,aAAa,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;AACrE,CAAC;;ACpND,SAAS,UAAU,CAClB,OAAyB,EACzB,mBAA0D,EAC1D,kBAAsD,EACtD,cAAuB,EAAA;AAEvB,IAAA,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAGA,gBAAK,CAAC,QAAQ,CAAmB,MAAM,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC;AACzG,IAAA,MAAM,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,GAAGA,gBAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACjE,MAAM,cAAc,GAAGA,gBAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAExCa,cAAkB,CAAC,MAAK;AACvB,QAAA,eAAe,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC;AAC3C,KAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IAEd,MAAM,eAAe,GAAGb,gBAAK,CAAC,WAAW,CACxC,CAAC,CAAkC,KAAI;;AACtC,QAAA,MAAM,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC,MAAwB,CAAC;QAClD,MAAM,EAAE,GAAG,CAAA,EAAA,GAAA,UAAU,CAAC,YAAY,CAAC,gBAAgB,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,CAAC;QAE5D,IAAI,EAAE,EAAE;YACP,cAAc,CAAC,OAAO,GAAG,CAAA,MAAA,CAAA,EAAA,GAAA,YAAY,CAAC,mBAAmB,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,EAAE,0CAAE,QAAQ,EAAE,KAAI,EAAE,CAAC;AAEnG,YAAA,iBAAiB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;SAC1C;AACF,KAAC,EACD,CAAC,YAAY,CAAC,CACd,CAAC;IAEF,MAAM,eAAe,GAAGA,gBAAK,CAAC,WAAW,CACxC,CAAC,CAAkC,KAAI;;AACtC,QAAA,MAAM,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC,MAAwB,CAAC;QAClD,MAAM,EAAE,GAAG,CAAA,EAAA,GAAA,UAAU,CAAC,YAAY,CAAC,gBAAgB,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,CAAC;AAE5D,QAAA,IAAI,EAAE,IAAI,cAAc,CAAC,OAAO,IAAI,EAAE,KAAK,cAAc,CAAC,OAAO,EAAE;YAClE,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,YAAY,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC;YACnF,MAAM,cAAc,GAAG,mBAAmB,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AAC7D,YAAA,MAAM,aAAa,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;YAExC,aAAa,CAAC,gBAAgB,CAAC,GAAG,YAAY,CAAC,cAAc,CAAC,CAAC;YAC/D,aAAa,CAAC,cAAc,CAAC,GAAG,YAAY,CAAC,gBAAgB,CAAC,CAAC;YAE/D,eAAe,CAAC,aAAa,CAAC,CAAC;YAE/B,mBAAmB,CAAC,aAAa,CAAC,CAAC;SACnC;AACF,KAAC,EACD,CAAC,mBAAmB,EAAE,YAAY,CAAC,CACnC,CAAC;IAEF,MAAM,cAAc,GAAGA,gBAAK,CAAC,WAAW,CAAC,CAAC,CAAkC,KAAI;QAC/E,CAAC,CAAC,cAAc,EAAE,CAAC;KACnB,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,eAAe,GAAGA,gBAAK,CAAC,WAAW,CAAC,CAAC,CAAkC,KAAI;QAChF,CAAC,CAAC,cAAc,EAAE,CAAC;KACnB,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,aAAa,GAAGA,gBAAK,CAAC,WAAW,CAAC,CAAC,CAAkC,KAAI;QAC9E,CAAC,CAAC,cAAc,EAAE,CAAC;AAEnB,QAAA,cAAc,CAAC,OAAO,GAAG,EAAE,CAAC;QAE5B,iBAAiB,CAAC,EAAE,CAAC,CAAC;KACtB,EAAE,EAAE,CAAC,CAAC;AAEP,IAAA,MAAM,oBAAoB,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AAC9D,IAAA,MAAM,iBAAiB,GAAGA,gBAAK,CAAC,OAAO,CACtC,MAAM,YAAY,CAAC,mBAAmB,CAAC,YAAY,EAAE,kBAAkB,KAAA,IAAA,IAAlB,kBAAkB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAlB,kBAAkB,CAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,EAAE,EAC3F,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAClC,CAAC;IAEF,OAAO;QACN,YAAY;QACZ,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,cAAc;QACd,eAAe;QACf,aAAa;QACb,oBAAoB;QACpB,iBAAiB;KACjB,CAAC;AACH;;AChEA,SAAS,SAAS,CAAI,KAAoB,EAAA;IACzC,MAAM,EACL,IAAI,GAAG,YAAY,CAAC,IAAI,EACxB,OAAO,GAAG,YAAY,CAAC,OAAO,EAC9B,KAAK,GAAG,YAAY,CAAC,KAAK,EAC1B,OAAO,GAAG,YAAY,CAAC,OAAO,EAC9B,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAChC,OAAO,GAAG,YAAY,CAAC,OAAO,EAC9B,gBAAgB,GAAG,YAAY,CAAC,gBAAgB,EAChD,cAAc,GAAG,YAAY,CAAC,cAAc,EAC5C,KAAK,GAAG,YAAY,CAAC,KAAK,EAC1B,cAAc,GAAG,YAAY,CAAC,cAAc,EAC5C,oBAAoB,GAAG,YAAY,CAAC,oBAAoB,EACxD,uBAAuB,GAAG,YAAY,CAAC,uBAAuB,EAC9D,yBAAyB,GAAG,YAAY,CAAC,yBAAyB,EAClE,yBAAyB,GAAG,YAAY,CAAC,yBAAyB,EAClE,qBAAqB,GAAG,YAAY,CAAC,qBAAqB,EAC1D,qBAAqB,GAAG,YAAY,CAAC,qBAAqB,EAC1D,uBAAuB,GAAG,YAAY,CAAC,uBAAuB,EAC9D,4BAA4B,GAAG,YAAY,CAAC,4BAA4B,EACxE,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,EACpD,oBAAoB,GAAG,YAAY,CAAC,oBAAoB,EACxD,cAAc,GAAG,YAAY,CAAC,cAAc,EAC5C,mBAAmB,GAAG,YAAY,CAAC,mBAAmB,EACtD,YAAY,GAAG,YAAY,CAAC,YAAY,EACxC,gBAAgB,GAAG,YAAY,CAAC,gBAAgB,EAChD,uBAAuB,GAAG,YAAY,CAAC,uBAAuB,EAC9D,mBAAmB,GAAG,YAAY,CAAC,mBAAmB,EACtD,qBAAqB,GAAG,YAAY,CAAC,qBAAqB,EAC1D,0BAA0B,GAAG,YAAY,CAAC,0BAA0B,EACpE,iBAAiB,GAAG,YAAY,CAAC,iBAAiB,EAClD,4BAA4B,GAAG,YAAY,CAAC,4BAA4B,EACxE,sBAAsB,GAAG,YAAY,CAAC,sBAAsB,EAC5D,uBAAuB,GAAG,YAAY,CAAC,uBAAuB,EAC9D,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,EACpD,sBAAsB,GAAG,YAAY,CAAC,sBAAsB,EAC5D,mBAAmB,GAAG,YAAY,CAAC,mBAAmB,EACtD,0BAA0B,GAAG,YAAY,CAAC,0BAA0B,EACpE,UAAU,GAAG,YAAY,CAAC,UAAU,EACpC,eAAe,GAAG,YAAY,CAAC,eAAe,EAC9C,iBAAiB,GAAG,YAAY,CAAC,iBAAiB,EAClD,gBAAgB,GAAG,YAAY,CAAC,gBAAgB,EAChD,eAAe,GAAG,YAAY,CAAC,eAAe,EAC9C,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAChC,WAAW,GAAG,YAAY,CAAC,WAAW,EACtC,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAChC,WAAW,GAAG,YAAY,CAAC,WAAW,EACtC,uBAAuB,GAAG,YAAY,CAAC,uBAAuB,EAC9D,UAAU,GAAG,YAAY,CAAC,UAAU,EACpC,SAAS,GAAG,YAAY,CAAC,SAAS,EAClC,cAAc,GAAG,YAAY,CAAC,cAAc,EAC5C,aAAa,GAAG,YAAY,CAAC,aAAa,EAC1C,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,EACpD,aAAa,GAAG,YAAY,CAAC,aAAa,EAC1C,cAAc,GAAG,YAAY,CAAC,cAAc,EAC5C,cAAc,GAAG,YAAY,CAAC,cAAc,EAC5C,gBAAgB,GAAG,YAAY,CAAC,gBAAgB,EAChD,cAAc,GAAG,YAAY,CAAC,cAAc,EAC5C,YAAY,GAAG,YAAY,CAAC,YAAY,EACxC,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,EACpD,eAAe,GAAG,YAAY,CAAC,eAAe,EAC9C,eAAe,GAAG,YAAY,CAAC,eAAe,EAC9C,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAChC,MAAM,GAAG,YAAY,CAAC,MAAM,EAC5B,YAAY,GAAG,YAAY,CAAC,YAAY,EACxC,UAAU,GAAG,YAAY,CAAC,UAAU,EACpC,uBAAuB,GAAG,YAAY,CAAC,uBAAuB,EAC9D,4BAA4B,GAAG,YAAY,CAAC,4BAA4B,EACxE,qBAAqB,GAAG,YAAY,CAAC,qBAAqB,EAC1D,0BAA0B,GAAG,YAAY,CAAC,0BAA0B,EACpE,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,EACpD,wBAAwB,GAAG,YAAY,CAAC,wBAAwB,EAChE,qBAAqB,GAAG,YAAY,CAAC,qBAAqB,EAC1D,kCAAkC,GAAG,YAAY,CAAC,kCAAkC,EACpF,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,EACpD,cAAc,GAAG,YAAY,CAAC,cAAc,EAC5C,iBAAiB,GAAG,YAAY,CAAC,iBAAiB,EAClD,oBAAoB,GAAG,YAAY,CAAC,oBAAoB,EACxD,KAAK,GAAG,YAAY,CAAC,KAAK,EAC1B,YAAY,GAAG,YAAY,CAAC,YAAY,EACxC,SAAS,GAAG,YAAY,CAAC,SAAS,EAClC,mBAAmB,GAAG,YAAY,CAAC,mBAAmB,EACtD,SAAS,EACT,SAAS,GACT,GAAG,KAAK,CAAC;AAEV,IAAA,MAAM,EACL,YAAY,EACZ,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,cAAc,EACd,eAAe,EACf,aAAa,EACb,oBAAoB,EACpB,iBAAiB,GACjB,GAAG,UAAU,CAAC,OAAO,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,cAAc,CAAC,CAAC;IAEjF,MAAM,CACL,EACC,WAAW,EACX,WAAW,EACX,YAAY,EACZ,WAAW,EACX,aAAa,EACb,cAAc,EACd,aAAa,EACb,0BAA0B,GAC1B,EACD,QAAQ,EACR,GAAGA,gBAAK,CAAC,UAAU,CAA0C,YAAY,EAAE;AAC3E,QAAA,WAAW,EAAE,KAAK;AAClB,QAAA,aAAa,EAAE,CAAC;AAChB,QAAA,YAAY,EAAE,EAAE;AAChB,QAAA,cAAc,EAAE,iBAAiB;AACjC,QAAA,0BAA0B,EAAE,KAAK;AACjC,QAAA,aAAa,EAAE,oBAAoB;AACnC,QAAA,WAAW,EAAE,qBAAqB;AAClC,QAAA,WAAW,EAAE,iBAAiB;AAC9B,QAAA,gBAAgB,EAAE,KAAK;QACvB,cAAc,EAAE,YAAY,CAAC,cAAc;AAC3C,KAAA,CAAC,CAAC;IAEH,MAAM,EAAE,qBAAqB,GAAG,KAAK,EAAE,2BAA2B,GAAG,KAAK,EAAE,GAAG,uBAAuB,CAAC;AACvG,IAAA,MAAM,eAAe,GAAG,CAAC,EAAE,gBAAgB,KAAK,2BAA2B,IAAI,qBAAqB,CAAC,CAAC,CAAC;AACvG,IAAA,MAAM,iBAAiB,GAAG,UAAU,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5E,IAAA,MAAM,UAAU,GAAG,mBAAmB,IAAI,gBAAgB,CAAC;IAE3D,MAAM,YAAY,GAAGA,gBAAK,CAAC,OAAO,CAAC,MAAM,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC;IACnG,MAAM,YAAY,GAAGA,gBAAK,CAAC,OAAO,CAAC,OAAY,MAAA,CAAA,MAAA,CAAA,EAAA,GAAC,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,EAAI,CAAA,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;AAE7G,IAAA,MAAM,UAAU,GAAGA,gBAAK,CAAC,OAAO,CAAC,MAAK;QAErC,IAAI,UAAU,EAAE;AACf,YAAA,OAAO,IAAI,CAAC;SACZ;AAED,QAAA,IAAI,CAAA,cAAc,KAAA,IAAA,IAAd,cAAc,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAd,cAAc,CAAE,YAAY,KAAI,OAAO,cAAc,CAAC,YAAY,KAAK,UAAU,EAAE;AACtF,YAAA,MAAM,MAAM,GAAG,cAAc,CAAC,YAAY,CAAC;AAC3C,YAAA,MAAM,kBAAkB,GAAG,aAAa,KAAK,SAAS,CAAC,GAAG,GAAG,MAAM,GAAG,CAAC,CAAI,EAAE,CAAI,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAExG,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC1C;AAED,QAAA,OAAO,IAAI,CAAC,IAAI,EAAE,cAAc,aAAd,cAAc,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAd,cAAc,CAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;AAC1E,KAAC,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,aAAa,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;AAEpE,IAAA,MAAM,SAAS,GAAGA,gBAAK,CAAC,OAAO,CAAC,MAAK;AACpC,QAAA,IAAI,UAAU,IAAI,CAAC,gBAAgB,EAAE;AAEpC,YAAA,MAAM,SAAS,GAAG,WAAW,GAAG,WAAW,CAAC;AAC5C,YAAA,MAAM,UAAU,GAAG,SAAS,GAAG,WAAW,CAAC;YAE3C,OAAO,UAAU,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;SAC/C;AAED,QAAA,OAAO,UAAU,CAAC;AACnB,KAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,gBAAgB,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC;IAEzE,MAAM,UAAU,GAAGA,gBAAK,CAAC,WAAW,CAAC,CAAC,MAAqB,KAAI;QAC9D,QAAQ,CAAC,MAAM,CAAC,CAAC;KACjB,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,mBAAmB,GAAGA,gBAAK,CAAC,WAAW,CAAC,CAAC,MAAwB,KAAI;QAC1E,QAAQ,CAAC,MAAM,CAAC,CAAC;KACjB,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,iBAAiB,GAAGA,gBAAK,CAAC,WAAW,CAAC,CAAC,MAA0B,KAAI;QAC1E,QAAQ,CAAC,MAAM,CAAC,CAAC;KACjB,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,gBAAgB,GAAGA,gBAAK,CAAC,WAAW,CACzC,CAAC,GAAM,EAAE,CAAwC,KAAK,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC,EAC1E,CAAC,YAAY,CAAC,CACd,CAAC;IAEF,MAAM,sBAAsB,GAAGA,gBAAK,CAAC,WAAW,CAC/C,CAAC,GAAM,EAAE,CAAwC,KAAK,kBAAkB,CAAC,GAAG,EAAE,CAAC,CAAC,EAChF,CAAC,kBAAkB,CAAC,CACpB,CAAC;IAEF,MAAM,mBAAmB,GAAGA,gBAAK,CAAC,WAAW,CAC5C,CAAC,GAAM,EAAE,CAAwC,KAAK,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC,EAC7E,CAAC,eAAe,CAAC,CACjB,CAAC;IAEF,MAAM,mBAAmB,GAAGA,gBAAK,CAAC,WAAW,CAC5C,CAAC,GAAM,EAAE,CAAwC,KAAK,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC,EAC7E,CAAC,eAAe,CAAC,CACjB,CAAC;AAEF,IAAA,MAAM,gBAAgB,GAAGA,gBAAK,CAAC,WAAW,CACzC,CAAC,IAAY,KACZ,QAAQ,CAAC;AACR,QAAA,IAAI,EAAE,aAAa;QACnB,IAAI;QACJ,gBAAgB;AAChB,QAAA,WAAW,EAAE,yBAAyB;QACtC,2BAA2B;KAC3B,CAAC,EACH,CAAC,gBAAgB,EAAE,2BAA2B,EAAE,yBAAyB,CAAC,CAC1E,CAAC;IAEF,MAAM,uBAAuB,GAAGA,gBAAK,CAAC,WAAW,CAChD,CAAC,cAAsB,KAAI;AAC1B,QAAA,MAAM,QAAQ,GAAG,mBAAmB,IAAI,SAAS,CAAC,MAAM,CAAC;QACzD,MAAM,WAAW,GAAG,gBAAgB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAC/D,MAAM,gBAAgB,GAAG,eAAe,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QAInE,IAAI,CAAC,gBAAgB,EAAE;YACtB,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;SACnC;AAED,QAAA,QAAQ,CAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC,CAAC;AACjG,KAAC,EACD,CAAC,WAAW,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,SAAS,CAAC,MAAM,CAAC,CACxF,CAAC;IAEF,MAAM,aAAa,GAAG,MAAK;QAC1B,IAAI,WAAW,EAAE;AAChB,YAAA,OAAO,KAAK,CAAC;SACb;QAED,IAAI,gBAAgB,EAAE;AACrB,YAAA,OAAO,IAAI,CAAC;SACZ;QAED,OAAO,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC;AAClD,KAAC,CAAC;IAEF,MAAM,UAAU,GAAG,MAAK;QACvB,IAAI,QAAQ,EAAE;AACb,YAAA,OAAO,KAAK,CAAC;SACb;QAED,IAAI,KAAK,EAAE;AACV,YAAA,OAAO,IAAI,CAAC;SACZ;QAED,IAAI,OAAO,EAAE;AACZ,YAAA,OAAO,IAAI,CAAC;SACZ;AAED,QAAA,OAAO,KAAK,CAAC;AACd,KAAC,CAAC;AAGF,IAAA,IAAI,UAAU,IAAI,CAAC,gBAAgB,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QACvF,MAAM,WAAW,GAAG,gBAAgB,CAAC,UAAU,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QACrE,MAAM,gBAAgB,GAAG,eAAe,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QAEnE,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;KACnC;IAEDa,cAAkB,CAAC,MAAK;AACvB,QAAA,oBAAoB,CAAC,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAE3F,KAAC,EAAE,CAAC,0BAA0B,CAAC,CAAC,CAAC;IAEjCA,cAAkB,CAAC,MAAK;AACvB,QAAA,MAAM,CAAC,cAAc,EAAE,aAAa,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAE5D,KAAC,EAAE,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC,CAAC;IAEpCA,cAAkB,CAAC,MAAK;QACvB,YAAY,CAAC,WAAW,EAAE,mBAAmB,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AACrE,KAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAElBA,cAAkB,CAAC,MAAK;AACvB,QAAA,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAC/C,KAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAElBA,cAAkB,CAAC,MAAK;QACvB,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;AACzC,KAAC,EAAE,CAAC,qBAAqB,EAAE,0BAA0B,CAAC,CAAC,CAAC;IAExDA,cAAkB,CAAC,MAAK;QACvB,IAAI,UAAU,IAAI,gBAAgB,IAAI,mBAAmB,GAAG,CAAC,EAAE;YAC9D,MAAM,WAAW,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;YACvE,MAAM,gBAAgB,GAAG,eAAe,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAEnE,YAAA,IAAI,WAAW,KAAK,gBAAgB,EAAE;gBACrC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;aACnC;SACD;AACF,KAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC;AAE1B,IAAAb,gBAAK,CAAC,SAAS,CAAC,MAAK;QACpB,QAAQ,CAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,CAAC,CAAC;AAChF,KAAC,EAAE,CAAC,oBAAoB,EAAE,iBAAiB,CAAC,CAAC,CAAC;AAE9C,IAAAA,gBAAK,CAAC,SAAS,CAAC,MAAK;QACpB,IAAI,CAAC,qBAAqB,EAAE;YAC3B,OAAO;SACP;AAED,QAAA,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;AAE7E,QAAA,MAAM,QAAQ,GAAG,oBAAoB,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC;AAEtF,QAAA,QAAQ,CAAC;AACR,YAAA,IAAI,EAAE,sBAAsB;YAC5B,QAAQ;AACR,YAAA,YAAY,EAAE,QAAQ;YACtB,SAAS,EAAE,UAAU,CAAC,MAAM;YAC5B,eAAe;AACf,SAAA,CAAC,CAAC;AAIJ,KAAC,EAAE,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC,CAAC;IAElC,MAAM,WAAW,GAAG,yBAAyB,GAAG,SAAS,GAAG,UAAU,CAAC;AACvE,IAAA,MAAM,aAAa,GAAG,2BAA2B,IAAI,oBAAoB,IAAI,yBAAyB,CAAC;AAEvG,IAAA,QACCA,gBAAC,CAAA,aAAA,CAAAc,oBAAa,EAAC,EAAA,KAAK,EAAE,YAAY,EAAA;AAChC,QAAA,UAAU,EAAE,KACZd,gBAAC,CAAA,aAAA,CAAA,MAAM,IACN,KAAK,EAAE,KAAK,EACZ,OAAO,EAAE,OAAO,EAChB,QAAQ,EAAE,CAAC,aAAa,EACxB,aAAa,EAAE,aAAa,EAC5B,SAAS,EAAE,SAAS,EACpB,cAAc,EAAE,cAAc,EAC9B,gBAAgB,EAAE,gBAAgB,EAClC,cAAc,EAAE,cAAc,GAC7B,CACF;AAEA,QAAA,SAAS,KACTA,gBAAC,CAAA,aAAA,CAAA,SAAS,IAAC,KAAK,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAC1D,EAAA,kBAAkB,CACR,CACZ;AAED,QAAAA,gBAAA,CAAA,aAAA,CAAC,iBAAiB,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,aAAA,EACJ,UAAU,EAAA,cAAA,EACT,WAAW,EAAA,0BAAA,EACC,uBAAuB,EACjD,SAAS,EAAE,SAAS,EAAA,EAChB,YAAY,CAAA;AAEhB,YAAAA,gBAAA,CAAA,aAAA,CAAC,OAAO,EAAA,IAAA;gBACN,eAAe,IAAI,CAAC,gBAAgB,IAAIA,+BAAC,eAAe,EAAA,IAAA,EAAE,iBAAiB,CAAmB;gBAE/FA,gBAAC,CAAA,aAAA,CAAAe,UAAK,kBAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAC,WAAW,EAAC,IAAI,EAAC,OAAO,EAAA,GAAM,SAAS,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,EAAC;AAC1G,oBAAA,aAAa,EAAE,KACff,gBAAA,CAAA,aAAA,CAAC,IAAI,EAAC,EAAA,SAAS,EAAC,eAAe,EAAC,IAAI,EAAC,UAAU,kBAAe,WAAW,EAAA;wBACxEA,gBAAC,CAAA,aAAA,CAAA,OAAO,EAAC,EAAA,SAAS,EAAC,kBAAkB,EAAC,IAAI,EAAC,KAAK,EAAA,QAAA,EAAS,KAAK,EAAA;4BAC5D,cAAc;AACd,iCAAC,aAAa,IACbA,gBAAA,CAAA,aAAA,CAAC,QAAQ,EAAA,EAAC,KAAK,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,EAAA,CAAI,KAEzCA,gBAAA,CAAA,aAAA,CAAC,cAAc,EAAA,EACd,WAAW,EAAE,WAAW,EACxB,YAAY,EAAE,YAAY,EAC1B,uBAAuB,EAAE,uBAAuB,EAChD,4BAA4B,EAAE,4BAA4B,EAC1D,qBAAqB,EAAE,qBAAqB,EAC5C,OAAO,EAAE,WAAW,EACpB,QAAQ,EAAE,QAAQ,EAClB,eAAe,EAAE,eAAe,EAChC,eAAe,EAAE,mBAAmB,EAAA,CACnC,CACF,CAAC;AACF,4BAAA,cAAc,IAAI,CAAC,0BAA0B,IAAIA,gBAAA,CAAA,aAAA,CAAC,cAAc,EAAG,IAAA,CAAA;4BACnE,YAAY,CAAC,GAAG,CAAC,MAAM,KACvBA,gBAAC,CAAA,aAAA,CAAA,MAAM,IACN,GAAG,EAAE,MAAM,CAAC,EAAE,EACd,MAAM,EAAE,MAAM,EACd,cAAc,EAAE,cAAc,EAC9B,QAAQ,EAAE,eAAe,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EACpD,UAAU,EAAE,UAAU,EACtB,gBAAgB,EAAE,gBAAgB,EAClC,qBAAqB,EAAE,qBAAqB,EAC5C,yBAAyB,EAAE,yBAAyB,EACpD,aAAa,EAAE,aAAa,EAC5B,QAAQ,EAAE,QAAQ,EAClB,UAAU,EAAE,UAAU,EACtB,MAAM,EAAE,UAAU,EAClB,WAAW,EAAE,eAAe,EAC5B,UAAU,EAAE,cAAc,EAC1B,SAAS,EAAE,aAAa,EACxB,WAAW,EAAE,eAAe,EAC5B,WAAW,EAAE,eAAe,EAC5B,gBAAgB,EAAE,gBAAgB,GACjC,CACF,CAAC,CACO,CACJ,CACP;oBAEA,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,eAAe,IAAIA,gBAAC,CAAA,aAAA,CAAAgB,aAAM,EAAE,IAAA,EAAA,eAAe,CAAU;AAE5E,oBAAA,eAAe,IAAI,gBAAgB,IAAIhB,+BAAC,eAAe,EAAA,IAAA,EAAE,iBAAiB,CAAmB;AAE7F,oBAAA,CAAC,eAAe,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,KACzCA,gBAAA,CAAA,aAAA,CAAC,IAAI,EAAA,EAAC,SAAS,EAAC,eAAe,EAAC,IAAI,EAAC,UAAU,EAAA,EAC7C,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,KAAI;wBACzB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAe,EAAE,QAAQ,CAAoB,CAAC;AAC/D,wBAAA,MAAM,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;wBAClC,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;AAC5D,wBAAA,MAAM,gBAAgB,GAAG,CAAC,EAAE,cAAc,IAAI,qBAAqB,IAAI,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;AACnG,wBAAA,MAAM,gBAAgB,GAAG,CAAC,EAAE,cAAc,IAAI,qBAAqB,IAAI,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;AAEnG,wBAAA,QACCA,gBAAC,CAAA,aAAA,CAAA,GAAG,IACH,EAAE,EAAE,EAAE,EACN,GAAG,EAAE,EAAE,EACP,QAAQ,EAAE,QAAQ,EACL,aAAA,EAAA,EAAE,EACf,OAAO,EAAE,YAAY,EACrB,GAAG,EAAE,GAAG,EACR,QAAQ,EAAE,UAAU,CAAC,MAAM,EAC3B,QAAQ,EAAE,CAAC,EACX,cAAc,EAAE,cAAc,EAC9B,cAAc,EAAE,cAAc,EAC9B,cAAc,EAAE,cAAc,EAC9B,gBAAgB,EAAE,gBAAgB,EAClC,cAAc,EAAE,cAAc,EAC9B,KAAK,EAAE,KAAK,EACZ,kBAAkB,EAAE,kBAAkB,EACtC,wBAAwB,EAAE,wBAAwB,EAClD,uBAAuB,EAAE,uBAAuB,EAChD,4BAA4B,EAAE,4BAA4B,EAC1D,0BAA0B,EAAE,0BAA0B,EACtD,uBAAuB,EAAE,gBAAgB,EACzC,eAAe,EAAE,gBAAgB,EACjC,kCAAkC,EAAE,kCAAkC,EACtE,oBAAoB,EAAE,oBAAoB,EAC1C,QAAQ,EAAE,QAAQ,EAClB,uBAAuB,EAAE,uBAAuB,EAChD,uBAAuB,EAAE,uBAAuB,EAChD,4BAA4B,EAAE,4BAA4B,EAC1D,qBAAqB,EAAE,qBAAqB,EAC5C,oBAAoB,EAAE,oBAAoB,EAC1C,OAAO,EAAE,OAAO,EAChB,kBAAkB,EAAE,kBAAkB,EACtC,YAAY,EAAE,gBAAgB,EAC9B,kBAAkB,EAAE,sBAAsB,EAC1C,eAAe,EAAE,mBAAmB,EACpC,eAAe,EAAE,mBAAmB,EACpC,aAAa,EAAE,iBAAiB,EAChC,gBAAgB,EAAE,gBAAgB,EAClC,WAAW,EAAE,eAAe,EAC5B,UAAU,EAAE,cAAc,EAC1B,SAAS,EAAE,aAAa,EACxB,WAAW,EAAE,eAAe,EAC5B,WAAW,EAAE,eAAe,EAAA,CAC3B,EACD;AACH,qBAAC,CAAC,CACI,CACP,CACM,CACC,CACS;AAEnB,QAAA,iBAAiB,KACjBA,gBAAA,CAAA,aAAA,CAAA,KAAA,EAAA,IAAA;YACCA,gBAAC,CAAA,aAAA,CAAA,UAAU,EACV,EAAA,YAAY,EAAE,gBAAgB,EAC9B,mBAAmB,EAAE,uBAAuB,EAC5C,QAAQ,EAAE,mBAAmB,IAAI,UAAU,CAAC,MAAM,EAClD,WAAW,EAAE,WAAW,EACxB,WAAW,EAAE,WAAW,EACxB,SAAS,EAAE,SAAS,EACpB,4BAA4B,EAAE,4BAA4B,EAC1D,sBAAsB,EAAE,sBAAsB,EAC9C,uBAAuB,EAAE,uBAAuB,EAChD,kBAAkB,EAAE,kBAAkB,EACtC,sBAAsB,EAAE,sBAAsB,EAC9C,0BAA0B,EAAE,0BAA0B,EAAA,CACrD,CACG,CACN,CACc,EACf;AACH,CAAC;AAED,kBAAeA,gBAAK,CAAC,IAAI,CAAC,SAAS,CAAqB;;;;;;;"}