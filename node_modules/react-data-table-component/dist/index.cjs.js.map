{"version": 3, "file": "index.cjs.js", "sources": ["../src/DataTable/types.ts", "../src/DataTable/util.ts", "../src/DataTable/tableReducer.ts", "../src/DataTable/Table.tsx", "../src/DataTable/TableHead.tsx", "../src/DataTable/TableHeadRow.tsx", "../src/DataTable/media.ts", "../src/DataTable/Cell.ts", "../src/DataTable/TableCell.tsx", "../src/DataTable/Checkbox.tsx", "../src/DataTable/TableCellCheckbox.tsx", "../src/DataTable/ExpanderButton.tsx", "../src/DataTable/TableCellExpander.tsx", "../src/DataTable/ExpanderRow.tsx", "../src/DataTable/constants.ts", "../src/DataTable/TableRow.tsx", "../src/icons/NativeSortIcon.tsx", "../src/DataTable/TableCol.tsx", "../src/DataTable/TableColCheckbox.tsx", "../src/hooks/useRTL.ts", "../src/DataTable/ContextMenu.tsx", "../src/DataTable/TableHeader.tsx", "../src/DataTable/TableSubheader.tsx", "../src/DataTable/TableBody.tsx", "../src/DataTable/ResponsiveWrapper.tsx", "../src/DataTable/ProgressWrapper.tsx", "../src/DataTable/TableWrapper.tsx", "../src/DataTable/TableColExpander.tsx", "../src/DataTable/NoDataWrapper.tsx", "../src/icons/Dropdown.tsx", "../src/DataTable/Select.tsx", "../src/DataTable/defaultProps.tsx", "../src/icons/ExpanderCollapsedIcon.tsx", "../src/icons/ExpanderExpandedIcon.tsx", "../src/icons/FirstPage.tsx", "../src/icons/LastPage.tsx", "../src/icons/Right.tsx", "../src/icons/Left.tsx", "../src/DataTable/Pagination.tsx", "../src/hooks/useWindowSize.ts", "../src/hooks/useDidUpdateEffect.ts", "../node_modules/deepmerge/dist/cjs.js", "../src/DataTable/themes.ts", "../src/hooks/useColumns.ts", "../src/DataTable/DataTable.tsx", "../src/DataTable/styles.ts"], "sourcesContent": ["import { Alignment, Direction, Media } from './constants';\nimport { CSSObject } from 'styled-components';\n\nexport enum SortOrder {\n\tASC = 'asc',\n\tDESC = 'desc',\n}\n\nexport type Primitive = string | number | boolean;\nexport type ColumnSortFunction<T> = (a: T, b: T) => number;\nexport type ExpandRowToggled<T> = (expanded: boolean, row: T) => void;\nexport type Format<T> = (row: T, rowIndex: number) => React.ReactNode;\nexport type RowState<T> = ((row: T) => boolean) | null;\nexport type Selector<T> = (row: T, rowIndex?: number) => Primitive;\nexport type SortFunction<T> = (rows: T[], field: Selector<T>, sortDirection: SortOrder) => T[];\nexport type TableRow = Record<string, unknown>;\nexport type ComponentProps = Record<string, unknown>;\nexport type ExpanderComponentProps<T> = { data: T };\nexport type ExpandableRowsComponent<T> = React.ComponentType<ExpanderComponentProps<T>>;\nexport type PaginationChangePage = (page: number, totalRows: number) => void;\nexport type PaginationChangeRowsPerPage = (currentRowsPerPage: number, currentPage: number) => void;\nexport type PaginationComponentProps = {\n\trowsPerPage: number;\n\trowCount: number;\n\tcurrentPage: number;\n\tonChangePage: PaginationChangePage;\n\tonChangeRowsPerPage: PaginationChangeRowsPerPage;\n};\nexport type PaginationComponent = React.ComponentType<PaginationComponentProps>;\n\nexport type TableProps<T> = {\n\tactions?: React.ReactNode | React.ReactNode[];\n\tariaLabel?: string;\n\tclassName?: string;\n\tclearSelectedRows?: boolean;\n\tcolumns: TableColumn<T>[];\n\tconditionalRowStyles?: ConditionalStyles<T>[];\n\tcontextActions?: React.ReactNode | React.ReactNode[];\n\tcontextComponent?: React.ReactNode;\n\tcontextMessage?: ContextMessage;\n\tcustomStyles?: TableStyles;\n\tdata: T[];\n\tdefaultSortAsc?: boolean;\n\tdefaultSortFieldId?: string | number | null | undefined;\n\tdense?: boolean;\n\tdirection?: Direction;\n\tdisabled?: boolean;\n\texpandableIcon?: ExpandableIcon;\n\texpandableInheritConditionalStyles?: boolean;\n\texpandableRowDisabled?: RowState<T>;\n\texpandableRowExpanded?: RowState<T>;\n\texpandableRows?: boolean;\n\texpandableRowsComponent?: ExpandableRowsComponent<T>;\n\texpandableRowsComponentProps?: ComponentProps;\n\texpandableRowsHideExpander?: boolean;\n\texpandOnRowClicked?: boolean;\n\texpandOnRowDoubleClicked?: boolean;\n\tfixedHeader?: boolean;\n\tfixedHeaderScrollHeight?: string;\n\thighlightOnHover?: boolean;\n\tkeyField?: string;\n\tnoContextMenu?: boolean;\n\tnoDataComponent?: React.ReactNode;\n\tnoHeader?: boolean;\n\tnoTableHead?: boolean;\n\tonChangePage?: PaginationChangePage;\n\tonChangeRowsPerPage?: PaginationChangeRowsPerPage;\n\tonRowClicked?: (row: T, e: React.MouseEvent) => void;\n\tonRowDoubleClicked?: (row: T, e: React.MouseEvent) => void;\n\tonRowMouseEnter?: (row: T, e: React.MouseEvent) => void;\n\tonRowMouseLeave?: (row: T, e: React.MouseEvent) => void;\n\tonRowExpandToggled?: ExpandRowToggled<T>;\n\tonSelectedRowsChange?: (selected: { allSelected: boolean; selectedCount: number; selectedRows: T[] }) => void;\n\tonSort?: (selectedColumn: TableColumn<T>, sortDirection: SortOrder, sortedRows: T[]) => void;\n\tonColumnOrderChange?: (nextOrder: TableColumn<T>[]) => void;\n\tpagination?: boolean;\n\tpaginationComponent?: PaginationComponent;\n\tpaginationComponentOptions?: PaginationOptions;\n\tpaginationDefaultPage?: number;\n\tpaginationIconFirstPage?: React.ReactNode;\n\tpaginationIconLastPage?: React.ReactNode;\n\tpaginationIconNext?: React.ReactNode;\n\tpaginationIconPrevious?: React.ReactNode;\n\tpaginationPerPage?: number;\n\tpaginationResetDefaultPage?: boolean;\n\tpaginationRowsPerPageOptions?: number[];\n\tpaginationServer?: boolean;\n\tpaginationServerOptions?: PaginationServerOptions;\n\tpaginationTotalRows?: number;\n\tpersistTableHead?: boolean;\n\tpointerOnHover?: boolean;\n\tprogressComponent?: React.ReactNode;\n\tprogressPending?: boolean;\n\tresponsive?: boolean;\n\tselectableRowDisabled?: RowState<T>;\n\tselectableRows?: boolean;\n\tselectableRowsComponent?: 'input' | React.ReactNode;\n\tselectableRowsComponentProps?: ComponentProps;\n\tselectableRowSelected?: RowState<T>;\n\tselectableRowsHighlight?: boolean;\n\tselectableRowsNoSelectAll?: boolean;\n\tselectableRowsVisibleOnly?: boolean;\n\tselectableRowsSingle?: boolean;\n\tsortFunction?: SortFunction<T> | null;\n\tsortIcon?: React.ReactNode;\n\tsortServer?: boolean;\n\tstriped?: boolean;\n\tstyle?: CSSObject;\n\tsubHeader?: React.ReactNode | React.ReactNode[];\n\tsubHeaderAlign?: Alignment;\n\tsubHeaderComponent?: React.ReactNode | React.ReactNode[];\n\tsubHeaderWrap?: boolean;\n\ttheme?: Themes;\n\t/**\n\t *  Shows and displays a header with a title\n\t *  */\n\ttitle?: string | React.ReactNode;\n};\n\nexport type TableColumnBase = {\n\tallowOverflow?: boolean;\n\tbutton?: boolean;\n\tcenter?: boolean;\n\tcompact?: boolean;\n\treorder?: boolean;\n\tgrow?: number;\n\thide?: number | ((value: number) => CSSObject) | Media;\n\tid?: string | number;\n\tignoreRowClick?: boolean;\n\tmaxWidth?: string;\n\tminWidth?: string;\n\tname?: string | number | React.ReactNode;\n\tomit?: boolean;\n\tright?: boolean;\n\tsortable?: boolean;\n\tstyle?: CSSObject;\n\twidth?: string;\n\twrap?: boolean;\n};\n\nexport interface TableColumn<T> extends TableColumnBase {\n\tname?: string | number | React.ReactNode;\n\tsortField?: string;\n\tcell?: (row: T, rowIndex: number, column: TableColumn<T>, id: string | number) => React.ReactNode;\n\tconditionalCellStyles?: ConditionalStyles<T>[];\n\tformat?: Format<T> | undefined;\n\tselector?: Selector<T>;\n\tsortFunction?: ColumnSortFunction<T>;\n}\n\nexport interface ConditionalStyles<T> {\n\twhen: (row: T) => boolean;\n\tstyle?: CSSObject | ((row: T) => CSSObject);\n\tclassNames?: string[];\n}\n\nexport interface TableStyles {\n\ttable?: {\n\t\tstyle: CSSObject;\n\t};\n\ttableWrapper?: {\n\t\tstyle: CSSObject;\n\t};\n\tresponsiveWrapper?: {\n\t\tstyle: CSSObject;\n\t};\n\theader?: {\n\t\tstyle: CSSObject;\n\t};\n\tsubHeader?: {\n\t\tstyle: CSSObject;\n\t};\n\thead?: {\n\t\tstyle: CSSObject;\n\t};\n\theadRow?: {\n\t\tstyle?: CSSObject;\n\t\tdenseStyle?: CSSObject;\n\t};\n\theadCells?: {\n\t\tstyle?: CSSObject;\n\t\tdraggingStyle?: CSSObject;\n\t};\n\tcontextMenu?: {\n\t\tstyle?: CSSObject;\n\t\tactiveStyle?: CSSObject;\n\t};\n\tcells?: {\n\t\tstyle: CSSObject;\n\t\tdraggingStyle?: CSSObject;\n\t};\n\trows?: {\n\t\tstyle?: CSSObject;\n\t\tselectedHighlightStyle?: CSSObject;\n\t\tdenseStyle?: CSSObject;\n\t\thighlightOnHoverStyle?: CSSObject;\n\t\tstripedStyle?: CSSObject;\n\t};\n\texpanderRow?: {\n\t\tstyle: CSSObject;\n\t};\n\texpanderCell?: {\n\t\tstyle: CSSObject;\n\t};\n\texpanderButton?: {\n\t\tstyle: CSSObject;\n\t};\n\tpagination?: {\n\t\tstyle?: CSSObject;\n\t\tpageButtonsStyle?: CSSObject;\n\t};\n\tnoData?: {\n\t\tstyle: CSSObject;\n\t};\n\tprogress?: {\n\t\tstyle: CSSObject;\n\t};\n}\n\nexport interface PaginationOptions {\n\tnoRowsPerPage?: boolean;\n\trowsPerPageText?: string;\n\trangeSeparatorText?: string;\n\tselectAllRowsItem?: boolean;\n\tselectAllRowsItemText?: string;\n}\n\nexport interface PaginationServerOptions {\n\tpersistSelectedOnSort?: boolean;\n\tpersistSelectedOnPageChange?: boolean;\n}\n\nexport interface ExpandableIcon {\n\tcollapsed: React.ReactNode;\n\texpanded: React.ReactNode;\n}\n\nexport interface ContextMessage {\n\tsingular: string;\n\tplural: string;\n\tmessage?: string;\n}\n\nexport type TableState<T> = {\n\tallSelected: boolean;\n\tcontextMessage: ContextMessage;\n\tselectedCount: number;\n\tselectedRows: T[];\n\tselectedColumn: TableColumn<T>;\n\tsortDirection: SortOrder;\n\tcurrentPage: number;\n\trowsPerPage: number;\n\tselectedRowsFlag: boolean;\n\t/* server-side pagination and server-side sorting will cause selectedRows to change\n\t because of this behavior onSelectedRowsChange useEffect is triggered (by design it should notify if there was a change)\n\t however, when using selectableRowsSingle\n\t*/\n\ttoggleOnSelectedRowsChange: boolean;\n};\n\n// Theming\ntype ThemeText = {\n\tprimary: string;\n\tsecondary: string;\n\tdisabled: string;\n};\n\ntype ThemeBackground = {\n\tdefault: string;\n};\n\ntype ThemeContext = {\n\tbackground: string;\n\ttext: string;\n};\n\ntype ThemeDivider = {\n\tdefault: string;\n};\n\ntype ThemeButton = {\n\tdefault: string;\n\tfocus: string;\n\thover: string;\n\tdisabled: string;\n};\n\ntype ThemeSelected = {\n\tdefault: string;\n\ttext: string;\n};\n\ntype ThemeHighlightOnHover = {\n\tdefault: string;\n\ttext: string;\n};\n\ntype ThemeStriped = {\n\tdefault: string;\n\ttext: string;\n};\n\nexport type Themes = string;\n\nexport interface Theme {\n\ttext: ThemeText;\n\tbackground: ThemeBackground;\n\tcontext: ThemeContext;\n\tdivider: ThemeDivider;\n\tbutton: ThemeButton;\n\tselected: ThemeSelected;\n\thighlightOnHover: ThemeHighlightOnHover;\n\tstriped: ThemeStriped;\n}\n\n// Reducer Actions\nexport interface AllRowsAction<T> {\n\ttype: 'SELECT_ALL_ROWS';\n\tkeyField: string;\n\trows: T[];\n\trowCount: number;\n\tmergeSelections: boolean;\n}\n\nexport interface SingleRowAction<T> {\n\ttype: 'SELECT_SINGLE_ROW';\n\tkeyField: string;\n\trow: T;\n\tisSelected: boolean;\n\trowCount: number;\n\tsingleSelect: boolean;\n}\n\nexport interface MultiRowAction<T> {\n\ttype: 'SELECT_MULTIPLE_ROWS';\n\tkeyField: string;\n\tselectedRows: T[];\n\ttotalRows: number;\n\tmergeSelections: boolean;\n}\n\nexport interface SortAction<T> {\n\ttype: 'SORT_CHANGE';\n\tsortDirection: SortOrder;\n\tselectedColumn: TableColumn<T>;\n\tclearSelectedOnSort: boolean;\n}\n\nexport interface PaginationPageAction {\n\ttype: 'CHANGE_PAGE';\n\tpage: number;\n\tpaginationServer: boolean;\n\tvisibleOnly: boolean;\n\tpersistSelectedOnPageChange: boolean;\n}\n\nexport interface PaginationRowsPerPageAction {\n\ttype: 'CHANGE_ROWS_PER_PAGE';\n\trowsPerPage: number;\n\tpage: number;\n}\n\nexport interface ClearSelectedRowsAction {\n\ttype: 'CLEAR_SELECTED_ROWS';\n\tselectedRowsFlag: boolean;\n}\n\nexport interface ColumnsAction<T> {\n\ttype: 'UPDATE_COLUMNS';\n\tcols: TableColumn<T>[];\n}\n\nexport type Action<T> =\n\t| AllRowsAction<T>\n\t| SingleRowAction<T>\n\t| MultiRowAction<T>\n\t| SortAction<T>\n\t| PaginationPageAction\n\t| PaginationRowsPerPageAction\n\t| ClearSelectedRowsAction;\n", "import { CSSObject } from 'styled-components';\nimport { ConditionalStyles, TableColumn, Format, TableRow, Selector, SortOrder, SortFunction } from './types';\n\nexport function prop<T, K extends keyof T>(obj: T, key: K): T[K] {\n\treturn obj[key];\n}\n\nexport function isEmpty(field: string | number | undefined = ''): boolean {\n\tif (typeof field === 'number') {\n\t\treturn false;\n\t}\n\n\treturn !field || field.length === 0;\n}\n\nexport function sort<T>(\n\trows: T[],\n\tselector: Selector<T> | null | undefined,\n\tdirection: SortOrder,\n\tsortFn?: SortFunction<T> | null,\n): T[] {\n\tif (!selector) {\n\t\treturn rows;\n\t}\n\n\tif (sortFn && typeof sortFn === 'function') {\n\t\t// we must create a new rows reference\n\t\treturn sortFn(rows.slice(0), selector, direction);\n\t}\n\n\treturn rows.slice(0).sort((a: T, b: T) => {\n\t\tconst aValue = selector(a);\n\t\tconst bValue = selector(b);\n\n\t\tif (direction === 'asc') {\n\t\t\tif (aValue < bValue) {\n\t\t\t\treturn -1;\n\t\t\t}\n\n\t\t\tif (aValue > bValue) {\n\t\t\t\treturn 1;\n\t\t\t}\n\t\t}\n\n\t\tif (direction === 'desc') {\n\t\t\tif (aValue > bValue) {\n\t\t\t\treturn -1;\n\t\t\t}\n\n\t\t\tif (aValue < bValue) {\n\t\t\t\treturn 1;\n\t\t\t}\n\t\t}\n\n\t\treturn 0;\n\t});\n}\n\nexport function getProperty<T>(\n\trow: T,\n\t// TODO: remove string type in V8\n\tselector: Selector<T> | undefined | null,\n\tformat: Format<T> | undefined | null,\n\trowIndex: number,\n): React.ReactNode {\n\tif (!selector) {\n\t\treturn null;\n\t}\n\n\t// format will override how the selector is displayed but the original dataset is used for sorting\n\tif (format && typeof format === 'function') {\n\t\treturn format(row, rowIndex);\n\t}\n\n\treturn selector(row, rowIndex);\n}\n\nexport function insertItem<T>(array: T[] = [], item: T, index = 0): T[] {\n\treturn [...array.slice(0, index), item, ...array.slice(index)];\n}\n\nexport function removeItem<T>(array: T[] = [], item: T, keyField = 'id'): T[] {\n\tconst newArray = array.slice();\n\tconst outerField = prop(item as TableRow, keyField);\n\n\tif (outerField) {\n\t\tnewArray.splice(\n\t\t\tnewArray.findIndex((a: T) => {\n\t\t\t\tconst innerField = prop(a as TableRow, keyField);\n\n\t\t\t\treturn innerField === outerField;\n\t\t\t}),\n\t\t\t1,\n\t\t);\n\t} else {\n\t\tnewArray.splice(\n\t\t\tnewArray.findIndex(a => a === item),\n\t\t\t1,\n\t\t);\n\t}\n\n\treturn newArray;\n}\n\n// Make sure columns have unique id's\nexport function decorateColumns<T>(columns: TableColumn<T>[]): TableColumn<T>[] {\n\treturn columns.map((column, index) => {\n\t\tconst decoratedColumn: TableColumn<T> = {\n\t\t\t...column,\n\t\t\tsortable: column.sortable || !!column.sortFunction || undefined,\n\t\t};\n\n\t\tif (!column.id) {\n\t\t\tdecoratedColumn.id = index + 1;\n\n\t\t\treturn decoratedColumn;\n\t\t}\n\n\t\treturn decoratedColumn;\n\t});\n}\n\nexport function getSortDirection(ascDirection: boolean | undefined = false): SortOrder {\n\treturn ascDirection ? SortOrder.ASC : SortOrder.DESC;\n}\n\nexport function handleFunctionProps(\n\tobject: { [key: string]: unknown },\n\t...args: unknown[]\n): { [key: string]: unknown } {\n\tlet newObject;\n\n\tObject.keys(object)\n\t\t.map(o => object[o])\n\t\t.forEach((value, index) => {\n\t\t\tconst oldObject = object;\n\n\t\t\tif (typeof value === 'function') {\n\t\t\t\tnewObject = { ...oldObject, [Object.keys(object)[index]]: value(...args) };\n\t\t\t\t// delete oldObject[value];\n\t\t\t}\n\t\t});\n\n\treturn newObject || object;\n}\n\nexport function getNumberOfPages(rowCount: number, rowsPerPage: number): number {\n\treturn Math.ceil(rowCount / rowsPerPage);\n}\n\nexport function recalculatePage(prevPage: number, nextPage: number): number {\n\treturn Math.min(prevPage, nextPage);\n}\n\nexport const noop = (): null => null;\n\nexport function getConditionalStyle<T>(\n\trow: T,\n\tconditionalRowStyles: ConditionalStyles<T>[] = [],\n\tbaseClassNames: string[] = [],\n): { conditionalStyle: CSSObject; classNames: string } {\n\tlet rowStyle = {};\n\tlet classNames: string[] = [...baseClassNames];\n\n\tif (conditionalRowStyles.length) {\n\t\tconditionalRowStyles.forEach(crs => {\n\t\t\tif (!crs.when || typeof crs.when !== 'function') {\n\t\t\t\tthrow new Error('\"when\" must be defined in the conditional style object and must be function');\n\t\t\t}\n\n\t\t\t// evaluate the field and if true return a the style to be applied\n\t\t\tif (crs.when(row)) {\n\t\t\t\trowStyle = crs.style || {};\n\n\t\t\t\tif (crs.classNames) {\n\t\t\t\t\tclassNames = [...classNames, ...crs.classNames];\n\t\t\t\t}\n\n\t\t\t\tif (typeof crs.style === 'function') {\n\t\t\t\t\trowStyle = crs.style(row) || {};\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n\n\treturn { conditionalStyle: rowStyle, classNames: classNames.join(' ') };\n}\n\nexport function isRowSelected<T>(row: T, selectedRows: T[] = [], keyField = 'id'): boolean {\n\t// cast row as TableRow because the property is unknown in advance therefore, typescript will throw an error\n\tconst outerField = prop(row as TableRow, keyField);\n\n\tif (outerField) {\n\t\treturn selectedRows.some(r => {\n\t\t\tconst innerField = prop(r as TableRow, keyField);\n\n\t\t\treturn innerField === outerField;\n\t\t});\n\t}\n\n\treturn selectedRows.some(r => r === row);\n}\n\nexport function isOdd(num: number): boolean {\n\treturn num % 2 === 0;\n}\n\nexport function findColumnIndexById<T>(columns: TableColumn<T>[], id: string | undefined): number {\n\tif (!id) {\n\t\treturn -1;\n\t}\n\n\treturn columns.findIndex(c => {\n\t\treturn equalizeId(c.id, id);\n\t});\n}\n\nexport function equalizeId(a: string | number | undefined, b: string | number | undefined): boolean {\n\treturn a == b;\n}\n", "import { insertItem, isRowSelected, removeItem } from './util';\nimport { Action, TableState } from './types';\n\nexport function tableReducer<T>(state: TableState<T>, action: Action<T>): TableState<T> {\n\tconst toggleOnSelectedRowsChange = !state.toggleOnSelectedRowsChange;\n\n\tswitch (action.type) {\n\t\tcase 'SELECT_ALL_ROWS': {\n\t\t\tconst { keyField, rows, rowCount, mergeSelections } = action;\n\t\t\tconst allChecked = !state.allSelected;\n\t\t\tconst toggleOnSelectedRowsChange = !state.toggleOnSelectedRowsChange;\n\n\t\t\tif (mergeSelections) {\n\t\t\t\tconst selections = allChecked\n\t\t\t\t\t? [...state.selectedRows, ...rows.filter(row => !isRowSelected(row, state.selectedRows, keyField))]\n\t\t\t\t\t: state.selectedRows.filter(row => !isRowSelected(row, rows, keyField));\n\n\t\t\t\treturn {\n\t\t\t\t\t...state,\n\t\t\t\t\tallSelected: allChecked,\n\t\t\t\t\tselectedCount: selections.length,\n\t\t\t\t\tselectedRows: selections,\n\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tallSelected: allChecked,\n\t\t\t\tselectedCount: allChecked ? rowCount : 0,\n\t\t\t\tselectedRows: allChecked ? rows : [],\n\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t};\n\t\t}\n\n\t\tcase 'SELECT_SINGLE_ROW': {\n\t\t\tconst { keyField, row, isSelected, rowCount, singleSelect } = action;\n\n\t\t\t// handle single select mode\n\t\t\tif (singleSelect) {\n\t\t\t\tif (isSelected) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...state,\n\t\t\t\t\t\tselectedCount: 0,\n\t\t\t\t\t\tallSelected: false,\n\t\t\t\t\t\tselectedRows: [],\n\t\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\treturn {\n\t\t\t\t\t...state,\n\t\t\t\t\tselectedCount: 1,\n\t\t\t\t\tallSelected: false,\n\t\t\t\t\tselectedRows: [row],\n\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// handle multi select mode\n\t\t\tif (isSelected) {\n\t\t\t\treturn {\n\t\t\t\t\t...state,\n\t\t\t\t\tselectedCount: state.selectedRows.length > 0 ? state.selectedRows.length - 1 : 0,\n\t\t\t\t\tallSelected: false,\n\t\t\t\t\tselectedRows: removeItem(state.selectedRows, row, keyField),\n\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tselectedCount: state.selectedRows.length + 1,\n\t\t\t\tallSelected: state.selectedRows.length + 1 === rowCount,\n\t\t\t\tselectedRows: insertItem(state.selectedRows, row),\n\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t};\n\t\t}\n\n\t\tcase 'SELECT_MULTIPLE_ROWS': {\n\t\t\tconst { keyField, selectedRows, totalRows, mergeSelections } = action;\n\n\t\t\tif (mergeSelections) {\n\t\t\t\tconst selections = [\n\t\t\t\t\t...state.selectedRows,\n\t\t\t\t\t...selectedRows.filter(row => !isRowSelected(row, state.selectedRows, keyField)),\n\t\t\t\t];\n\n\t\t\t\treturn {\n\t\t\t\t\t...state,\n\t\t\t\t\tselectedCount: selections.length,\n\t\t\t\t\tallSelected: false,\n\t\t\t\t\tselectedRows: selections,\n\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tselectedCount: selectedRows.length,\n\t\t\t\tallSelected: selectedRows.length === totalRows,\n\t\t\t\tselectedRows,\n\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t};\n\t\t}\n\n\t\tcase 'CLEAR_SELECTED_ROWS': {\n\t\t\tconst { selectedRowsFlag } = action;\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tallSelected: false,\n\t\t\t\tselectedCount: 0,\n\t\t\t\tselectedRows: [],\n\t\t\t\tselectedRowsFlag,\n\t\t\t};\n\t\t}\n\n\t\tcase 'SORT_CHANGE': {\n\t\t\tconst { sortDirection, selectedColumn, clearSelectedOnSort } = action;\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tselectedColumn,\n\t\t\t\tsortDirection,\n\t\t\t\tcurrentPage: 1,\n\t\t\t\t// when using server-side paging reset selected row counts when sorting\n\t\t\t\t...(clearSelectedOnSort && {\n\t\t\t\t\tallSelected: false,\n\t\t\t\t\tselectedCount: 0,\n\t\t\t\t\tselectedRows: [],\n\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t}),\n\t\t\t};\n\t\t}\n\n\t\tcase 'CHANGE_PAGE': {\n\t\t\tconst { page, paginationServer, visibleOnly, persistSelectedOnPageChange } = action;\n\t\t\tconst mergeSelections = paginationServer && persistSelectedOnPageChange;\n\t\t\tconst clearSelectedOnPage = (paginationServer && !persistSelectedOnPageChange) || visibleOnly;\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tcurrentPage: page,\n\t\t\t\t...(mergeSelections && {\n\t\t\t\t\tallSelected: false,\n\t\t\t\t}),\n\t\t\t\t// when using server-side paging reset selected row counts\n\t\t\t\t...(clearSelectedOnPage && {\n\t\t\t\t\tallSelected: false,\n\t\t\t\t\tselectedCount: 0,\n\t\t\t\t\tselectedRows: [],\n\t\t\t\t\ttoggleOnSelectedRowsChange,\n\t\t\t\t}),\n\t\t\t};\n\t\t}\n\n\t\tcase 'CHANGE_ROWS_PER_PAGE': {\n\t\t\tconst { rowsPerPage, page } = action;\n\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tcurrentPage: page,\n\t\t\t\trowsPerPage,\n\t\t\t};\n\t\t}\n\t}\n}\n", "import styled, { css } from 'styled-components';\n\nconst disabledCSS = css`\n\tpointer-events: none;\n\topacity: 0.4;\n`;\n\nconst TableStyle = styled.div<{\n\tdisabled?: boolean;\n}>`\n\tposition: relative;\n\tbox-sizing: border-box;\n\tdisplay: flex;\n\tflex-direction: column;\n\twidth: 100%;\n\theight: 100%;\n\tmax-width: 100%;\n\t${({ disabled }) => disabled && disabledCSS};\n\t${({ theme }) => theme.table.style};\n`;\n\nexport default TableStyle;\n", "import styled, { css } from 'styled-components';\n\nconst fixedCSS = css`\n\tposition: sticky;\n\tposition: -webkit-sticky; /* Safari */\n\ttop: 0;\n\tz-index: 1;\n`;\n\nconst Head = styled.div<{\n\t$fixedHeader?: boolean;\n}>`\n\tdisplay: flex;\n\twidth: 100%;\n\t${({ $fixedHeader }) => $fixedHeader && fixedCSS};\n\t${({ theme }) => theme.head.style};\n`;\n\nexport default Head;\n", "import styled from 'styled-components';\n\nconst HeadRow = styled.div<{\n\t$dense?: boolean;\n\tdisabled?: boolean;\n}>`\n\tdisplay: flex;\n\talign-items: stretch;\n\twidth: 100%;\n\t${({ theme }) => theme.headRow.style};\n\t${({ $dense, theme }) => $dense && theme.headRow.denseStyle};\n`;\n\nexport default HeadRow;\n", "import { css, CSSObject, RuleSet } from 'styled-components';\n\nexport const SMALL = 599;\nexport const MEDIUM = 959;\nexport const LARGE = 1280;\n\nexport const media = {\n\tsm: (literals: TemplateStringsArray, ...args: CSSObject[]): RuleSet<object> => css`\n\t\t@media screen and (max-width: ${SMALL}px) {\n\t\t\t${css(literals, ...args)}\n\t\t}\n\t`,\n\tmd: (literals: TemplateStringsArray, ...args: CSSObject[]): RuleSet<object> => css`\n\t\t@media screen and (max-width: ${MEDIUM}px) {\n\t\t\t${css(literals, ...args)}\n\t\t}\n\t`,\n\tlg: (literals: TemplateStringsArray, ...args: CSSObject[]): RuleSet<object> => css`\n\t\t@media screen and (max-width: ${LARGE}px) {\n\t\t\t${css(literals, ...args)}\n\t\t}\n\t`,\n\tcustom:\n\t\t(value: number) =>\n\t\t(literals: TemplateStringsArray, ...args: CSSObject[]): RuleSet<object> => css`\n\t\t\t@media screen and (max-width: ${value}px) {\n\t\t\t\t${css(literals, ...args)}\n\t\t\t}\n\t\t`,\n};\n", "import styled, { css } from 'styled-components';\nimport { media } from './media';\nimport { TableColumnBase } from './types';\n\nexport const CellBase = styled.div<{\n\t$headCell?: boolean;\n\t$noPadding?: boolean;\n}>`\n\tposition: relative;\n\tdisplay: flex;\n\talign-items: center;\n\tbox-sizing: border-box;\n\tline-height: normal;\n\t${({ theme, $headCell }) => theme[$headCell ? 'headCells' : 'cells'].style};\n\t${({ $noPadding }) => $noPadding && 'padding: 0'};\n`;\n\nexport type CellProps = Pick<\n\tTableColumnBase,\n\t'button' | 'grow' | 'maxWidth' | 'minWidth' | 'width' | 'right' | 'center' | 'compact' | 'hide' | 'allowOverflow'\n>;\n\n// Flex calculations\nexport const CellExtended = styled(CellBase)<CellProps>`\n\tflex-grow: ${({ button, grow }) => (grow === 0 || button ? 0 : grow || 1)};\n\tflex-shrink: 0;\n\tflex-basis: 0;\n\tmax-width: ${({ maxWidth }) => maxWidth || '100%'};\n\tmin-width: ${({ minWidth }) => minWidth || '100px'};\n\t${({ width }) =>\n\t\twidth &&\n\t\tcss`\n\t\t\tmin-width: ${width};\n\t\t\tmax-width: ${width};\n\t\t`};\n\t${({ right }) => right && 'justify-content: flex-end'};\n\t${({ button, center }) => (center || button) && 'justify-content: center'};\n\t${({ compact, button }) => (compact || button) && 'padding: 0'};\n\n\t/* handle hiding cells */\n\t${({ hide }) =>\n\t\thide &&\n\t\thide === 'sm' &&\n\t\tmedia.sm`\n    display: none;\n  `};\n\t${({ hide }) =>\n\t\thide &&\n\t\thide === 'md' &&\n\t\tmedia.md`\n    display: none;\n  `};\n\t${({ hide }) =>\n\t\thide &&\n\t\thide === 'lg' &&\n\t\tmedia.lg`\n    display: none;\n  `};\n\t${({ hide }) =>\n\t\thide &&\n\t\tNumber.isInteger(hide) &&\n\t\tmedia.custom(hide as number)`\n    display: none;\n  `};\n`;\n", "import * as React from 'react';\nimport styled, { css, CSSObject } from 'styled-components';\nimport { CellExtended } from './Cell';\nimport { getProperty, getConditionalStyle } from './util';\nimport { TableColumn } from './types';\n\ninterface CellStyleProps {\n\t$renderAsCell: boolean | undefined;\n\t$wrapCell: boolean | undefined;\n\t$allowOverflow: boolean | undefined;\n\t$cellStyle: CSSObject | undefined;\n\t$isDragging: boolean;\n}\n\nconst overflowCSS = css<CellStyleProps>`\n\tdiv:first-child {\n\t\twhite-space: ${({ $wrapCell }) => ($wrapCell ? 'normal' : 'nowrap')};\n\t\toverflow: ${({ $allowOverflow }) => ($allowOverflow ? 'visible' : 'hidden')};\n\t\ttext-overflow: ellipsis;\n\t}\n`;\n\nconst CellStyle = styled(CellExtended).attrs(props => ({\n\tstyle: props.style,\n}))<CellStyleProps>`\n\t${({ $renderAsCell }) => !$renderAsCell && overflowCSS};\n\t${({ theme, $isDragging }) => $isDragging && theme.cells.draggingStyle};\n\t${({ $cellStyle }) => $cellStyle};\n`;\n\ninterface CellProps<T> {\n\tid: string;\n\tdataTag: string | null;\n\tcolumn: TableColumn<T>;\n\trow: T;\n\trowIndex: number;\n\tisDragging: boolean;\n\tonDragStart: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragOver: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnd: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnter: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;\n}\n\nfunction Cell<T>({\n\tid,\n\tcolumn,\n\trow,\n\trowIndex,\n\tdataTag,\n\tisDragging,\n\tonDragStart,\n\tonDragOver,\n\tonDragEnd,\n\tonDragEnter,\n\tonDragLeave,\n}: CellProps<T>): JSX.Element {\n\tconst { conditionalStyle, classNames } = getConditionalStyle(row, column.conditionalCellStyles, ['rdt_TableCell']);\n\n\treturn (\n\t\t<CellStyle\n\t\t\tid={id}\n\t\t\tdata-column-id={column.id}\n\t\t\trole=\"cell\"\n\t\t\tclassName={classNames}\n\t\t\tdata-tag={dataTag}\n\t\t\t$cellStyle={column.style}\n\t\t\t$renderAsCell={!!column.cell}\n\t\t\t$allowOverflow={column.allowOverflow}\n\t\t\tbutton={column.button}\n\t\t\tcenter={column.center}\n\t\t\tcompact={column.compact}\n\t\t\tgrow={column.grow}\n\t\t\thide={column.hide}\n\t\t\tmaxWidth={column.maxWidth}\n\t\t\tminWidth={column.minWidth}\n\t\t\tright={column.right}\n\t\t\twidth={column.width}\n\t\t\t$wrapCell={column.wrap}\n\t\t\tstyle={conditionalStyle as React.CSSProperties}\n\t\t\t$isDragging={isDragging}\n\t\t\tonDragStart={onDragStart}\n\t\t\tonDragOver={onDragOver}\n\t\t\tonDragEnd={onDragEnd}\n\t\t\tonDragEnter={onDragEnter}\n\t\t\tonDragLeave={onDragLeave}\n\t\t>\n\t\t\t{!column.cell && <div data-tag={dataTag}>{getProperty(row, column.selector, column.format, rowIndex)}</div>}\n\t\t\t{column.cell && column.cell(row, rowIndex, column, id)}\n\t\t</CellStyle>\n\t);\n}\n\nexport default React.memo(Cell) as typeof Cell;\n", "import * as React from 'react';\nimport { handleFunctionProps, noop } from './util';\n\nconst defaultComponentName = 'input';\n\nconst calculateBaseStyle = (disabled: boolean) => ({\n\tfontSize: '18px',\n\t...(!disabled && { cursor: 'pointer' }),\n\tpadding: 0,\n\tmarginTop: '1px',\n\tverticalAlign: 'middle',\n\tposition: 'relative',\n});\n\ninterface CheckboxProps {\n\tname: string;\n\t// eslint-disable-next-line @typescript-eslint/no-explicit-any\n\tcomponent?: any;\n\tcomponentOptions?: { [key: string]: unknown };\n\tindeterminate?: boolean;\n\tchecked?: boolean;\n\tdisabled?: boolean;\n\tonClick?: (e: React.MouseEvent) => void;\n}\n\nfunction Checkbox({\n\tname,\n\tcomponent = defaultComponentName,\n\tcomponentOptions = { style: {} },\n\tindeterminate = false,\n\tchecked = false,\n\tdisabled = false,\n\tonClick = noop,\n}: CheckboxProps): JSX.Element {\n\tconst setCheckboxRef = (checkbox: HTMLInputElement) => {\n\t\tif (checkbox) {\n\t\t\t// eslint-disable-next-line no-param-reassign\n\t\t\tcheckbox.indeterminate = indeterminate;\n\t\t}\n\t};\n\n\tconst TagName = component;\n\tconst baseStyle = TagName !== defaultComponentName ? componentOptions.style : calculateBaseStyle(disabled);\n\tconst resolvedComponentOptions = React.useMemo(\n\t\t() => handleFunctionProps(componentOptions, indeterminate),\n\t\t[componentOptions, indeterminate],\n\t);\n\n\treturn (\n\t\t<TagName\n\t\t\t// allow this component to fully control these options\n\t\t\ttype=\"checkbox\"\n\t\t\tref={setCheckboxRef}\n\t\t\tstyle={baseStyle}\n\t\t\tonClick={disabled ? noop : onClick}\n\t\t\tname={name}\n\t\t\taria-label={name}\n\t\t\tchecked={checked}\n\t\t\tdisabled={disabled}\n\t\t\t{...resolvedComponentOptions}\n\t\t\tonChange={noop} // prevent uncontrolled checkbox warnings -  we don't need onChange\n\t\t/>\n\t);\n}\n\nexport default React.memo(Checkbox);\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport { CellBase } from './Cell';\nimport Checkbox from './Checkbox';\nimport { RowState, SingleRowAction, ComponentProps } from './types';\n\nconst TableCellCheckboxStyle = styled(CellBase)`\n\tflex: 0 0 48px;\n\tmin-width: 48px;\n\tjustify-content: center;\n\talign-items: center;\n\tuser-select: none;\n\twhite-space: nowrap;\n`;\n\ntype TableCellCheckboxProps<T> = {\n\tname: string;\n\tkeyField: string;\n\trow: T;\n\trowCount: number;\n\tselected: boolean;\n\tselectableRowsComponent: 'input' | React.ReactNode;\n\tselectableRowsComponentProps: ComponentProps;\n\tselectableRowsSingle: boolean;\n\tselectableRowDisabled: RowState<T>;\n\tonSelectedRow: (action: SingleRowAction<T>) => void;\n};\n\nfunction TableCellCheckbox<T>({\n\tname,\n\tkeyField,\n\trow,\n\trowCount,\n\tselected,\n\tselectableRowsComponent,\n\tselectableRowsComponentProps,\n\tselectableRowsSingle,\n\tselectableRowDisabled,\n\tonSelectedRow,\n}: TableCellCheckboxProps<T>): JSX.Element {\n\tconst disabled = !!(selectableRowDisabled && selectableRowDisabled(row));\n\n\tconst handleOnRowSelected = () => {\n\t\tonSelectedRow({\n\t\t\ttype: 'SELECT_SINGLE_ROW',\n\t\t\trow,\n\t\t\tisSelected: selected,\n\t\t\tkeyField,\n\t\t\trowCount,\n\t\t\tsingleSelect: selectableRowsSingle,\n\t\t});\n\t};\n\n\treturn (\n\t\t<TableCellCheckboxStyle onClick={(e: React.MouseEvent) => e.stopPropagation()} className=\"rdt_TableCell\" $noPadding>\n\t\t\t<Checkbox\n\t\t\t\tname={name}\n\t\t\t\tcomponent={selectableRowsComponent}\n\t\t\t\tcomponentOptions={selectableRowsComponentProps}\n\t\t\t\tchecked={selected}\n\t\t\t\taria-checked={selected}\n\t\t\t\tonClick={handleOnRowSelected}\n\t\t\t\tdisabled={disabled}\n\t\t\t/>\n\t\t</TableCellCheckboxStyle>\n\t);\n}\n\nexport default TableCellCheckbox;\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport { ExpandableIcon } from './types';\n\nconst ButtonStyle = styled.button`\n\tdisplay: inline-flex;\n\talign-items: center;\n\tuser-select: none;\n\twhite-space: nowrap;\n\tborder: none;\n\tbackground-color: transparent;\n\t${({ theme }) => theme.expanderButton.style};\n`;\n\ntype ExpanderButtonProps<T> = {\n\tdisabled?: boolean;\n\texpanded?: boolean;\n\texpandableIcon: ExpandableIcon;\n\tid: string | number;\n\trow: T;\n\tonToggled?: (row: T) => void;\n};\n\nfunction ExpanderButton<T>({\n\tdisabled = false,\n\texpanded = false,\n\texpandableIcon,\n\tid,\n\trow,\n\tonToggled,\n}: ExpanderButtonProps<T>): JSX.Element {\n\tconst icon = expanded ? expandableIcon.expanded : expandableIcon.collapsed;\n\tconst handleToggle = () => onToggled && onToggled(row);\n\n\treturn (\n\t\t<ButtonStyle\n\t\t\taria-disabled={disabled}\n\t\t\tonClick={handleToggle}\n\t\t\tdata-testid={`expander-button-${id}`}\n\t\t\tdisabled={disabled}\n\t\t\taria-label={expanded ? 'Collapse Row' : 'Expand Row'}\n\t\t\trole=\"button\"\n\t\t\ttype=\"button\"\n\t\t>\n\t\t\t{icon}\n\t\t</ButtonStyle>\n\t);\n}\n\nexport default ExpanderButton;\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport { CellBase } from './Cell';\nimport ExpanderButton from './ExpanderButton';\nimport { ExpandableIcon } from './types';\n\nconst CellExpanderStyle = styled(CellBase)`\n\twhite-space: nowrap;\n\tfont-weight: 400;\n\tmin-width: 48px;\n\t${({ theme }) => theme.expanderCell.style};\n`;\n\ntype CellExpanderProps<T> = {\n\tdisabled: boolean;\n\texpanded: boolean;\n\texpandableIcon: ExpandableIcon;\n\tid: string | number;\n\trow: T;\n\tonToggled: (row: T) => void;\n};\n\nfunction CellExpander<T>({\n\trow,\n\texpanded = false,\n\texpandableIcon,\n\tid,\n\tonToggled,\n\tdisabled = false,\n}: CellExpanderProps<T>): JSX.Element {\n\treturn (\n\t\t<CellExpanderStyle onClick={(e: React.MouseEvent) => e.stopPropagation()} $noPadding>\n\t\t\t<ExpanderButton\n\t\t\t\tid={id}\n\t\t\t\trow={row}\n\t\t\t\texpanded={expanded}\n\t\t\t\texpandableIcon={expandableIcon}\n\t\t\t\tdisabled={disabled}\n\t\t\t\tonToggled={onToggled}\n\t\t\t/>\n\t\t</CellExpanderStyle>\n\t);\n}\n\nexport default CellExpander;\n", "import * as React from 'react';\nimport styled, { CSSObject } from 'styled-components';\nimport { ComponentProps, ExpandableRowsComponent } from './types';\n\nconst ExpanderRowStyle = styled.div<{\n\t$extendedRowStyle: CSSObject;\n}>`\n\twidth: 100%;\n\tbox-sizing: border-box;\n\t${({ theme }) => theme.expanderRow.style};\n\t${({ $extendedRowStyle }) => $extendedRowStyle};\n`;\n\ntype ExpanderRowProps<T> = {\n\tdata: T;\n\tExpanderComponent: ExpandableRowsComponent<T>;\n\textendedRowStyle: CSSObject;\n\textendedClassNames: string;\n\texpanderComponentProps: ComponentProps;\n};\n\nfunction ExpanderRow<T>({\n\tdata,\n\tExpanderComponent,\n\texpanderComponentProps,\n\textendedRowStyle,\n\textendedClassNames,\n}: ExpanderRowProps<T>): JSX.Element {\n\t// we need to strip of rdt_TableRow from extendedClassNames\n\tconst classNamesSplit = extendedClassNames.split(' ').filter(c => c !== 'rdt_TableRow');\n\tconst classNames = ['rdt_ExpanderRow', ...classNamesSplit].join(' ');\n\n\treturn (\n\t\t<ExpanderRowStyle className={classNames} $extendedRowStyle={extendedRowStyle as CSSObject}>\n\t\t\t<ExpanderComponent data={data} {...expanderComponentProps} />\n\t\t</ExpanderRowStyle>\n\t);\n}\n\nexport default React.memo(ExpanderRow) as typeof ExpanderRow;\n", "export const STOP_PROP_TAG = 'allowRowEvents';\n\nexport enum Direction {\n\tLTR = 'ltr',\n\tRTL = 'rtl',\n\tAUTO = 'auto',\n}\n\nexport enum Alignment {\n\tLEFT = 'left',\n\tRIGHT = 'right',\n\tCENTER = 'center',\n}\n\nexport enum Media {\n\tSM = 'sm',\n\tMD = 'md',\n\tLG = 'lg',\n}\n", "import * as React from 'react';\nimport styled, { css } from 'styled-components';\nimport TableCell from './TableCell';\nimport TableCellCheckbox from './TableCellCheckbox';\nimport TableCellExpander from './TableCellExpander';\nimport ExpanderRow from './ExpanderRow';\nimport { prop, equalizeId, getConditionalStyle, isOdd, noop } from './util';\nimport { STOP_PROP_TAG } from './constants';\nimport { TableRow, SingleRowAction, TableProps } from './types';\nimport { CSSObject } from 'styled-components';\n\nconst highlightCSS = css<{\n\t$highlightOnHover?: boolean;\n}>`\n\t&:hover {\n\t\t${({ $highlightOnHover, theme }) => $highlightOnHover && theme.rows.highlightOnHoverStyle};\n\t}\n`;\n\nconst pointerCSS = css`\n\t&:hover {\n\t\tcursor: pointer;\n\t}\n`;\n\nconst TableRowStyle = styled.div.attrs(props => ({\n\tstyle: props.style,\n}))<{\n\t$dense?: boolean;\n\t$highlightOnHover?: boolean;\n\t$pointerOnHover?: boolean;\n\t$selected?: boolean;\n\t$striped?: boolean;\n\t$conditionalStyle?: CSSObject;\n}>`\n\tdisplay: flex;\n\talign-items: stretch;\n\talign-content: stretch;\n\twidth: 100%;\n\tbox-sizing: border-box;\n\t${({ theme }) => theme.rows.style};\n\t${({ $dense, theme }) => $dense && theme.rows.denseStyle};\n\t${({ $striped, theme }) => $striped && theme.rows.stripedStyle};\n\t${({ $highlightOnHover }) => $highlightOnHover && highlightCSS};\n\t${({ $pointerOnHover }) => $pointerOnHover && pointerCSS};\n\t${({ $selected, theme }) => $selected && theme.rows.selectedHighlightStyle};\n\t${({ $conditionalStyle }) => $conditionalStyle};\n`;\n\ntype DProps<T> = Pick<\n\tTableProps<T>,\n\t| 'columns'\n\t| 'conditionalRowStyles'\n\t| 'dense'\n\t| 'expandableIcon'\n\t| 'expandableRows'\n\t| 'expandableRowsComponent'\n\t| 'expandableRowsComponentProps'\n\t| 'expandableRowsHideExpander'\n\t| 'expandOnRowClicked'\n\t| 'expandOnRowDoubleClicked'\n\t| 'highlightOnHover'\n\t| 'expandableInheritConditionalStyles'\n\t| 'keyField'\n\t| 'onRowClicked'\n\t| 'onRowDoubleClicked'\n\t| 'onRowMouseEnter'\n\t| 'onRowMouseLeave'\n\t| 'onRowExpandToggled'\n\t| 'pointerOnHover'\n\t| 'selectableRowDisabled'\n\t| 'selectableRows'\n\t| 'selectableRowsComponent'\n\t| 'selectableRowsComponentProps'\n\t| 'selectableRowsHighlight'\n\t| 'selectableRowsSingle'\n\t| 'striped'\n>;\n\ninterface TableRowProps<T> extends Required<DProps<T>> {\n\tdraggingColumnId: number | string;\n\tdefaultExpanded?: boolean;\n\tdefaultExpanderDisabled: boolean;\n\tid: string | number;\n\tonSelectedRow: (action: SingleRowAction<T>) => void;\n\tpointerOnHover: boolean;\n\trow: T;\n\trowCount: number;\n\trowIndex: number;\n\tselected: boolean;\n\tonDragStart: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragOver: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnd: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnter: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;\n}\n\nfunction Row<T>({\n\tcolumns = [],\n\tconditionalRowStyles = [],\n\tdefaultExpanded = false,\n\tdefaultExpanderDisabled = false,\n\tdense = false,\n\texpandableIcon,\n\texpandableRows = false,\n\texpandableRowsComponent,\n\texpandableRowsComponentProps,\n\texpandableRowsHideExpander,\n\texpandOnRowClicked = false,\n\texpandOnRowDoubleClicked = false,\n\thighlightOnHover = false,\n\tid,\n\texpandableInheritConditionalStyles,\n\tkeyField,\n\tonRowClicked = noop,\n\tonRowDoubleClicked = noop,\n\tonRowMouseEnter = noop,\n\tonRowMouseLeave = noop,\n\tonRowExpandToggled = noop,\n\tonSelectedRow = noop,\n\tpointerOnHover = false,\n\trow,\n\trowCount,\n\trowIndex,\n\tselectableRowDisabled = null,\n\tselectableRows = false,\n\tselectableRowsComponent,\n\tselectableRowsComponentProps,\n\tselectableRowsHighlight = false,\n\tselectableRowsSingle = false,\n\tselected,\n\tstriped = false,\n\tdraggingColumnId,\n\tonDragStart,\n\tonDragOver,\n\tonDragEnd,\n\tonDragEnter,\n\tonDragLeave,\n}: TableRowProps<T>): JSX.Element {\n\tconst [expanded, setExpanded] = React.useState(defaultExpanded);\n\n\tReact.useEffect(() => {\n\t\tsetExpanded(defaultExpanded);\n\t}, [defaultExpanded]);\n\n\tconst handleExpanded = React.useCallback(() => {\n\t\tsetExpanded(!expanded);\n\t\tonRowExpandToggled(!expanded, row);\n\t}, [expanded, onRowExpandToggled, row]);\n\n\tconst showPointer = pointerOnHover || (expandableRows && (expandOnRowClicked || expandOnRowDoubleClicked));\n\n\tconst handleRowClick = React.useCallback(\n\t\t(e: React.MouseEvent<HTMLDivElement>) => {\n\t\t\t// use event delegation allow events to propagate only when the element with data-tag STOP_PROP_TAG is present\n\t\t\tconst target = e.target as HTMLDivElement;\n\n\t\t\tif (target.getAttribute('data-tag') === STOP_PROP_TAG) {\n\t\t\t\tonRowClicked(row, e);\n\n\t\t\t\tif (!defaultExpanderDisabled && expandableRows && expandOnRowClicked) {\n\t\t\t\t\thandleExpanded();\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t[defaultExpanderDisabled, expandOnRowClicked, expandableRows, handleExpanded, onRowClicked, row],\n\t);\n\n\tconst handleRowDoubleClick = React.useCallback(\n\t\t(e: React.MouseEvent<HTMLDivElement>) => {\n\t\t\tconst target = e.target as HTMLDivElement;\n\n\t\t\tif (target.getAttribute('data-tag') === STOP_PROP_TAG) {\n\t\t\t\tonRowDoubleClicked(row, e);\n\t\t\t\tif (!defaultExpanderDisabled && expandableRows && expandOnRowDoubleClicked) {\n\t\t\t\t\thandleExpanded();\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t[defaultExpanderDisabled, expandOnRowDoubleClicked, expandableRows, handleExpanded, onRowDoubleClicked, row],\n\t);\n\n\tconst handleRowMouseEnter = React.useCallback(\n\t\t(e: React.MouseEvent<Element, MouseEvent>) => {\n\t\t\tonRowMouseEnter(row, e);\n\t\t},\n\t\t[onRowMouseEnter, row],\n\t);\n\n\tconst handleRowMouseLeave = React.useCallback(\n\t\t(e: React.MouseEvent<Element, MouseEvent>) => {\n\t\t\tonRowMouseLeave(row, e);\n\t\t},\n\t\t[onRowMouseLeave, row],\n\t);\n\n\tconst rowKeyField = prop(row as TableRow, keyField);\n\tconst { conditionalStyle, classNames } = getConditionalStyle(row, conditionalRowStyles, ['rdt_TableRow']);\n\tconst highlightSelected = selectableRowsHighlight && selected;\n\tconst inheritStyles = expandableInheritConditionalStyles ? conditionalStyle : {};\n\tconst isStriped = striped && isOdd(rowIndex);\n\n\treturn (\n\t\t<>\n\t\t\t<TableRowStyle\n\t\t\t\tid={`row-${id}`}\n\t\t\t\trole=\"row\"\n\t\t\t\t$striped={isStriped}\n\t\t\t\t$highlightOnHover={highlightOnHover}\n\t\t\t\t$pointerOnHover={!defaultExpanderDisabled && showPointer}\n\t\t\t\t$dense={dense}\n\t\t\t\tonClick={handleRowClick}\n\t\t\t\tonDoubleClick={handleRowDoubleClick}\n\t\t\t\tonMouseEnter={handleRowMouseEnter}\n\t\t\t\tonMouseLeave={handleRowMouseLeave}\n\t\t\t\tclassName={classNames}\n\t\t\t\t$selected={highlightSelected}\n\t\t\t\t$conditionalStyle={conditionalStyle}\n\t\t\t>\n\t\t\t\t{selectableRows && (\n\t\t\t\t\t<TableCellCheckbox\n\t\t\t\t\t\tname={`select-row-${rowKeyField}`}\n\t\t\t\t\t\tkeyField={keyField}\n\t\t\t\t\t\trow={row}\n\t\t\t\t\t\trowCount={rowCount}\n\t\t\t\t\t\tselected={selected}\n\t\t\t\t\t\tselectableRowsComponent={selectableRowsComponent}\n\t\t\t\t\t\tselectableRowsComponentProps={selectableRowsComponentProps}\n\t\t\t\t\t\tselectableRowDisabled={selectableRowDisabled}\n\t\t\t\t\t\tselectableRowsSingle={selectableRowsSingle}\n\t\t\t\t\t\tonSelectedRow={onSelectedRow}\n\t\t\t\t\t/>\n\t\t\t\t)}\n\n\t\t\t\t{expandableRows && !expandableRowsHideExpander && (\n\t\t\t\t\t<TableCellExpander\n\t\t\t\t\t\tid={rowKeyField as string}\n\t\t\t\t\t\texpandableIcon={expandableIcon}\n\t\t\t\t\t\texpanded={expanded}\n\t\t\t\t\t\trow={row}\n\t\t\t\t\t\tonToggled={handleExpanded}\n\t\t\t\t\t\tdisabled={defaultExpanderDisabled}\n\t\t\t\t\t/>\n\t\t\t\t)}\n\n\t\t\t\t{columns.map(column => {\n\t\t\t\t\tif (column.omit) {\n\t\t\t\t\t\treturn null;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn (\n\t\t\t\t\t\t<TableCell\n\t\t\t\t\t\t\tid={`cell-${column.id}-${rowKeyField}`}\n\t\t\t\t\t\t\tkey={`cell-${column.id}-${rowKeyField}`}\n\t\t\t\t\t\t\t// apply a tag that Row will use to stop event propagation when TableCell is clicked\n\t\t\t\t\t\t\tdataTag={column.ignoreRowClick || column.button ? null : STOP_PROP_TAG}\n\t\t\t\t\t\t\tcolumn={column}\n\t\t\t\t\t\t\trow={row}\n\t\t\t\t\t\t\trowIndex={rowIndex}\n\t\t\t\t\t\t\tisDragging={equalizeId(draggingColumnId, column.id)}\n\t\t\t\t\t\t\tonDragStart={onDragStart}\n\t\t\t\t\t\t\tonDragOver={onDragOver}\n\t\t\t\t\t\t\tonDragEnd={onDragEnd}\n\t\t\t\t\t\t\tonDragEnter={onDragEnter}\n\t\t\t\t\t\t\tonDragLeave={onDragLeave}\n\t\t\t\t\t\t/>\n\t\t\t\t\t);\n\t\t\t\t})}\n\t\t\t</TableRowStyle>\n\n\t\t\t{expandableRows && expanded && (\n\t\t\t\t<ExpanderRow\n\t\t\t\t\tkey={`expander-${rowKeyField}`}\n\t\t\t\t\tdata={row}\n\t\t\t\t\textendedRowStyle={inheritStyles}\n\t\t\t\t\textendedClassNames={classNames}\n\t\t\t\t\tExpanderComponent={expandableRowsComponent}\n\t\t\t\t\texpanderComponentProps={expandableRowsComponentProps}\n\t\t\t\t/>\n\t\t\t)}\n\t\t</>\n\t);\n}\n\nexport default Row;\n", "import React from 'react';\nimport styled from 'styled-components';\nimport { SortOrder } from '../DataTable/types';\n\nconst Icon = styled.span<{\n\t$sortActive: boolean;\n\t$sortDirection: SortOrder;\n}>`\n\tpadding: 2px;\n\tcolor: inherit;\n\tflex-grow: 0;\n\tflex-shrink: 0;\n\t${({ $sortActive }) => ($sortActive ? 'opacity: 1' : 'opacity: 0')};\n\t${({ $sortDirection }) => $sortDirection === 'desc' && 'transform: rotate(180deg)'};\n`;\n\ninterface NativeSortIconProps {\n\tsortActive: boolean;\n\tsortDirection: SortOrder;\n}\n\nconst NativeSortIcon: React.FC<NativeSortIconProps> = ({ sortActive, sortDirection }) => (\n\t<Icon $sortActive={sortActive} $sortDirection={sortDirection}>\n\t\t&#9650;\n\t</Icon>\n);\n\nexport default NativeSortIcon;\n", "import * as React from 'react';\nimport styled, { css } from 'styled-components';\nimport { CellExtended, CellProps } from './Cell';\nimport NativeSortIcon from '../icons/NativeSortIcon';\nimport { equalizeId } from './util';\nimport { TableColumn, SortAction, SortOrder } from './types';\n\ninterface ColumnStyleProps extends CellProps {\n\t$isDragging?: boolean;\n\tonDragStart: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragOver: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnd: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnter: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;\n}\n\nconst ColumnStyled = styled(CellExtended)<ColumnStyleProps>`\n\t${({ button }) => button && 'text-align: center'};\n\t${({ theme, $isDragging }) => $isDragging && theme.headCells.draggingStyle};\n`;\n\ninterface ColumnSortableProps {\n\tdisabled: boolean;\n\t$sortActive: boolean;\n}\n\nconst sortableCSS = css<ColumnSortableProps>`\n\tcursor: pointer;\n\tspan.__rdt_custom_sort_icon__ {\n\t\ti,\n\t\tsvg {\n\t\t\ttransform: 'translate3d(0, 0, 0)';\n\t\t\t${({ $sortActive }) => ($sortActive ? 'opacity: 1' : 'opacity: 0')};\n\t\t\tcolor: inherit;\n\t\t\tfont-size: 18px;\n\t\t\theight: 18px;\n\t\t\twidth: 18px;\n\t\t\tbackface-visibility: hidden;\n\t\t\ttransform-style: preserve-3d;\n\t\t\ttransition-duration: 95ms;\n\t\t\ttransition-property: transform;\n\t\t}\n\n\t\t&.asc i,\n\t\t&.asc svg {\n\t\t\ttransform: rotate(180deg);\n\t\t}\n\t}\n\n\t${({ $sortActive }) =>\n\t\t!$sortActive &&\n\t\tcss`\n\t\t\t&:hover,\n\t\t\t&:focus {\n\t\t\t\topacity: 0.7;\n\n\t\t\t\tspan,\n\t\t\t\tspan.__rdt_custom_sort_icon__ * {\n\t\t\t\t\topacity: 0.7;\n\t\t\t\t}\n\t\t\t}\n\t\t`};\n`;\n\nconst ColumnSortable = styled.div<ColumnSortableProps>`\n\tdisplay: inline-flex;\n\talign-items: center;\n\tjustify-content: inherit;\n\theight: 100%;\n\twidth: 100%;\n\toutline: none;\n\tuser-select: none;\n\toverflow: hidden;\n\t${({ disabled }) => !disabled && sortableCSS};\n`;\n\nconst ColumnText = styled.div`\n\toverflow: hidden;\n\twhite-space: nowrap;\n\ttext-overflow: ellipsis;\n`;\n\ntype TableColProps<T> = {\n\tcolumn: TableColumn<T>;\n\tdisabled: boolean;\n\tdraggingColumnId?: string | number;\n\tsortIcon?: React.ReactNode;\n\tpagination: boolean;\n\tpaginationServer: boolean;\n\tpersistSelectedOnSort: boolean;\n\tselectedColumn: TableColumn<T>;\n\tsortDirection: SortOrder;\n\tsortServer: boolean;\n\tselectableRowsVisibleOnly: boolean;\n\tonSort: (action: SortAction<T>) => void;\n\tonDragStart: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragOver: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnd: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragEnter: (e: React.DragEvent<HTMLDivElement>) => void;\n\tonDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;\n};\n\nfunction TableCol<T>({\n\tcolumn,\n\tdisabled,\n\tdraggingColumnId,\n\tselectedColumn = {},\n\tsortDirection,\n\tsortIcon,\n\tsortServer,\n\tpagination,\n\tpaginationServer,\n\tpersistSelectedOnSort,\n\tselectableRowsVisibleOnly,\n\tonSort,\n\tonDragStart,\n\tonDragOver,\n\tonDragEnd,\n\tonDragEnter,\n\tonDragLeave,\n}: TableColProps<T>): JSX.Element | null {\n\tReact.useEffect(() => {\n\t\tif (typeof column.selector === 'string') {\n\t\t\tconsole.error(\n\t\t\t\t`Warning: ${column.selector} is a string based column selector which has been deprecated as of v7 and will be removed in v8. Instead, use a selector function e.g. row => row[field]...`,\n\t\t\t);\n\t\t}\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, []);\n\n\tconst [showTooltip, setShowTooltip] = React.useState(false);\n\tconst columnRef = React.useRef<HTMLDivElement | null>(null);\n\n\tReact.useEffect(() => {\n\t\tif (columnRef.current) {\n\t\t\tsetShowTooltip(columnRef.current.scrollWidth > columnRef.current.clientWidth);\n\t\t}\n\t}, [showTooltip]);\n\n\tif (column.omit) {\n\t\treturn null;\n\t}\n\n\tconst handleSortChange = () => {\n\t\tif (!column.sortable && !column.selector) {\n\t\t\treturn;\n\t\t}\n\n\t\tlet direction = sortDirection;\n\n\t\tif (equalizeId(selectedColumn.id, column.id)) {\n\t\t\tdirection = sortDirection === SortOrder.ASC ? SortOrder.DESC : SortOrder.ASC;\n\t\t}\n\n\t\tonSort({\n\t\t\ttype: 'SORT_CHANGE',\n\t\t\tsortDirection: direction,\n\t\t\tselectedColumn: column,\n\t\t\tclearSelectedOnSort:\n\t\t\t\t(pagination && paginationServer && !persistSelectedOnSort) || sortServer || selectableRowsVisibleOnly,\n\t\t});\n\t};\n\n\tconst handleKeyPress = (event: React.KeyboardEvent<HTMLDivElement>) => {\n\t\tif (event.key === 'Enter') {\n\t\t\thandleSortChange();\n\t\t}\n\t};\n\n\tconst renderNativeSortIcon = (sortActive: boolean) => (\n\t\t<NativeSortIcon sortActive={sortActive} sortDirection={sortDirection} />\n\t);\n\n\tconst renderCustomSortIcon = () => (\n\t\t<span className={[sortDirection, '__rdt_custom_sort_icon__'].join(' ')}>{sortIcon}</span>\n\t);\n\n\tconst sortActive = !!(column.sortable && equalizeId(selectedColumn.id, column.id));\n\tconst disableSort = !column.sortable || disabled;\n\tconst nativeSortIconLeft = column.sortable && !sortIcon && !column.right;\n\tconst nativeSortIconRight = column.sortable && !sortIcon && column.right;\n\tconst customSortIconLeft = column.sortable && sortIcon && !column.right;\n\tconst customSortIconRight = column.sortable && sortIcon && column.right;\n\n\treturn (\n\t\t<ColumnStyled\n\t\t\tdata-column-id={column.id}\n\t\t\tclassName=\"rdt_TableCol\"\n\t\t\t$headCell\n\t\t\tallowOverflow={column.allowOverflow}\n\t\t\tbutton={column.button}\n\t\t\tcompact={column.compact}\n\t\t\tgrow={column.grow}\n\t\t\thide={column.hide}\n\t\t\tmaxWidth={column.maxWidth}\n\t\t\tminWidth={column.minWidth}\n\t\t\tright={column.right}\n\t\t\tcenter={column.center}\n\t\t\twidth={column.width}\n\t\t\tdraggable={column.reorder}\n\t\t\t$isDragging={equalizeId(column.id, draggingColumnId)}\n\t\t\tonDragStart={onDragStart}\n\t\t\tonDragOver={onDragOver}\n\t\t\tonDragEnd={onDragEnd}\n\t\t\tonDragEnter={onDragEnter}\n\t\t\tonDragLeave={onDragLeave}\n\t\t>\n\t\t\t{column.name && (\n\t\t\t\t<ColumnSortable\n\t\t\t\t\tdata-column-id={column.id}\n\t\t\t\t\tdata-sort-id={column.id}\n\t\t\t\t\trole=\"columnheader\"\n\t\t\t\t\ttabIndex={0}\n\t\t\t\t\tclassName=\"rdt_TableCol_Sortable\"\n\t\t\t\t\tonClick={!disableSort ? handleSortChange : undefined}\n\t\t\t\t\tonKeyPress={!disableSort ? handleKeyPress : undefined}\n\t\t\t\t\t$sortActive={!disableSort && sortActive}\n\t\t\t\t\tdisabled={disableSort}\n\t\t\t\t>\n\t\t\t\t\t{!disableSort && customSortIconRight && renderCustomSortIcon()}\n\t\t\t\t\t{!disableSort && nativeSortIconRight && renderNativeSortIcon(sortActive)}\n\n\t\t\t\t\t{typeof column.name === 'string' ? (\n\t\t\t\t\t\t<ColumnText title={showTooltip ? column.name : undefined} ref={columnRef} data-column-id={column.id}>\n\t\t\t\t\t\t\t{column.name}\n\t\t\t\t\t\t</ColumnText>\n\t\t\t\t\t) : (\n\t\t\t\t\t\tcolumn.name\n\t\t\t\t\t)}\n\n\t\t\t\t\t{!disableSort && customSortIconLeft && renderCustomSortIcon()}\n\t\t\t\t\t{!disableSort && nativeSortIconLeft && renderNativeSortIcon(sortActive)}\n\t\t\t\t</ColumnSortable>\n\t\t\t)}\n\t\t</ColumnStyled>\n\t);\n}\n\nexport default React.memo(TableCol) as typeof TableCol;\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport { CellBase } from './Cell';\nimport Checkbox from './Checkbox';\nimport { AllRowsAction, RowState } from './types';\n\nconst ColumnStyle = styled(CellBase)`\n\tflex: 0 0 48px;\n\tjustify-content: center;\n\talign-items: center;\n\tuser-select: none;\n\twhite-space: nowrap;\n\tfont-size: unset;\n`;\n\ninterface ColumnCheckboxProps<T> {\n\theadCell?: boolean;\n\tselectableRowsComponent: 'input' | React.ReactNode;\n\tselectableRowsComponentProps: Record<string, unknown>;\n\tselectableRowDisabled: RowState<T>;\n\tkeyField: string;\n\tmergeSelections: boolean;\n\trowData: T[];\n\tselectedRows: T[];\n\tallSelected: boolean;\n\tonSelectAllRows: (action: AllRowsAction<T>) => void;\n}\n\nfunction ColumnCheckbox<T>({\n\theadCell = true,\n\trowData,\n\tkeyField,\n\tallSelected,\n\tmergeSelections,\n\tselectedRows,\n\tselectableRowsComponent,\n\tselectableRowsComponentProps,\n\tselectableRowDisabled,\n\tonSelectAllRows,\n}: ColumnCheckboxProps<T>): JSX.Element {\n\tconst indeterminate = selectedRows.length > 0 && !allSelected;\n\tconst rows = selectableRowDisabled ? rowData.filter((row: T) => !selectableRowDisabled(row)) : rowData;\n\tconst isDisabled = rows.length === 0;\n\t// The row count should subtract rows that are disabled\n\tconst rowCount = Math.min(rowData.length, rows.length);\n\n\tconst handleSelectAll = () => {\n\t\tonSelectAllRows({\n\t\t\ttype: 'SELECT_ALL_ROWS',\n\t\t\trows,\n\t\t\trowCount,\n\t\t\tmergeSelections,\n\t\t\tkeyField,\n\t\t});\n\t};\n\n\treturn (\n\t\t<ColumnStyle className=\"rdt_TableCol\" $headCell={headCell} $noPadding>\n\t\t\t<Checkbox\n\t\t\t\tname=\"select-all-rows\"\n\t\t\t\tcomponent={selectableRowsComponent}\n\t\t\t\tcomponentOptions={selectableRowsComponentProps}\n\t\t\t\tonClick={handleSelectAll}\n\t\t\t\tchecked={allSelected}\n\t\t\t\tindeterminate={indeterminate}\n\t\t\t\tdisabled={isDisabled}\n\t\t\t/>\n\t\t</ColumnStyle>\n\t);\n}\n\nexport default ColumnCheckbox;\n", "import * as React from 'react';\nimport { Direction } from '../DataTable/constants';\n\nfunction useRTL(direction: Direction = Direction.AUTO): boolean {\n\tconst isClient = typeof window === 'object';\n\n\tconst [isRTL, setIsRTL] = React.useState(false);\n\n\tReact.useEffect(() => {\n\t\tif (!isClient) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (direction === 'auto') {\n\t\t\tconst canUse = !!(window.document && window.document.createElement);\n\t\t\tconst bodyRTL = <HTMLScriptElement>document.getElementsByTagName('BODY')[0];\n\t\t\tconst htmlTRL = <HTMLScriptElement>document.getElementsByTagName('HTML')[0];\n\t\t\tconst hasRTL = bodyRTL.dir === 'rtl' || htmlTRL.dir === 'rtl';\n\n\t\t\tsetIsRTL(canUse && hasRTL);\n\n\t\t\treturn;\n\t\t}\n\n\t\tsetIsRTL(direction === 'rtl');\n\t}, [direction, isClient]);\n\n\treturn isRTL;\n}\n\nexport default useRTL;\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport useRTL from '../hooks/useRTL';\nimport { Direction } from './constants';\nimport { ContextMessage } from './types';\n\nconst Title = styled.div`\n\tdisplay: flex;\n\talign-items: center;\n\tflex: 1 0 auto;\n\theight: 100%;\n\tcolor: ${({ theme }) => theme.contextMenu.fontColor};\n\tfont-size: ${({ theme }) => theme.contextMenu.fontSize};\n\tfont-weight: 400;\n`;\n\nconst ContextActions = styled.div`\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: flex-end;\n\tflex-wrap: wrap;\n`;\n\nconst ContextMenuStyle = styled.div<{\n\t$rtl?: boolean;\n\t$visible: boolean;\n}>`\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbox-sizing: inherit;\n\tz-index: 1;\n\talign-items: center;\n\tjustify-content: space-between;\n\tdisplay: flex;\n\t${({ $rtl }) => $rtl && 'direction: rtl'};\n\t${({ theme }) => theme.contextMenu.style};\n\t${({ theme, $visible }) => $visible && theme.contextMenu.activeStyle};\n`;\n\nconst generateDefaultContextTitle = (contextMessage: ContextMessage, selectedCount: number, rtl: boolean) => {\n\tif (selectedCount === 0) {\n\t\treturn null;\n\t}\n\n\tconst datumName = selectedCount === 1 ? contextMessage.singular : contextMessage.plural;\n\n\t// TODO: add mock document rtl tests\n\tif (rtl) {\n\t\treturn `${selectedCount} ${contextMessage.message || ''} ${datumName}`;\n\t}\n\n\treturn `${selectedCount} ${datumName} ${contextMessage.message || ''}`;\n};\n\ntype ContextMenuProps = {\n\tcontextMessage: ContextMessage;\n\tcontextActions: React.ReactNode | React.ReactNode[];\n\tcontextComponent: React.ReactNode | null;\n\tselectedCount: number;\n\tdirection: Direction;\n};\n\nfunction ContextMenu({\n\tcontextMessage,\n\tcontextActions,\n\tcontextComponent,\n\tselectedCount,\n\tdirection,\n}: ContextMenuProps): JSX.Element {\n\tconst isRTL = useRTL(direction);\n\tconst visible = selectedCount > 0;\n\n\tif (contextComponent) {\n\t\treturn (\n\t\t\t<ContextMenuStyle $visible={visible}>\n\t\t\t\t{React.cloneElement(contextComponent as React.ReactElement, { selectedCount })}\n\t\t\t</ContextMenuStyle>\n\t\t);\n\t}\n\n\treturn (\n\t\t<ContextMenuStyle $visible={visible} $rtl={isRTL}>\n\t\t\t<Title>{generateDefaultContextTitle(contextMessage, selectedCount, isRTL)}</Title>\n\t\t\t<ContextActions>{contextActions}</ContextActions>\n\t\t</ContextMenuStyle>\n\t);\n}\n\nexport default ContextMenu;\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport ContextMenu from './ContextMenu';\nimport { Direction } from './constants';\nimport { ContextMessage } from './types';\n\nconst HeaderStyle = styled.div`\n\tposition: relative;\n\tbox-sizing: border-box;\n\toverflow: hidden;\n\tdisplay: flex;\n\tflex: 1 1 auto;\n\talign-items: center;\n\tjustify-content: space-between;\n\twidth: 100%;\n\tflex-wrap: wrap;\n\t${({ theme }) => theme.header.style}\n`;\n\nconst Title = styled.div`\n\tflex: 1 0 auto;\n\tcolor: ${({ theme }) => theme.header.fontColor};\n\tfont-size: ${({ theme }) => theme.header.fontSize};\n\tfont-weight: 400;\n`;\n\nconst Actions = styled.div`\n\tflex: 1 0 auto;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: flex-end;\n\n\t> * {\n\t\tmargin-left: 5px;\n\t}\n`;\n\ntype HeaderProps = {\n\ttitle?: string | React.ReactNode;\n\tactions?: React.ReactNode | React.ReactNode[];\n\tdirection: Direction;\n\tselectedCount: number;\n\tshowMenu?: boolean;\n\tcontextMessage: ContextMessage;\n\tcontextActions: React.ReactNode | React.ReactNode[];\n\tcontextComponent: React.ReactNode | null;\n};\n\nconst Header = ({\n\ttitle,\n\tactions = null,\n\tcontextMessage,\n\tcontextActions,\n\tcontextComponent,\n\tselectedCount,\n\tdirection,\n\tshowMenu = true,\n}: HeaderProps): JSX.Element => (\n\t<HeaderStyle className=\"rdt_TableHeader\" role=\"heading\" aria-level={1}>\n\t\t<Title>{title}</Title>\n\t\t{actions && <Actions>{actions}</Actions>}\n\n\t\t{showMenu && (\n\t\t\t<ContextMenu\n\t\t\t\tcontextMessage={contextMessage}\n\t\t\t\tcontextActions={contextActions}\n\t\t\t\tcontextComponent={contextComponent}\n\t\t\t\tdirection={direction}\n\t\t\t\tselectedCount={selectedCount}\n\t\t\t/>\n\t\t)}\n\t</HeaderStyle>\n);\n\nexport default Header;\n", "import * as React from 'react';\nimport styled from 'styled-components';\n\nconst alignMap = {\n\tleft: 'flex-start',\n\tright: 'flex-end',\n\tcenter: 'center',\n};\n\ntype AlignItems = 'center' | 'left' | 'right';\n\nconst SubheaderWrapper = styled.header<{\n\talign: AlignItems;\n\t$wrapContent: boolean;\n}>`\n\tposition: relative;\n\tdisplay: flex;\n\tflex: 1 1 auto;\n\tbox-sizing: border-box;\n\talign-items: center;\n\tpadding: 4px 16px 4px 24px;\n\twidth: 100%;\n\tjustify-content: ${({ align }) => alignMap[align]};\n\tflex-wrap: ${({ $wrapContent }) => ($wrapContent ? 'wrap' : 'nowrap')};\n\t${({ theme }) => theme.subHeader.style}\n`;\n\ntype SubheaderProps = {\n\talign?: AlignItems;\n\twrapContent?: boolean;\n\tchildren?: React.ReactNode;\n};\n\nconst Subheader = ({ align = 'right', wrapContent = true, ...rest }: SubheaderProps): JSX.Element => (\n\t<SubheaderWrapper align={align} $wrapContent={wrapContent} {...rest} />\n);\n\nexport default Subheader;\n", "import styled from 'styled-components';\n\nconst Body = styled.div`\n\tdisplay: flex;\n\tflex-direction: column;\n`;\n\nexport default Body;\n", "import styled, { css } from 'styled-components';\n\n/* Hack when using layovers/menus that get clipped by overflow-x\n  when a table is responsive due to overflow-xy scroll spec stupidity.\n  Note: The parent element height must be set to 100%!\n  https://www.brunildo.org/test/Overflowxy2.html\n*/\n\nconst ResponsiveWrapper = styled.div<{\n\t$responsive: boolean;\n\t$fixedHeader?: boolean;\n\t$fixedHeaderScrollHeight?: string;\n}>`\n\tposition: relative;\n\twidth: 100%;\n\tborder-radius: inherit;\n\t${({ $responsive, $fixedHeader }) =>\n\t\t$responsive &&\n\t\tcss`\n\t\t\toverflow-x: auto;\n\n\t\t\t// hidden prevents vertical scrolling in firefox when fixedHeader is disabled\n\t\t\toverflow-y: ${$fixedHeader ? 'auto' : 'hidden'};\n\t\t\tmin-height: 0;\n\t\t`};\n\n\t${({ $fixedHeader = false, $fixedHeaderScrollHeight = '100vh' }) =>\n\t\t$fixedHeader &&\n\t\tcss`\n\t\t\tmax-height: ${$fixedHeaderScrollHeight};\n\t\t\t-webkit-overflow-scrolling: touch;\n\t\t`};\n\n\t${({ theme }) => theme.responsiveWrapper.style};\n`;\n\nexport default ResponsiveWrapper;\n", "import styled from 'styled-components';\n\nconst ProgressWrapper = styled.div`\n\tposition: relative;\n\tbox-sizing: border-box;\n\twidth: 100%;\n\theight: 100%;\n\t${props => props.theme.progress.style};\n`;\n\nexport default ProgressWrapper;\n", "import styled from 'styled-components';\n\nconst Wrapper = styled.div`\n\tposition: relative;\n\twidth: 100%;\n\t${({ theme }) => theme.tableWrapper.style};\n`;\n\nexport default Wrapper;\n", "import styled from 'styled-components';\nimport { CellBase } from './Cell';\n\nconst ColumnExpander = styled(CellBase)`\n\twhite-space: nowrap;\n\t${({ theme }) => theme.expanderCell.style};\n`;\n\nexport default ColumnExpander;\n", "import styled from 'styled-components';\n\nconst NoDataWrapper = styled.div`\n\tbox-sizing: border-box;\n\twidth: 100%;\n\theight: 100%;\n\t${({ theme }) => theme.noData.style};\n`;\n\nexport default NoDataWrapper;\n", "import React from 'react';\n\nconst DropdownIcon: React.FC = () => (\n\t<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\">\n\t\t<path d=\"M7 10l5 5 5-5z\" />\n\t\t<path d=\"M0 0h24v24H0z\" fill=\"none\" />\n\t</svg>\n);\n\nexport default DropdownIcon;\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport DropDownIcon from '../icons/Dropdown';\n\nconst SelectControl = styled.select`\n\tcursor: pointer;\n\theight: 24px;\n\tmax-width: 100%;\n\tuser-select: none;\n\tpadding-left: 8px;\n\tpadding-right: 24px;\n\tbox-sizing: content-box;\n\tfont-size: inherit;\n\tcolor: inherit;\n\tborder: none;\n\tbackground-color: transparent;\n\tappearance: none;\n\tdirection: ltr;\n\tflex-shrink: 0;\n\n\t&::-ms-expand {\n\t\tdisplay: none;\n\t}\n\n\t&:disabled::-ms-expand {\n\t\tbackground: #f60;\n\t}\n\n\toption {\n\t\tcolor: initial;\n\t}\n`;\n\nconst SelectWrapper = styled.div`\n\tposition: relative;\n\tflex-shrink: 0;\n\tfont-size: inherit;\n\tcolor: inherit;\n\tmargin-top: 1px;\n\n\tsvg {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tcolor: inherit;\n\t\tposition: absolute;\n\t\tfill: currentColor;\n\t\twidth: 24px;\n\t\theight: 24px;\n\t\tdisplay: inline-block;\n\t\tuser-select: none;\n\t\tpointer-events: none;\n\t}\n`;\n\ntype SelectProps = {\n\tonChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;\n\tdefaultValue: string | number;\n\tchildren: React.ReactNode;\n};\n\nconst Select = ({ defaultValue, onChange, ...rest }: SelectProps): JSX.Element => (\n\t<SelectWrapper>\n\t\t<SelectControl onChange={onChange} defaultValue={defaultValue} {...rest} />\n\t\t<DropDownIcon />\n\t</SelectWrapper>\n);\n\nexport default Select;\n", "import React from 'react';\nimport FirstPageIcon from '../icons/FirstPage';\nimport LastPageIcon from '../icons/LastPage';\nimport LeftIcon from '../icons/Left';\nimport RightIcon from '../icons/Right';\nimport ExpanderCollapsedIcon from '../icons/ExpanderCollapsedIcon';\nimport ExpanderExpandedIcon from '../icons/ExpanderExpandedIcon';\nimport { noop } from './util';\nimport { Alignment, Direction } from './constants';\n\nexport const defaultProps = {\n\tcolumns: [],\n\tdata: [],\n\ttitle: '',\n\tkeyField: 'id',\n\tselectableRows: false,\n\tselectableRowsHighlight: false,\n\tselectableRowsNoSelectAll: false,\n\tselectableRowSelected: null,\n\tselectableRowDisabled: null,\n\tselectableRowsComponent: 'input' as const,\n\tselectableRowsComponentProps: {},\n\tselectableRowsVisibleOnly: false,\n\tselectableRowsSingle: false,\n\tclearSelectedRows: false,\n\texpandableRows: false,\n\texpandableRowDisabled: null,\n\texpandableRowExpanded: null,\n\texpandOnRowClicked: false,\n\texpandableRowsHideExpander: false,\n\texpandOnRowDoubleClicked: false,\n\texpandableInheritConditionalStyles: false,\n\texpandableRowsComponent: function DefaultExpander(): JSX.Element {\n\t\treturn (\n\t\t\t<div>\n\t\t\t\tTo add an expander pass in a component instance via <strong>expandableRowsComponent</strong>. You can then\n\t\t\t\taccess props.data from this component.\n\t\t\t</div>\n\t\t);\n\t},\n\texpandableIcon: {\n\t\tcollapsed: <ExpanderCollapsedIcon />,\n\t\texpanded: <ExpanderExpandedIcon />,\n\t},\n\texpandableRowsComponentProps: {},\n\tprogressPending: false,\n\tprogressComponent: <div style={{ fontSize: '24px', fontWeight: 700, padding: '24px' }}>Loading...</div>,\n\tpersistTableHead: false,\n\tsortIcon: null,\n\tsortFunction: null,\n\tsortServer: false,\n\tstriped: false,\n\thighlightOnHover: false,\n\tpointerOnHover: false,\n\tnoContextMenu: false,\n\tcontextMessage: { singular: 'item', plural: 'items', message: 'selected' },\n\tactions: null,\n\tcontextActions: null,\n\tcontextComponent: null,\n\tdefaultSortFieldId: null,\n\tdefaultSortAsc: true,\n\tresponsive: true,\n\tnoDataComponent: <div style={{ padding: '24px' }}>There are no records to display</div>,\n\tdisabled: false,\n\tnoTableHead: false,\n\tnoHeader: false,\n\tsubHeader: false,\n\tsubHeaderAlign: Alignment.RIGHT,\n\tsubHeaderWrap: true,\n\tsubHeaderComponent: null,\n\tfixedHeader: false,\n\tfixedHeaderScrollHeight: '100vh',\n\tpagination: false,\n\tpaginationServer: false,\n\tpaginationServerOptions: {\n\t\tpersistSelectedOnSort: false,\n\t\tpersistSelectedOnPageChange: false,\n\t},\n\tpaginationDefaultPage: 1,\n\tpaginationResetDefaultPage: false,\n\tpaginationTotalRows: 0,\n\tpaginationPerPage: 10,\n\tpaginationRowsPerPageOptions: [10, 15, 20, 25, 30],\n\tpaginationComponent: null,\n\tpaginationComponentOptions: {},\n\tpaginationIconFirstPage: <FirstPageIcon />,\n\tpaginationIconLastPage: <LastPageIcon />,\n\tpaginationIconNext: <RightIcon />,\n\tpaginationIconPrevious: <LeftIcon />,\n\tdense: false,\n\tconditionalRowStyles: [],\n\ttheme: 'default' as const,\n\tcustomStyles: {},\n\tdirection: Direction.AUTO,\n\tonChangePage: noop,\n\tonChangeRowsPerPage: noop,\n\tonRowClicked: noop,\n\tonRowDoubleClicked: noop,\n\tonRowMouseEnter: noop,\n\tonRowMouseLeave: noop,\n\tonRowExpandToggled: noop,\n\tonSelectedRowsChange: noop,\n\tonSort: noop,\n\tonColumnOrderChange: noop,\n};\n", "import React from 'react';\n\nconst ExpanderCollapsedIcon: React.FC = () => (\n\t<svg fill=\"currentColor\" height=\"24\" viewBox=\"0 0 24 24\" width=\"24\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t<path d=\"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z\" />\n\t\t<path d=\"M0-.25h24v24H0z\" fill=\"none\" />\n\t</svg>\n);\n\nexport default ExpanderCollapsedIcon;\n", "import React from 'react';\n\nconst ExpanderExpandedIcon: React.FC = () => (\n\t<svg fill=\"currentColor\" height=\"24\" viewBox=\"0 0 24 24\" width=\"24\" xmlns=\"http://www.w3.org/2000/svg\">\n\t\t<path d=\"M7.41 7.84L12 12.42l4.59-4.58L18 9.25l-6 6-6-6z\" />\n\t\t<path d=\"M0-.75h24v24H0z\" fill=\"none\" />\n\t</svg>\n);\n\nexport default ExpanderExpandedIcon;\n", "import React from 'react';\n\nconst FirstPage: React.FC = () => (\n\t<svg\n\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\twidth=\"24\"\n\t\theight=\"24\"\n\t\tviewBox=\"0 0 24 24\"\n\t\taria-hidden=\"true\"\n\t\trole=\"presentation\"\n\t>\n\t\t<path d=\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\" />\n\t\t<path fill=\"none\" d=\"M24 24H0V0h24v24z\" />\n\t</svg>\n);\n\nexport default FirstPage;\n", "import React from 'react';\n\nconst LastPage: React.FC = () => (\n\t<svg\n\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\twidth=\"24\"\n\t\theight=\"24\"\n\t\tviewBox=\"0 0 24 24\"\n\t\taria-hidden=\"true\"\n\t\trole=\"presentation\"\n\t>\n\t\t<path d=\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\" />\n\t\t<path fill=\"none\" d=\"M0 0h24v24H0V0z\" />\n\t</svg>\n);\n\nexport default LastPage;\n", "import React from 'react';\n\nconst Right: React.FC = () => (\n\t<svg\n\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\twidth=\"24\"\n\t\theight=\"24\"\n\t\tviewBox=\"0 0 24 24\"\n\t\taria-hidden=\"true\"\n\t\trole=\"presentation\"\n\t>\n\t\t<path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\" />\n\t\t<path d=\"M0 0h24v24H0z\" fill=\"none\" />\n\t</svg>\n);\n\nexport default Right;\n", "import React from 'react';\n\nconst Left: React.FC = () => (\n\t<svg\n\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\twidth=\"24\"\n\t\theight=\"24\"\n\t\tviewBox=\"0 0 24 24\"\n\t\taria-hidden=\"true\"\n\t\trole=\"presentation\"\n\t>\n\t\t<path d=\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\" />\n\t\t<path d=\"M0 0h24v24H0z\" fill=\"none\" />\n\t</svg>\n);\n\nexport default Left;\n", "import * as React from 'react';\nimport styled from 'styled-components';\nimport Select from './Select';\nimport { getNumberOfPages } from './util';\nimport useWindowSize from '../hooks/useWindowSize';\nimport useRTL from '../hooks/useRTL';\nimport { media, SMALL } from './media';\nimport { Direction } from './constants';\nimport { PaginationOptions } from './types';\nimport { defaultProps } from './defaultProps';\n\nconst defaultComponentOptions = {\n\trowsPerPageText: 'Rows per page:',\n\trangeSeparatorText: 'of',\n\tnoRowsPerPage: false,\n\tselectAllRowsItem: false,\n\tselectAllRowsItemText: 'All',\n};\n\nconst PaginationWrapper = styled.nav`\n\tdisplay: flex;\n\tflex: 1 1 auto;\n\tjustify-content: flex-end;\n\talign-items: center;\n\tbox-sizing: border-box;\n\tpadding-right: 8px;\n\tpadding-left: 8px;\n\twidth: 100%;\n\t${({ theme }) => theme.pagination.style};\n`;\n\nconst Button = styled.button<{\n\t$isRTL: boolean;\n}>`\n\tposition: relative;\n\tdisplay: block;\n\tuser-select: none;\n\tborder: none;\n\t${({ theme }) => theme.pagination.pageButtonsStyle};\n\t${({ $isRTL }) => $isRTL && 'transform: scale(-1, -1)'};\n`;\n\nconst PageList = styled.div`\n\tdisplay: flex;\n\talign-items: center;\n\tborder-radius: 4px;\n\twhite-space: nowrap;\n\t${media.sm`\n    width: 100%;\n    justify-content: space-around;\n  `};\n`;\n\nconst Span = styled.span`\n\tflex-shrink: 1;\n\tuser-select: none;\n`;\n\nconst Range = styled(Span)`\n\tmargin: 0 24px;\n`;\n\nconst RowLabel = styled(Span)`\n\tmargin: 0 4px;\n`;\n\ninterface PaginationProps {\n\trowsPerPage: number;\n\trowCount: number;\n\tcurrentPage: number;\n\tdirection?: Direction;\n\tpaginationRowsPerPageOptions?: number[];\n\tpaginationIconLastPage?: React.ReactNode;\n\tpaginationIconFirstPage?: React.ReactNode;\n\tpaginationIconNext?: React.ReactNode;\n\tpaginationIconPrevious?: React.ReactNode;\n\tpaginationComponentOptions?: PaginationOptions;\n\tonChangePage: (page: number) => void;\n\tonChangeRowsPerPage: (numRows: number, currentPage: number) => void;\n}\n\nfunction Pagination({\n\trowsPerPage,\n\trowCount,\n\tcurrentPage,\n\tdirection = defaultProps.direction,\n\tpaginationRowsPerPageOptions = defaultProps.paginationRowsPerPageOptions,\n\tpaginationIconLastPage = defaultProps.paginationIconLastPage,\n\tpaginationIconFirstPage = defaultProps.paginationIconFirstPage,\n\tpaginationIconNext = defaultProps.paginationIconNext,\n\tpaginationIconPrevious = defaultProps.paginationIconPrevious,\n\tpaginationComponentOptions = defaultProps.paginationComponentOptions,\n\tonChangeRowsPerPage = defaultProps.onChangeRowsPerPage,\n\tonChangePage = defaultProps.onChangePage,\n}: PaginationProps): JSX.Element {\n\tconst windowSize = useWindowSize();\n\tconst isRTL = useRTL(direction);\n\tconst shouldShow = windowSize.width && windowSize.width > SMALL;\n\t// const isRTL = detectRTL(direction);\n\tconst numPages = getNumberOfPages(rowCount, rowsPerPage);\n\tconst lastIndex = currentPage * rowsPerPage;\n\tconst firstIndex = lastIndex - rowsPerPage + 1;\n\tconst disabledLesser = currentPage === 1;\n\tconst disabledGreater = currentPage === numPages;\n\tconst options = { ...defaultComponentOptions, ...paginationComponentOptions };\n\tconst range =\n\t\tcurrentPage === numPages\n\t\t\t? `${firstIndex}-${rowCount} ${options.rangeSeparatorText} ${rowCount}`\n\t\t\t: `${firstIndex}-${lastIndex} ${options.rangeSeparatorText} ${rowCount}`;\n\n\tconst handlePrevious = React.useCallback(() => onChangePage(currentPage - 1), [currentPage, onChangePage]);\n\tconst handleNext = React.useCallback(() => onChangePage(currentPage + 1), [currentPage, onChangePage]);\n\tconst handleFirst = React.useCallback(() => onChangePage(1), [onChangePage]);\n\tconst handleLast = React.useCallback(\n\t\t() => onChangePage(getNumberOfPages(rowCount, rowsPerPage)),\n\t\t[onChangePage, rowCount, rowsPerPage],\n\t);\n\tconst handleRowsPerPage = React.useCallback(\n\t\t(e: React.ChangeEvent<HTMLSelectElement>) => onChangeRowsPerPage(Number(e.target.value), currentPage),\n\t\t[currentPage, onChangeRowsPerPage],\n\t);\n\n\tconst selectOptions = paginationRowsPerPageOptions.map((num: number) => (\n\t\t<option key={num} value={num}>\n\t\t\t{num}\n\t\t</option>\n\t));\n\n\tif (options.selectAllRowsItem) {\n\t\tselectOptions.push(\n\t\t\t<option key={-1} value={rowCount}>\n\t\t\t\t{options.selectAllRowsItemText}\n\t\t\t</option>,\n\t\t);\n\t}\n\n\tconst select = (\n\t\t<Select onChange={handleRowsPerPage} defaultValue={rowsPerPage} aria-label={options.rowsPerPageText}>\n\t\t\t{selectOptions}\n\t\t</Select>\n\t);\n\n\treturn (\n\t\t<PaginationWrapper className=\"rdt_Pagination\">\n\t\t\t{!options.noRowsPerPage && shouldShow && (\n\t\t\t\t<>\n\t\t\t\t\t<RowLabel>{options.rowsPerPageText}</RowLabel>\n\t\t\t\t\t{select}\n\t\t\t\t</>\n\t\t\t)}\n\t\t\t{shouldShow && <Range>{range}</Range>}\n\t\t\t<PageList>\n\t\t\t\t<Button\n\t\t\t\t\tid=\"pagination-first-page\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\taria-label=\"First Page\"\n\t\t\t\t\taria-disabled={disabledLesser}\n\t\t\t\t\tonClick={handleFirst}\n\t\t\t\t\tdisabled={disabledLesser}\n\t\t\t\t\t$isRTL={isRTL}\n\t\t\t\t>\n\t\t\t\t\t{paginationIconFirstPage}\n\t\t\t\t</Button>\n\n\t\t\t\t<Button\n\t\t\t\t\tid=\"pagination-previous-page\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\taria-label=\"Previous Page\"\n\t\t\t\t\taria-disabled={disabledLesser}\n\t\t\t\t\tonClick={handlePrevious}\n\t\t\t\t\tdisabled={disabledLesser}\n\t\t\t\t\t$isRTL={isRTL}\n\t\t\t\t>\n\t\t\t\t\t{paginationIconPrevious}\n\t\t\t\t</Button>\n\n\t\t\t\t{!options.noRowsPerPage && !shouldShow && select}\n\n\t\t\t\t<Button\n\t\t\t\t\tid=\"pagination-next-page\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\taria-label=\"Next Page\"\n\t\t\t\t\taria-disabled={disabledGreater}\n\t\t\t\t\tonClick={handleNext}\n\t\t\t\t\tdisabled={disabledGreater}\n\t\t\t\t\t$isRTL={isRTL}\n\t\t\t\t>\n\t\t\t\t\t{paginationIconNext}\n\t\t\t\t</Button>\n\n\t\t\t\t<Button\n\t\t\t\t\tid=\"pagination-last-page\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\taria-label=\"Last Page\"\n\t\t\t\t\taria-disabled={disabledGreater}\n\t\t\t\t\tonClick={handleLast}\n\t\t\t\t\tdisabled={disabledGreater}\n\t\t\t\t\t$isRTL={isRTL}\n\t\t\t\t>\n\t\t\t\t\t{paginationIconLastPage}\n\t\t\t\t</Button>\n\t\t\t</PageList>\n\t\t</PaginationWrapper>\n\t);\n}\n\nexport default React.memo(Pagination);\n", "// Credit: https://usehooks.com/useWindowSize/\nimport * as React from 'react';\n\ntype Hook = () => {\n\twidth: number | undefined;\n\theight: number | undefined;\n};\n\nconst useWindowSize: Hook = () => {\n\tconst isClient = typeof window === 'object';\n\n\tfunction getSize() {\n\t\treturn {\n\t\t\twidth: isClient ? window.innerWidth : undefined,\n\t\t\theight: isClient ? window.innerHeight : undefined,\n\t\t};\n\t}\n\n\tconst [windowSize, setWindowSize] = React.useState(getSize);\n\n\tReact.useEffect(() => {\n\t\tif (!isClient) {\n\t\t\treturn () => null;\n\t\t}\n\n\t\tfunction handleResize() {\n\t\t\tsetWindowSize(getSize());\n\t\t}\n\n\t\twindow.addEventListener('resize', handleResize);\n\t\treturn () => window.removeEventListener('resize', handleResize);\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, []);\n\n\treturn windowSize;\n};\n\nexport default useWindowSize;\n", "import * as React from 'react';\n\ntype Hook = (fn: () => void, inputs: unknown[]) => void;\n\nconst useFirstUpdate: Hook = (fn, inputs) => {\n\tconst firstUpdate = React.useRef(true);\n\n\tReact.useEffect(() => {\n\t\tif (firstUpdate.current) {\n\t\t\tfirstUpdate.current = false;\n\t\t\treturn;\n\t\t}\n\n\t\tfn();\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, inputs);\n};\n\nexport default useFirstUpdate;\n", "'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction getMergeFunction(key, options) {\n\tif (!options.customMerge) {\n\t\treturn deepmerge\n\t}\n\tvar customMerge = options.customMerge(key);\n\treturn typeof customMerge === 'function' ? customMerge : deepmerge\n}\n\nfunction getEnumerableOwnPropertySymbols(target) {\n\treturn Object.getOwnPropertySymbols\n\t\t? Object.getOwnPropertySymbols(target).filter(function(symbol) {\n\t\t\treturn Object.propertyIsEnumerable.call(target, symbol)\n\t\t})\n\t\t: []\n}\n\nfunction getKeys(target) {\n\treturn Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))\n}\n\nfunction propertyIsOnObject(object, property) {\n\ttry {\n\t\treturn property in object\n\t} catch(_) {\n\t\treturn false\n\t}\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n\treturn propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n\t\t&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n\t\t\t&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tgetKeys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tgetKeys(source).forEach(function(key) {\n\t\tif (propertyIsUnsafe(target, key)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n\t\t\tdestination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\t// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n\t// implementations can use it. The caller may not replace it.\n\toptions.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n", "import merge from 'deepmerge';\nimport { Theme, Themes } from './types';\n\ntype ThemeMapping = {\n\t[propertyName: string]: Theme;\n};\n\nconst defaultTheme = {\n\ttext: {\n\t\tprimary: 'rgba(0, 0, 0, 0.87)',\n\t\tsecondary: 'rgba(0, 0, 0, 0.54)',\n\t\tdisabled: 'rgba(0, 0, 0, 0.38)',\n\t},\n\tbackground: {\n\t\tdefault: '#FFFFFF',\n\t},\n\tcontext: {\n\t\tbackground: '#e3f2fd',\n\t\ttext: 'rgba(0, 0, 0, 0.87)',\n\t},\n\tdivider: {\n\t\tdefault: 'rgba(0,0,0,.12)',\n\t},\n\tbutton: {\n\t\tdefault: 'rgba(0,0,0,.54)',\n\t\tfocus: 'rgba(0,0,0,.12)',\n\t\thover: 'rgba(0,0,0,.12)',\n\t\tdisabled: 'rgba(0, 0, 0, .18)',\n\t},\n\tselected: {\n\t\tdefault: '#e3f2fd',\n\t\ttext: 'rgba(0, 0, 0, 0.87)',\n\t},\n\thighlightOnHover: {\n\t\tdefault: '#EEEEEE',\n\t\ttext: 'rgba(0, 0, 0, 0.87)',\n\t},\n\tstriped: {\n\t\tdefault: '#FAFAFA',\n\t\ttext: 'rgba(0, 0, 0, 0.87)',\n\t},\n};\n\nexport const defaultThemes: ThemeMapping = {\n\tdefault: defaultTheme,\n\tlight: defaultTheme,\n\tdark: {\n\t\ttext: {\n\t\t\tprimary: '#FFFFFF',\n\t\t\tsecondary: 'rgba(255, 255, 255, 0.7)',\n\t\t\tdisabled: 'rgba(0,0,0,.12)',\n\t\t},\n\t\tbackground: {\n\t\t\tdefault: '#424242',\n\t\t},\n\t\tcontext: {\n\t\t\tbackground: '#E91E63',\n\t\t\ttext: '#FFFFFF',\n\t\t},\n\t\tdivider: {\n\t\t\tdefault: 'rgba(81, 81, 81, 1)',\n\t\t},\n\t\tbutton: {\n\t\t\tdefault: '#FFFFFF',\n\t\t\tfocus: 'rgba(255, 255, 255, .54)',\n\t\t\thover: 'rgba(255, 255, 255, .12)',\n\t\t\tdisabled: 'rgba(255, 255, 255, .18)',\n\t\t},\n\t\tselected: {\n\t\t\tdefault: 'rgba(0, 0, 0, .7)',\n\t\t\ttext: '#FFFFFF',\n\t\t},\n\t\thighlightOnHover: {\n\t\t\tdefault: 'rgba(0, 0, 0, .7)',\n\t\t\ttext: '#FFFFFF',\n\t\t},\n\t\tstriped: {\n\t\t\tdefault: 'rgba(0, 0, 0, .87)',\n\t\t\ttext: '#FFFFFF',\n\t\t},\n\t},\n};\n\nexport function createTheme<T>(name = 'default', customTheme?: T, inherit: Themes = 'default'): Theme {\n\tif (!defaultThemes[name]) {\n\t\tdefaultThemes[name] = merge(defaultThemes[inherit], customTheme || {});\n\t}\n\n\t// allow tweaking default or light themes if the theme passed in matches\n\tdefaultThemes[name] = merge(defaultThemes[name], customTheme || {});\n\n\treturn defaultThemes[name];\n}\n", "import * as React from 'react';\nimport { decorateColumns, findColumnIndexById, getSortDirection } from '../DataTable/util';\nimport useDidUpdateEffect from '../hooks/useDidUpdateEffect';\nimport { SortOrder, TableColumn } from '../DataTable/types';\n\ntype ColumnsHook<T> = {\n\ttableColumns: TableColumn<T>[];\n\tdraggingColumnId: string;\n\thandleDragStart: (e: React.DragEvent<HTMLDivElement>) => void;\n\thandleDragEnter: (e: React.DragEvent<HTMLDivElement>) => void;\n\thandleDragOver: (e: React.DragEvent<HTMLDivElement>) => void;\n\thandleDragLeave: (e: React.DragEvent<HTMLDivElement>) => void;\n\thandleDragEnd: (e: React.DragEvent<HTMLDivElement>) => void;\n\tdefaultSortDirection: SortOrder;\n\tdefaultSortColumn: TableColumn<T>;\n};\n\nfunction useColumns<T>(\n\tcolumns: TableColumn<T>[],\n\tonColumnOrderChange: (nextOrder: TableColumn<T>[]) => void,\n\tdefaultSortFieldId: string | number | null | undefined,\n\tdefaultSortAsc: boolean,\n): ColumnsHook<T> {\n\tconst [tableColumns, setTableColumns] = React.useState<TableColumn<T>[]>(() => decorateColumns(columns));\n\tconst [draggingColumnId, setDraggingColumn] = React.useState('');\n\tconst sourceColumnId = React.useRef('');\n\n\tuseDidUpdateEffect(() => {\n\t\tsetTableColumns(decorateColumns(columns));\n\t}, [columns]);\n\n\tconst handleDragStart = React.useCallback(\n\t\t(e: React.DragEvent<HTMLDivElement>) => {\n\t\t\tconst { attributes } = e.target as HTMLDivElement;\n\t\t\tconst id = attributes.getNamedItem('data-column-id')?.value;\n\n\t\t\tif (id) {\n\t\t\t\tsourceColumnId.current = tableColumns[findColumnIndexById(tableColumns, id)]?.id?.toString() || '';\n\n\t\t\t\tsetDraggingColumn(sourceColumnId.current);\n\t\t\t}\n\t\t},\n\t\t[tableColumns],\n\t);\n\n\tconst handleDragEnter = React.useCallback(\n\t\t(e: React.DragEvent<HTMLDivElement>) => {\n\t\t\tconst { attributes } = e.target as HTMLDivElement;\n\t\t\tconst id = attributes.getNamedItem('data-column-id')?.value;\n\n\t\t\tif (id && sourceColumnId.current && id !== sourceColumnId.current) {\n\t\t\t\tconst selectedColIndex = findColumnIndexById(tableColumns, sourceColumnId.current);\n\t\t\t\tconst targetColIndex = findColumnIndexById(tableColumns, id);\n\t\t\t\tconst reorderedCols = [...tableColumns];\n\n\t\t\t\treorderedCols[selectedColIndex] = tableColumns[targetColIndex];\n\t\t\t\treorderedCols[targetColIndex] = tableColumns[selectedColIndex];\n\n\t\t\t\tsetTableColumns(reorderedCols);\n\n\t\t\t\tonColumnOrderChange(reorderedCols);\n\t\t\t}\n\t\t},\n\t\t[onColumnOrderChange, tableColumns],\n\t);\n\n\tconst handleDragOver = React.useCallback((e: React.DragEvent<HTMLDivElement>) => {\n\t\te.preventDefault();\n\t}, []);\n\n\tconst handleDragLeave = React.useCallback((e: React.DragEvent<HTMLDivElement>) => {\n\t\te.preventDefault();\n\t}, []);\n\n\tconst handleDragEnd = React.useCallback((e: React.DragEvent<HTMLDivElement>) => {\n\t\te.preventDefault();\n\n\t\tsourceColumnId.current = '';\n\n\t\tsetDraggingColumn('');\n\t}, []);\n\n\tconst defaultSortDirection = getSortDirection(defaultSortAsc);\n\tconst defaultSortColumn = React.useMemo(\n\t\t() => tableColumns[findColumnIndexById(tableColumns, defaultSortFieldId?.toString())] || {},\n\t\t[defaultSortFieldId, tableColumns],\n\t);\n\n\treturn {\n\t\ttableColumns,\n\t\tdraggingColumnId,\n\t\thandleDragStart,\n\t\thandleDragEnter,\n\t\thandleDragOver,\n\t\thandleDragLeave,\n\t\thandleDragEnd,\n\t\tdefaultSortDirection,\n\t\tdefaultSortColumn,\n\t};\n}\n\nexport default useColumns;\n", "import * as React from 'react';\nimport { ThemeProvider } from 'styled-components';\nimport { tableReducer } from './tableReducer';\nimport Table from './Table';\nimport Head from './TableHead';\nimport HeadRow from './TableHeadRow';\nimport Row from './TableRow';\nimport Column from './TableCol';\nimport ColumnCheckbox from './TableColCheckbox';\nimport Header from './TableHeader';\nimport Subheader from './TableSubheader';\nimport Body from './TableBody';\nimport ResponsiveWrapper from './ResponsiveWrapper';\nimport ProgressWrapper from './ProgressWrapper';\nimport Wrapper from './TableWrapper';\nimport ColumnExpander from './TableColExpander';\nimport { CellBase } from './Cell';\nimport NoData from './NoDataWrapper';\nimport NativePagination from './Pagination';\nimport useDidUpdateEffect from '../hooks/useDidUpdateEffect';\nimport { prop, getNumberOfPages, sort, isEmpty, isRowSelected, recalculatePage } from './util';\nimport { defaultProps } from './defaultProps';\nimport { createStyles } from './styles';\nimport {\n\tAction,\n\tAllRowsAction,\n\tSingleRowAction,\n\tTableRow,\n\tSortAction,\n\tTableProps,\n\tTableState,\n\tSortOrder,\n} from './types';\nimport useColumns from '../hooks/useColumns';\n\nfunction DataTable<T>(props: TableProps<T>): JSX.Element {\n\tconst {\n\t\tdata = defaultProps.data,\n\t\tcolumns = defaultProps.columns,\n\t\ttitle = defaultProps.title,\n\t\tactions = defaultProps.actions,\n\t\tkeyField = defaultProps.keyField,\n\t\tstriped = defaultProps.striped,\n\t\thighlightOnHover = defaultProps.highlightOnHover,\n\t\tpointerOnHover = defaultProps.pointerOnHover,\n\t\tdense = defaultProps.dense,\n\t\tselectableRows = defaultProps.selectableRows,\n\t\tselectableRowsSingle = defaultProps.selectableRowsSingle,\n\t\tselectableRowsHighlight = defaultProps.selectableRowsHighlight,\n\t\tselectableRowsNoSelectAll = defaultProps.selectableRowsNoSelectAll,\n\t\tselectableRowsVisibleOnly = defaultProps.selectableRowsVisibleOnly,\n\t\tselectableRowSelected = defaultProps.selectableRowSelected,\n\t\tselectableRowDisabled = defaultProps.selectableRowDisabled,\n\t\tselectableRowsComponent = defaultProps.selectableRowsComponent,\n\t\tselectableRowsComponentProps = defaultProps.selectableRowsComponentProps,\n\t\tonRowExpandToggled = defaultProps.onRowExpandToggled,\n\t\tonSelectedRowsChange = defaultProps.onSelectedRowsChange,\n\t\texpandableIcon = defaultProps.expandableIcon,\n\t\tonChangeRowsPerPage = defaultProps.onChangeRowsPerPage,\n\t\tonChangePage = defaultProps.onChangePage,\n\t\tpaginationServer = defaultProps.paginationServer,\n\t\tpaginationServerOptions = defaultProps.paginationServerOptions,\n\t\tpaginationTotalRows = defaultProps.paginationTotalRows,\n\t\tpaginationDefaultPage = defaultProps.paginationDefaultPage,\n\t\tpaginationResetDefaultPage = defaultProps.paginationResetDefaultPage,\n\t\tpaginationPerPage = defaultProps.paginationPerPage,\n\t\tpaginationRowsPerPageOptions = defaultProps.paginationRowsPerPageOptions,\n\t\tpaginationIconLastPage = defaultProps.paginationIconLastPage,\n\t\tpaginationIconFirstPage = defaultProps.paginationIconFirstPage,\n\t\tpaginationIconNext = defaultProps.paginationIconNext,\n\t\tpaginationIconPrevious = defaultProps.paginationIconPrevious,\n\t\tpaginationComponent = defaultProps.paginationComponent,\n\t\tpaginationComponentOptions = defaultProps.paginationComponentOptions,\n\t\tresponsive = defaultProps.responsive,\n\t\tprogressPending = defaultProps.progressPending,\n\t\tprogressComponent = defaultProps.progressComponent,\n\t\tpersistTableHead = defaultProps.persistTableHead,\n\t\tnoDataComponent = defaultProps.noDataComponent,\n\t\tdisabled = defaultProps.disabled,\n\t\tnoTableHead = defaultProps.noTableHead,\n\t\tnoHeader = defaultProps.noHeader,\n\t\tfixedHeader = defaultProps.fixedHeader,\n\t\tfixedHeaderScrollHeight = defaultProps.fixedHeaderScrollHeight,\n\t\tpagination = defaultProps.pagination,\n\t\tsubHeader = defaultProps.subHeader,\n\t\tsubHeaderAlign = defaultProps.subHeaderAlign,\n\t\tsubHeaderWrap = defaultProps.subHeaderWrap,\n\t\tsubHeaderComponent = defaultProps.subHeaderComponent,\n\t\tnoContextMenu = defaultProps.noContextMenu,\n\t\tcontextMessage = defaultProps.contextMessage,\n\t\tcontextActions = defaultProps.contextActions,\n\t\tcontextComponent = defaultProps.contextComponent,\n\t\texpandableRows = defaultProps.expandableRows,\n\t\tonRowClicked = defaultProps.onRowClicked,\n\t\tonRowDoubleClicked = defaultProps.onRowDoubleClicked,\n\t\tonRowMouseEnter = defaultProps.onRowMouseEnter,\n\t\tonRowMouseLeave = defaultProps.onRowMouseLeave,\n\t\tsortIcon = defaultProps.sortIcon,\n\t\tonSort = defaultProps.onSort,\n\t\tsortFunction = defaultProps.sortFunction,\n\t\tsortServer = defaultProps.sortServer,\n\t\texpandableRowsComponent = defaultProps.expandableRowsComponent,\n\t\texpandableRowsComponentProps = defaultProps.expandableRowsComponentProps,\n\t\texpandableRowDisabled = defaultProps.expandableRowDisabled,\n\t\texpandableRowsHideExpander = defaultProps.expandableRowsHideExpander,\n\t\texpandOnRowClicked = defaultProps.expandOnRowClicked,\n\t\texpandOnRowDoubleClicked = defaultProps.expandOnRowDoubleClicked,\n\t\texpandableRowExpanded = defaultProps.expandableRowExpanded,\n\t\texpandableInheritConditionalStyles = defaultProps.expandableInheritConditionalStyles,\n\t\tdefaultSortFieldId = defaultProps.defaultSortFieldId,\n\t\tdefaultSortAsc = defaultProps.defaultSortAsc,\n\t\tclearSelectedRows = defaultProps.clearSelectedRows,\n\t\tconditionalRowStyles = defaultProps.conditionalRowStyles,\n\t\ttheme = defaultProps.theme,\n\t\tcustomStyles = defaultProps.customStyles,\n\t\tdirection = defaultProps.direction,\n\t\tonColumnOrderChange = defaultProps.onColumnOrderChange,\n\t\tclassName,\n\t\tariaLabel,\n\t} = props;\n\n\tconst {\n\t\ttableColumns,\n\t\tdraggingColumnId,\n\t\thandleDragStart,\n\t\thandleDragEnter,\n\t\thandleDragOver,\n\t\thandleDragLeave,\n\t\thandleDragEnd,\n\t\tdefaultSortDirection,\n\t\tdefaultSortColumn,\n\t} = useColumns(columns, onColumnOrderChange, defaultSortFieldId, defaultSortAsc);\n\n\tconst [\n\t\t{\n\t\t\trowsPerPage,\n\t\t\tcurrentPage,\n\t\t\tselectedRows,\n\t\t\tallSelected,\n\t\t\tselectedCount,\n\t\t\tselectedColumn,\n\t\t\tsortDirection,\n\t\t\ttoggleOnSelectedRowsChange,\n\t\t},\n\t\tdispatch,\n\t] = React.useReducer<React.Reducer<TableState<T>, Action<T>>>(tableReducer, {\n\t\tallSelected: false,\n\t\tselectedCount: 0,\n\t\tselectedRows: [],\n\t\tselectedColumn: defaultSortColumn,\n\t\ttoggleOnSelectedRowsChange: false,\n\t\tsortDirection: defaultSortDirection,\n\t\tcurrentPage: paginationDefaultPage,\n\t\trowsPerPage: paginationPerPage,\n\t\tselectedRowsFlag: false,\n\t\tcontextMessage: defaultProps.contextMessage,\n\t});\n\n\tconst { persistSelectedOnSort = false, persistSelectedOnPageChange = false } = paginationServerOptions;\n\tconst mergeSelections = !!(paginationServer && (persistSelectedOnPageChange || persistSelectedOnSort));\n\tconst enabledPagination = pagination && !progressPending && data.length > 0;\n\tconst Pagination = paginationComponent || NativePagination;\n\n\tconst currentTheme = React.useMemo(() => createStyles(customStyles, theme), [customStyles, theme]);\n\tconst wrapperProps = React.useMemo(() => ({ ...(direction !== 'auto' && { dir: direction }) }), [direction]);\n\n\tconst sortedData = React.useMemo(() => {\n\t\t// server-side sorting bypasses internal sorting\n\t\tif (sortServer) {\n\t\t\treturn data;\n\t\t}\n\n\t\tif (selectedColumn?.sortFunction && typeof selectedColumn.sortFunction === 'function') {\n\t\t\tconst sortFn = selectedColumn.sortFunction;\n\t\t\tconst customSortFunction = sortDirection === SortOrder.ASC ? sortFn : (a: T, b: T) => sortFn(a, b) * -1;\n\n\t\t\treturn [...data].sort(customSortFunction);\n\t\t}\n\n\t\treturn sort(data, selectedColumn?.selector, sortDirection, sortFunction);\n\t}, [sortServer, selectedColumn, sortDirection, data, sortFunction]);\n\n\tconst tableRows = React.useMemo(() => {\n\t\tif (pagination && !paginationServer) {\n\t\t\t// when using client-side pagination we can just slice the rows set\n\t\t\tconst lastIndex = currentPage * rowsPerPage;\n\t\t\tconst firstIndex = lastIndex - rowsPerPage;\n\n\t\t\treturn sortedData.slice(firstIndex, lastIndex);\n\t\t}\n\n\t\treturn sortedData;\n\t}, [currentPage, pagination, paginationServer, rowsPerPage, sortedData]);\n\n\tconst handleSort = React.useCallback((action: SortAction<T>) => {\n\t\tdispatch(action);\n\t}, []);\n\n\tconst handleSelectAllRows = React.useCallback((action: AllRowsAction<T>) => {\n\t\tdispatch(action);\n\t}, []);\n\n\tconst handleSelectedRow = React.useCallback((action: SingleRowAction<T>) => {\n\t\tdispatch(action);\n\t}, []);\n\n\tconst handleRowClicked = React.useCallback(\n\t\t(row: T, e: React.MouseEvent<Element, MouseEvent>) => onRowClicked(row, e),\n\t\t[onRowClicked],\n\t);\n\n\tconst handleRowDoubleClicked = React.useCallback(\n\t\t(row: T, e: React.MouseEvent<Element, MouseEvent>) => onRowDoubleClicked(row, e),\n\t\t[onRowDoubleClicked],\n\t);\n\n\tconst handleRowMouseEnter = React.useCallback(\n\t\t(row: T, e: React.MouseEvent<Element, MouseEvent>) => onRowMouseEnter(row, e),\n\t\t[onRowMouseEnter],\n\t);\n\n\tconst handleRowMouseLeave = React.useCallback(\n\t\t(row: T, e: React.MouseEvent<Element, MouseEvent>) => onRowMouseLeave(row, e),\n\t\t[onRowMouseLeave],\n\t);\n\n\tconst handleChangePage = React.useCallback(\n\t\t(page: number) =>\n\t\t\tdispatch({\n\t\t\t\ttype: 'CHANGE_PAGE',\n\t\t\t\tpage,\n\t\t\t\tpaginationServer,\n\t\t\t\tvisibleOnly: selectableRowsVisibleOnly,\n\t\t\t\tpersistSelectedOnPageChange,\n\t\t\t}),\n\t\t[paginationServer, persistSelectedOnPageChange, selectableRowsVisibleOnly],\n\t);\n\n\tconst handleChangeRowsPerPage = React.useCallback(\n\t\t(newRowsPerPage: number) => {\n\t\t\tconst rowCount = paginationTotalRows || tableRows.length;\n\t\t\tconst updatedPage = getNumberOfPages(rowCount, newRowsPerPage);\n\t\t\tconst recalculatedPage = recalculatePage(currentPage, updatedPage);\n\n\t\t\t// update the currentPage for client-side pagination\n\t\t\t// server - side should be handled by onChangeRowsPerPage\n\t\t\tif (!paginationServer) {\n\t\t\t\thandleChangePage(recalculatedPage);\n\t\t\t}\n\n\t\t\tdispatch({ type: 'CHANGE_ROWS_PER_PAGE', page: recalculatedPage, rowsPerPage: newRowsPerPage });\n\t\t},\n\t\t[currentPage, handleChangePage, paginationServer, paginationTotalRows, tableRows.length],\n\t);\n\n\tconst showTableHead = () => {\n\t\tif (noTableHead) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (persistTableHead) {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn sortedData.length > 0 && !progressPending;\n\t};\n\n\tconst showHeader = () => {\n\t\tif (noHeader) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (title) {\n\t\t\treturn true;\n\t\t}\n\n\t\tif (actions) {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t};\n\n\t// recalculate the pagination and currentPage if the rows length changes\n\tif (pagination && !paginationServer && sortedData.length > 0 && tableRows.length === 0) {\n\t\tconst updatedPage = getNumberOfPages(sortedData.length, rowsPerPage);\n\t\tconst recalculatedPage = recalculatePage(currentPage, updatedPage);\n\n\t\thandleChangePage(recalculatedPage);\n\t}\n\n\tuseDidUpdateEffect(() => {\n\t\tonSelectedRowsChange({ allSelected, selectedCount, selectedRows: selectedRows.slice(0) });\n\t\t// onSelectedRowsChange trigger is controlled by toggleOnSelectedRowsChange state\n\t}, [toggleOnSelectedRowsChange]);\n\n\tuseDidUpdateEffect(() => {\n\t\tonSort(selectedColumn, sortDirection, sortedData.slice(0));\n\t\t// do not update on sortedData\n\t}, [selectedColumn, sortDirection]);\n\n\tuseDidUpdateEffect(() => {\n\t\tonChangePage(currentPage, paginationTotalRows || sortedData.length);\n\t}, [currentPage]);\n\n\tuseDidUpdateEffect(() => {\n\t\tonChangeRowsPerPage(rowsPerPage, currentPage);\n\t}, [rowsPerPage]);\n\n\tuseDidUpdateEffect(() => {\n\t\thandleChangePage(paginationDefaultPage);\n\t}, [paginationDefaultPage, paginationResetDefaultPage]);\n\n\tuseDidUpdateEffect(() => {\n\t\tif (pagination && paginationServer && paginationTotalRows > 0) {\n\t\t\tconst updatedPage = getNumberOfPages(paginationTotalRows, rowsPerPage);\n\t\t\tconst recalculatedPage = recalculatePage(currentPage, updatedPage);\n\n\t\t\tif (currentPage !== recalculatedPage) {\n\t\t\t\thandleChangePage(recalculatedPage);\n\t\t\t}\n\t\t}\n\t}, [paginationTotalRows]);\n\n\tReact.useEffect(() => {\n\t\tdispatch({ type: 'CLEAR_SELECTED_ROWS', selectedRowsFlag: clearSelectedRows });\n\t}, [selectableRowsSingle, clearSelectedRows]);\n\n\tReact.useEffect(() => {\n\t\tif (!selectableRowSelected) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst preSelectedRows = sortedData.filter(row => selectableRowSelected(row));\n\t\t// if selectableRowsSingle mode then return the first match\n\t\tconst selected = selectableRowsSingle ? preSelectedRows.slice(0, 1) : preSelectedRows;\n\n\t\tdispatch({\n\t\t\ttype: 'SELECT_MULTIPLE_ROWS',\n\t\t\tkeyField,\n\t\t\tselectedRows: selected,\n\t\t\ttotalRows: sortedData.length,\n\t\t\tmergeSelections,\n\t\t});\n\n\t\t// We only want to update the selectedRowState if data changes\n\t\t// eslint-disable-next-line react-hooks/exhaustive-deps\n\t}, [data, selectableRowSelected]);\n\n\tconst visibleRows = selectableRowsVisibleOnly ? tableRows : sortedData;\n\tconst showSelectAll = persistSelectedOnPageChange || selectableRowsSingle || selectableRowsNoSelectAll;\n\n\treturn (\n\t\t<ThemeProvider theme={currentTheme}>\n\t\t\t{showHeader() && (\n\t\t\t\t<Header\n\t\t\t\t\ttitle={title}\n\t\t\t\t\tactions={actions}\n\t\t\t\t\tshowMenu={!noContextMenu}\n\t\t\t\t\tselectedCount={selectedCount}\n\t\t\t\t\tdirection={direction}\n\t\t\t\t\tcontextActions={contextActions}\n\t\t\t\t\tcontextComponent={contextComponent}\n\t\t\t\t\tcontextMessage={contextMessage}\n\t\t\t\t/>\n\t\t\t)}\n\n\t\t\t{subHeader && (\n\t\t\t\t<Subheader align={subHeaderAlign} wrapContent={subHeaderWrap}>\n\t\t\t\t\t{subHeaderComponent}\n\t\t\t\t</Subheader>\n\t\t\t)}\n\n\t\t\t<ResponsiveWrapper\n\t\t\t\t$responsive={responsive}\n\t\t\t\t$fixedHeader={fixedHeader}\n\t\t\t\t$fixedHeaderScrollHeight={fixedHeaderScrollHeight}\n\t\t\t\tclassName={className}\n\t\t\t\t{...wrapperProps}\n\t\t\t>\n\t\t\t\t<Wrapper>\n\t\t\t\t\t{progressPending && !persistTableHead && <ProgressWrapper>{progressComponent}</ProgressWrapper>}\n\n\t\t\t\t\t<Table disabled={disabled} className=\"rdt_Table\" role=\"table\" {...(ariaLabel && { 'aria-label': ariaLabel })}>\n\t\t\t\t\t\t{showTableHead() && (\n\t\t\t\t\t\t\t<Head className=\"rdt_TableHead\" role=\"rowgroup\" $fixedHeader={fixedHeader}>\n\t\t\t\t\t\t\t\t<HeadRow className=\"rdt_TableHeadRow\" role=\"row\" $dense={dense}>\n\t\t\t\t\t\t\t\t\t{selectableRows &&\n\t\t\t\t\t\t\t\t\t\t(showSelectAll ? (\n\t\t\t\t\t\t\t\t\t\t\t<CellBase style={{ flex: '0 0 48px' }} />\n\t\t\t\t\t\t\t\t\t\t) : (\n\t\t\t\t\t\t\t\t\t\t\t<ColumnCheckbox\n\t\t\t\t\t\t\t\t\t\t\t\tallSelected={allSelected}\n\t\t\t\t\t\t\t\t\t\t\t\tselectedRows={selectedRows}\n\t\t\t\t\t\t\t\t\t\t\t\tselectableRowsComponent={selectableRowsComponent}\n\t\t\t\t\t\t\t\t\t\t\t\tselectableRowsComponentProps={selectableRowsComponentProps}\n\t\t\t\t\t\t\t\t\t\t\t\tselectableRowDisabled={selectableRowDisabled}\n\t\t\t\t\t\t\t\t\t\t\t\trowData={visibleRows}\n\t\t\t\t\t\t\t\t\t\t\t\tkeyField={keyField}\n\t\t\t\t\t\t\t\t\t\t\t\tmergeSelections={mergeSelections}\n\t\t\t\t\t\t\t\t\t\t\t\tonSelectAllRows={handleSelectAllRows}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t\t{expandableRows && !expandableRowsHideExpander && <ColumnExpander />}\n\t\t\t\t\t\t\t\t\t{tableColumns.map(column => (\n\t\t\t\t\t\t\t\t\t\t<Column\n\t\t\t\t\t\t\t\t\t\t\tkey={column.id}\n\t\t\t\t\t\t\t\t\t\t\tcolumn={column}\n\t\t\t\t\t\t\t\t\t\t\tselectedColumn={selectedColumn}\n\t\t\t\t\t\t\t\t\t\t\tdisabled={progressPending || sortedData.length === 0}\n\t\t\t\t\t\t\t\t\t\t\tpagination={pagination}\n\t\t\t\t\t\t\t\t\t\t\tpaginationServer={paginationServer}\n\t\t\t\t\t\t\t\t\t\t\tpersistSelectedOnSort={persistSelectedOnSort}\n\t\t\t\t\t\t\t\t\t\t\tselectableRowsVisibleOnly={selectableRowsVisibleOnly}\n\t\t\t\t\t\t\t\t\t\t\tsortDirection={sortDirection}\n\t\t\t\t\t\t\t\t\t\t\tsortIcon={sortIcon}\n\t\t\t\t\t\t\t\t\t\t\tsortServer={sortServer}\n\t\t\t\t\t\t\t\t\t\t\tonSort={handleSort}\n\t\t\t\t\t\t\t\t\t\t\tonDragStart={handleDragStart}\n\t\t\t\t\t\t\t\t\t\t\tonDragOver={handleDragOver}\n\t\t\t\t\t\t\t\t\t\t\tonDragEnd={handleDragEnd}\n\t\t\t\t\t\t\t\t\t\t\tonDragEnter={handleDragEnter}\n\t\t\t\t\t\t\t\t\t\t\tonDragLeave={handleDragLeave}\n\t\t\t\t\t\t\t\t\t\t\tdraggingColumnId={draggingColumnId}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t</HeadRow>\n\t\t\t\t\t\t\t</Head>\n\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t{!sortedData.length && !progressPending && <NoData>{noDataComponent}</NoData>}\n\n\t\t\t\t\t\t{progressPending && persistTableHead && <ProgressWrapper>{progressComponent}</ProgressWrapper>}\n\n\t\t\t\t\t\t{!progressPending && sortedData.length > 0 && (\n\t\t\t\t\t\t\t<Body className=\"rdt_TableBody\" role=\"rowgroup\">\n\t\t\t\t\t\t\t\t{tableRows.map((row, i) => {\n\t\t\t\t\t\t\t\t\tconst key = prop(row as TableRow, keyField) as string | number;\n\t\t\t\t\t\t\t\t\tconst id = isEmpty(key) ? i : key;\n\t\t\t\t\t\t\t\t\tconst selected = isRowSelected(row, selectedRows, keyField);\n\t\t\t\t\t\t\t\t\tconst expanderExpander = !!(expandableRows && expandableRowExpanded && expandableRowExpanded(row));\n\t\t\t\t\t\t\t\t\tconst expanderDisabled = !!(expandableRows && expandableRowDisabled && expandableRowDisabled(row));\n\n\t\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\t\t<Row\n\t\t\t\t\t\t\t\t\t\t\tid={id}\n\t\t\t\t\t\t\t\t\t\t\tkey={id}\n\t\t\t\t\t\t\t\t\t\t\tkeyField={keyField}\n\t\t\t\t\t\t\t\t\t\t\tdata-row-id={id}\n\t\t\t\t\t\t\t\t\t\t\tcolumns={tableColumns}\n\t\t\t\t\t\t\t\t\t\t\trow={row}\n\t\t\t\t\t\t\t\t\t\t\trowCount={sortedData.length}\n\t\t\t\t\t\t\t\t\t\t\trowIndex={i}\n\t\t\t\t\t\t\t\t\t\t\tselectableRows={selectableRows}\n\t\t\t\t\t\t\t\t\t\t\texpandableRows={expandableRows}\n\t\t\t\t\t\t\t\t\t\t\texpandableIcon={expandableIcon}\n\t\t\t\t\t\t\t\t\t\t\thighlightOnHover={highlightOnHover}\n\t\t\t\t\t\t\t\t\t\t\tpointerOnHover={pointerOnHover}\n\t\t\t\t\t\t\t\t\t\t\tdense={dense}\n\t\t\t\t\t\t\t\t\t\t\texpandOnRowClicked={expandOnRowClicked}\n\t\t\t\t\t\t\t\t\t\t\texpandOnRowDoubleClicked={expandOnRowDoubleClicked}\n\t\t\t\t\t\t\t\t\t\t\texpandableRowsComponent={expandableRowsComponent}\n\t\t\t\t\t\t\t\t\t\t\texpandableRowsComponentProps={expandableRowsComponentProps}\n\t\t\t\t\t\t\t\t\t\t\texpandableRowsHideExpander={expandableRowsHideExpander}\n\t\t\t\t\t\t\t\t\t\t\tdefaultExpanderDisabled={expanderDisabled}\n\t\t\t\t\t\t\t\t\t\t\tdefaultExpanded={expanderExpander}\n\t\t\t\t\t\t\t\t\t\t\texpandableInheritConditionalStyles={expandableInheritConditionalStyles}\n\t\t\t\t\t\t\t\t\t\t\tconditionalRowStyles={conditionalRowStyles}\n\t\t\t\t\t\t\t\t\t\t\tselected={selected}\n\t\t\t\t\t\t\t\t\t\t\tselectableRowsHighlight={selectableRowsHighlight}\n\t\t\t\t\t\t\t\t\t\t\tselectableRowsComponent={selectableRowsComponent}\n\t\t\t\t\t\t\t\t\t\t\tselectableRowsComponentProps={selectableRowsComponentProps}\n\t\t\t\t\t\t\t\t\t\t\tselectableRowDisabled={selectableRowDisabled}\n\t\t\t\t\t\t\t\t\t\t\tselectableRowsSingle={selectableRowsSingle}\n\t\t\t\t\t\t\t\t\t\t\tstriped={striped}\n\t\t\t\t\t\t\t\t\t\t\tonRowExpandToggled={onRowExpandToggled}\n\t\t\t\t\t\t\t\t\t\t\tonRowClicked={handleRowClicked}\n\t\t\t\t\t\t\t\t\t\t\tonRowDoubleClicked={handleRowDoubleClicked}\n\t\t\t\t\t\t\t\t\t\t\tonRowMouseEnter={handleRowMouseEnter}\n\t\t\t\t\t\t\t\t\t\t\tonRowMouseLeave={handleRowMouseLeave}\n\t\t\t\t\t\t\t\t\t\t\tonSelectedRow={handleSelectedRow}\n\t\t\t\t\t\t\t\t\t\t\tdraggingColumnId={draggingColumnId}\n\t\t\t\t\t\t\t\t\t\t\tonDragStart={handleDragStart}\n\t\t\t\t\t\t\t\t\t\t\tonDragOver={handleDragOver}\n\t\t\t\t\t\t\t\t\t\t\tonDragEnd={handleDragEnd}\n\t\t\t\t\t\t\t\t\t\t\tonDragEnter={handleDragEnter}\n\t\t\t\t\t\t\t\t\t\t\tonDragLeave={handleDragLeave}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t})}\n\t\t\t\t\t\t\t</Body>\n\t\t\t\t\t\t)}\n\t\t\t\t\t</Table>\n\t\t\t\t</Wrapper>\n\t\t\t</ResponsiveWrapper>\n\n\t\t\t{enabledPagination && (\n\t\t\t\t<div>\n\t\t\t\t\t<Pagination\n\t\t\t\t\t\tonChangePage={handleChangePage}\n\t\t\t\t\t\tonChangeRowsPerPage={handleChangeRowsPerPage}\n\t\t\t\t\t\trowCount={paginationTotalRows || sortedData.length}\n\t\t\t\t\t\tcurrentPage={currentPage}\n\t\t\t\t\t\trowsPerPage={rowsPerPage}\n\t\t\t\t\t\tdirection={direction}\n\t\t\t\t\t\tpaginationRowsPerPageOptions={paginationRowsPerPageOptions}\n\t\t\t\t\t\tpaginationIconLastPage={paginationIconLastPage}\n\t\t\t\t\t\tpaginationIconFirstPage={paginationIconFirstPage}\n\t\t\t\t\t\tpaginationIconNext={paginationIconNext}\n\t\t\t\t\t\tpaginationIconPrevious={paginationIconPrevious}\n\t\t\t\t\t\tpaginationComponentOptions={paginationComponentOptions}\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t)}\n\t\t</ThemeProvider>\n\t);\n}\n\nexport default React.memo(DataTable) as typeof DataTable;\n", "import merge from 'deepmerge';\nimport { defaultThemes } from './themes';\nimport { TableStyles, Theme, Themes } from './types';\n\nexport const defaultStyles = (theme: Theme): TableStyles => ({\n\ttable: {\n\t\tstyle: {\n\t\t\tcolor: theme.text.primary,\n\t\t\tbackgroundColor: theme.background.default,\n\t\t},\n\t},\n\ttableWrapper: {\n\t\tstyle: {\n\t\t\tdisplay: 'table',\n\t\t},\n\t},\n\tresponsiveWrapper: {\n\t\tstyle: {},\n\t},\n\theader: {\n\t\tstyle: {\n\t\t\tfontSize: '22px',\n\t\t\tcolor: theme.text.primary,\n\t\t\tbackgroundColor: theme.background.default,\n\t\t\tminHeight: '56px',\n\t\t\tpaddingLeft: '16px',\n\t\t\tpaddingRight: '8px',\n\t\t},\n\t},\n\tsubHeader: {\n\t\tstyle: {\n\t\t\tbackgroundColor: theme.background.default,\n\t\t\tminHeight: '52px',\n\t\t},\n\t},\n\thead: {\n\t\tstyle: {\n\t\t\tcolor: theme.text.primary,\n\t\t\tfontSize: '12px',\n\t\t\tfontWeight: 500,\n\t\t},\n\t},\n\theadRow: {\n\t\tstyle: {\n\t\t\tbackgroundColor: theme.background.default,\n\t\t\tminHeight: '52px',\n\t\t\tborderBottomWidth: '1px',\n\t\t\tborderBottomColor: theme.divider.default,\n\t\t\tborderBottomStyle: 'solid',\n\t\t},\n\t\tdenseStyle: {\n\t\t\tminHeight: '32px',\n\t\t},\n\t},\n\theadCells: {\n\t\tstyle: {\n\t\t\tpaddingLeft: '16px',\n\t\t\tpaddingRight: '16px',\n\t\t},\n\t\tdraggingStyle: {\n\t\t\tcursor: 'move',\n\t\t},\n\t},\n\tcontextMenu: {\n\t\tstyle: {\n\t\t\tbackgroundColor: theme.context.background,\n\t\t\tfontSize: '18px',\n\t\t\tfontWeight: 400,\n\t\t\tcolor: theme.context.text,\n\t\t\tpaddingLeft: '16px',\n\t\t\tpaddingRight: '8px',\n\t\t\ttransform: 'translate3d(0, -100%, 0)',\n\t\t\ttransitionDuration: '125ms',\n\t\t\ttransitionTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)',\n\t\t\twillChange: 'transform',\n\t\t},\n\t\tactiveStyle: {\n\t\t\ttransform: 'translate3d(0, 0, 0)',\n\t\t},\n\t},\n\tcells: {\n\t\tstyle: {\n\t\t\tpaddingLeft: '16px',\n\t\t\tpaddingRight: '16px',\n\t\t\twordBreak: 'break-word',\n\t\t},\n\t\tdraggingStyle: {},\n\t},\n\trows: {\n\t\tstyle: {\n\t\t\tfontSize: '13px',\n\t\t\tfontWeight: 400,\n\t\t\tcolor: theme.text.primary,\n\t\t\tbackgroundColor: theme.background.default,\n\t\t\tminHeight: '48px',\n\t\t\t'&:not(:last-of-type)': {\n\t\t\t\tborderBottomStyle: 'solid',\n\t\t\t\tborderBottomWidth: '1px',\n\t\t\t\tborderBottomColor: theme.divider.default,\n\t\t\t},\n\t\t},\n\t\tdenseStyle: {\n\t\t\tminHeight: '32px',\n\t\t},\n\t\tselectedHighlightStyle: {\n\t\t\t// use nth-of-type(n) to override other nth selectors\n\t\t\t'&:nth-of-type(n)': {\n\t\t\t\tcolor: theme.selected.text,\n\t\t\t\tbackgroundColor: theme.selected.default,\n\t\t\t\tborderBottomColor: theme.background.default,\n\t\t\t},\n\t\t},\n\t\thighlightOnHoverStyle: {\n\t\t\tcolor: theme.highlightOnHover.text,\n\t\t\tbackgroundColor: theme.highlightOnHover.default,\n\t\t\ttransitionDuration: '0.15s',\n\t\t\ttransitionProperty: 'background-color',\n\t\t\tborderBottomColor: theme.background.default,\n\t\t\toutlineStyle: 'solid',\n\t\t\toutlineWidth: '1px',\n\t\t\toutlineColor: theme.background.default,\n\t\t},\n\t\tstripedStyle: {\n\t\t\tcolor: theme.striped.text,\n\t\t\tbackgroundColor: theme.striped.default,\n\t\t},\n\t},\n\texpanderRow: {\n\t\tstyle: {\n\t\t\tcolor: theme.text.primary,\n\t\t\tbackgroundColor: theme.background.default,\n\t\t},\n\t},\n\texpanderCell: {\n\t\tstyle: {\n\t\t\tflex: '0 0 48px',\n\t\t},\n\t},\n\texpanderButton: {\n\t\tstyle: {\n\t\t\tcolor: theme.button.default,\n\t\t\tfill: theme.button.default,\n\t\t\tbackgroundColor: 'transparent',\n\t\t\tborderRadius: '2px',\n\t\t\ttransition: '0.25s',\n\t\t\theight: '100%',\n\t\t\twidth: '100%',\n\t\t\t'&:hover:enabled': {\n\t\t\t\tcursor: 'pointer',\n\t\t\t},\n\t\t\t'&:disabled': {\n\t\t\t\tcolor: theme.button.disabled,\n\t\t\t},\n\t\t\t'&:hover:not(:disabled)': {\n\t\t\t\tcursor: 'pointer',\n\t\t\t\tbackgroundColor: theme.button.hover,\n\t\t\t},\n\t\t\t'&:focus': {\n\t\t\t\toutline: 'none',\n\t\t\t\tbackgroundColor: theme.button.focus,\n\t\t\t},\n\t\t\tsvg: {\n\t\t\t\tmargin: 'auto',\n\t\t\t},\n\t\t},\n\t},\n\tpagination: {\n\t\tstyle: {\n\t\t\tcolor: theme.text.secondary,\n\t\t\tfontSize: '13px',\n\t\t\tminHeight: '56px',\n\t\t\tbackgroundColor: theme.background.default,\n\t\t\tborderTopStyle: 'solid',\n\t\t\tborderTopWidth: '1px',\n\t\t\tborderTopColor: theme.divider.default,\n\t\t},\n\t\tpageButtonsStyle: {\n\t\t\tborderRadius: '50%',\n\t\t\theight: '40px',\n\t\t\twidth: '40px',\n\t\t\tpadding: '8px',\n\t\t\tmargin: 'px',\n\t\t\tcursor: 'pointer',\n\t\t\ttransition: '0.4s',\n\t\t\tcolor: theme.button.default,\n\t\t\tfill: theme.button.default,\n\t\t\tbackgroundColor: 'transparent',\n\t\t\t'&:disabled': {\n\t\t\t\tcursor: 'unset',\n\t\t\t\tcolor: theme.button.disabled,\n\t\t\t\tfill: theme.button.disabled,\n\t\t\t},\n\t\t\t'&:hover:not(:disabled)': {\n\t\t\t\tbackgroundColor: theme.button.hover,\n\t\t\t},\n\t\t\t'&:focus': {\n\t\t\t\toutline: 'none',\n\t\t\t\tbackgroundColor: theme.button.focus,\n\t\t\t},\n\t\t},\n\t},\n\tnoData: {\n\t\tstyle: {\n\t\t\tdisplay: 'flex',\n\t\t\talignItems: 'center',\n\t\t\tjustifyContent: 'center',\n\t\t\tcolor: theme.text.primary,\n\t\t\tbackgroundColor: theme.background.default,\n\t\t},\n\t},\n\tprogress: {\n\t\tstyle: {\n\t\t\tdisplay: 'flex',\n\t\t\talignItems: 'center',\n\t\t\tjustifyContent: 'center',\n\t\t\tcolor: theme.text.primary,\n\t\t\tbackgroundColor: theme.background.default,\n\t\t},\n\t},\n});\n\nexport const createStyles = (\n\tcustomStyles: TableStyles = {},\n\tthemeName = 'default',\n\tinherit: Themes = 'default',\n): TableStyles => {\n\tconst themeType = defaultThemes[themeName] ? themeName : inherit;\n\n\treturn merge(defaultStyles(defaultThemes[themeType]), customStyles);\n};\n"], "names": ["SortOrder", "prop", "obj", "key", "insertItem", "array", "item", "index", "slice", "removeItem", "keyField", "newArray", "outerField", "splice", "findIndex", "a", "decorateColumns", "columns", "map", "column", "decoratedColumn", "Object", "assign", "sortable", "sortFunction", "undefined", "id", "getNumberOfPages", "rowCount", "rowsPerPage", "Math", "ceil", "recalculatePage", "prevPage", "nextPage", "min", "noop", "getConditionalStyle", "row", "conditionalRowStyles", "baseClassNames", "rowStyle", "classNames", "length", "for<PERSON>ach", "crs", "when", "Error", "style", "conditionalStyle", "join", "isRowSelected", "selectedRows", "some", "r", "findColumnIndexById", "c", "equalizeId", "b", "tableReducer", "state", "action", "toggleOnSelectedRowsChange", "type", "rows", "mergeSelections", "allChecked", "allSelected", "selections", "filter", "selectedCount", "isSelected", "singleSelect", "totalRows", "selectedRowsFlag", "sortDirection", "selectedColumn", "clearSelectedOnSort", "currentPage", "page", "paginationServer", "visibleOnly", "persistSelectedOnPageChange", "clearSelectedOnPage", "disabledCSS", "css", "TableStyle", "styled", "div", "disabled", "theme", "table", "fixedCSS", "Head", "$fixedHeader", "head", "HeadRow", "headRow", "$dense", "denseStyle", "media", "literals", "args", "value", "CellBase", "$headCell", "$noPadding", "CellExtended", "button", "grow", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "width", "right", "center", "compact", "hide", "Number", "isInteger", "overflowCSS", "$wrapCell", "$allowOverflow", "CellStyle", "attrs", "props", "$renderAsCell", "$isDragging", "cells", "draggingStyle", "$cellStyle", "TableCell", "React", "memo", "rowIndex", "dataTag", "isDragging", "onDragStart", "onDragOver", "onDragEnd", "onDragEnter", "onDragLeave", "conditionalCellStyles", "createElement", "role", "className", "cell", "allowOverflow", "wrap", "selector", "format", "getProperty", "defaultComponentName", "Checkbox$1", "name", "component", "componentOptions", "indeterminate", "checked", "onClick", "TagName", "baseStyle", "fontSize", "cursor", "padding", "marginTop", "verticalAlign", "position", "calculateBaseStyle", "resolvedComponentOptions", "useMemo", "object", "newObject", "keys", "o", "oldObject", "handleFunctionProps", "ref", "checkbox", "onChange", "TableCellCheckboxStyle", "TableCellCheckbox", "selected", "selectableRowsComponent", "selectableRowsComponentProps", "selectableRowsSingle", "selectableRowDisabled", "onSelectedRow", "e", "stopPropagation", "Checkbox", "ButtonStyle", "expanderButton", "ExpanderButton", "expanded", "expandableIcon", "onToggled", "icon", "collapsed", "CellExpanderStyle", "expanderCell", "CellExpander", "ExpanderRowStyle", "expanderRow", "$extendedRowStyle", "ExpanderRow$1", "data", "ExpanderComponent", "expanderComponentProps", "extendedRowStyle", "extendedClassNames", "split", "STOP_PROP_TAG", "Direction", "Alignment", "Media", "highlightCSS", "$highlightOnHover", "highlightOnHoverStyle", "pointerCSS", "TableRowStyle", "$striped", "stripedStyle", "$pointerOnHover", "$selected", "selectedHighlightStyle", "$conditionalStyle", "Row", "defaultExpanded", "defaultExpanderDisabled", "dense", "expandableRows", "expandableRowsComponent", "expandableRowsComponentProps", "expandableRowsHideExpander", "expandOnRowClicked", "expandOnRowDoubleClicked", "highlightOnHover", "expandableInheritConditionalStyles", "onRowClicked", "onRowDoubleClicked", "onRowMouseEnter", "onRowMouseLeave", "onRowExpandToggled", "pointerOnHover", "selectableRows", "selectableRowsHighlight", "striped", "draggingColumnId", "setExpanded", "useState", "useEffect", "handleExpanded", "useCallback", "showPointer", "handleRowClick", "target", "getAttribute", "handleRowDoubleClick", "handleRowMouseEnter", "handleRowMouseLeave", "rowKeyField", "highlightSelected", "inheritStyles", "isStriped", "Fragment", "onDoubleClick", "onMouseEnter", "onMouseLeave", "TableCellExpander", "omit", "ignoreRowClick", "ExpanderRow", "Icon", "span", "$sortActive", "$sortDirection", "NativeSortIcon", "sortActive", "ColumnStyled", "head<PERSON>ells", "sortableCSS", "ColumnSortable", "ColumnText", "Column", "sortIcon", "sortServer", "pagination", "persistSelectedOnSort", "selectableRowsVisibleOnly", "onSort", "console", "error", "showTooltip", "setShowTooltip", "columnRef", "useRef", "current", "scrollWidth", "clientWidth", "handleSortChange", "direction", "ASC", "DESC", "renderNativeSortIcon", "renderCustomSortIcon", "disableSort", "nativeSortIconLeft", "nativeSortIconRight", "customSortIconLeft", "customSortIconRight", "draggable", "reorder", "tabIndex", "onKeyPress", "event", "title", "ColumnStyle", "ColumnCheckbox", "head<PERSON>ell", "rowData", "onSelectAllRows", "isDisabled", "useRTL", "AUTO", "isClient", "window", "isRTL", "setIsRTL", "canUse", "document", "bodyRTL", "getElementsByTagName", "htmlTRL", "hasRTL", "dir", "Title", "contextMenu", "fontColor", "ContextActions", "ContextMenuStyle", "$rtl", "$visible", "activeStyle", "ContextMenu", "contextMessage", "contextActions", "contextComponent", "visible", "cloneElement", "rtl", "datum<PERSON><PERSON>", "singular", "plural", "message", "generateDefaultContextTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header", "Actions", "Header", "actions", "showMenu", "alignMap", "left", "SubheaderWrapper", "align", "$wrapContent", "subHeader", "Subheader", "_a", "wrapContent", "rest", "__rest", "Body", "ResponsiveWrapper", "$responsive", "$fixedHeaderScrollHeight", "responsiveWrapper", "ProgressWrapper", "progress", "Wrapper", "tableWrapper", "ColumnExpander", "NoDataWrapper", "noData", "DropdownIcon", "xmlns", "height", "viewBox", "d", "fill", "SelectControl", "select", "SelectWrapper", "Select", "defaultValue", "DropDownIcon", "defaultProps", "selectableRowsNoSelectAll", "selectableRowSelected", "clearSelectedRows", "expandableRowDisabled", "expandableRowExpanded", "progressPending", "progressComponent", "fontWeight", "persistTableHead", "noContextMenu", "defaultSortFieldId", "defaultSortAsc", "responsive", "noDataComponent", "noTableHead", "<PERSON><PERSON><PERSON><PERSON>", "subHeaderAlign", "RIGHT", "subHeaderWrap", "subHeaderComponent", "fixedHeader", "fixedHeaderScrollHeight", "paginationServerOptions", "paginationDefaultPage", "paginationResetDefaultPage", "paginationTotalRows", "paginationPerPage", "paginationRowsPerPageOptions", "paginationComponent", "paginationComponentOptions", "paginationIconFirstPage", "paginationIconLastPage", "paginationIconNext", "paginationIconPrevious", "customStyles", "onChangePage", "onChangeRowsPerPage", "onSelectedRowsChange", "onColumnOrderChange", "defaultComponentOptions", "rowsPerPageText", "rangeSeparatorText", "noRowsPerPage", "selectAllRowsItem", "selectAllRowsItemText", "PaginationWrapper", "nav", "<PERSON><PERSON>", "pageButtonsStyle", "$isRTL", "PageList", "Span", "Range", "RowLabel", "NativePagination", "windowSize", "getSize", "innerWidth", "innerHeight", "setWindowSize", "handleResize", "addEventListener", "removeEventListener", "useWindowSize", "shouldShow", "numPages", "lastIndex", "firstIndex", "<PERSON><PERSON><PERSON><PERSON>", "disabledGreater", "options", "range", "handlePrevious", "handleNext", "handleFirst", "handleLast", "handleRowsPerPage", "selectOptions", "num", "push", "useFirstUpdate", "fn", "inputs", "firstUpdate", "isMergeableObject", "isNonNullObject", "stringValue", "prototype", "toString", "call", "$$typeof", "REACT_ELEMENT_TYPE", "isReactElement", "isSpecial", "Symbol", "for", "cloneUnlessOtherwiseSpecified", "clone", "deepmerge", "val", "Array", "isArray", "defaultArrayMerge", "source", "concat", "element", "get<PERSON><PERSON><PERSON>", "getOwnPropertySymbols", "symbol", "propertyIsEnumerable", "getEnumerableOwnPropertySymbols", "propertyIsOnObject", "property", "_", "mergeObject", "destination", "hasOwnProperty", "propertyIsUnsafe", "customMerge", "getMergeFunction", "arrayMerge", "sourceIsArray", "all", "reduce", "prev", "next", "defaultTheme", "text", "primary", "secondary", "background", "default", "context", "divider", "focus", "hover", "defaultThemes", "light", "dark", "useColumns", "tableColumns", "setTableColumns", "setDraggingColumn", "sourceColumnId", "useDidUpdateEffect", "handleDragStart", "attributes", "getNamedItem", "_b", "handleDragEnter", "selectedColIndex", "targetColIndex", "reorderedCols", "handleDragOver", "preventDefault", "handleDragLeave", "handleDragEnd", "defaultSortDirection", "ascDirection", "getSortDirection", "defaultSortColumn", "DataTable$1", "aria<PERSON><PERSON><PERSON>", "dispatch", "useReducer", "enabledPagination", "Pagination", "currentTheme", "themeName", "inherit", "themeType", "merge", "color", "backgroundColor", "display", "minHeight", "paddingLeft", "paddingRight", "borderBottomWidth", "borderBottomColor", "borderBottomStyle", "transform", "transitionDuration", "transitionTimingFunction", "<PERSON><PERSON><PERSON><PERSON>", "wordBreak", "transitionProperty", "outlineStyle", "outlineWidth", "outlineColor", "flex", "borderRadius", "transition", "outline", "svg", "margin", "borderTopStyle", "borderTopWidth", "borderTopColor", "alignItems", "justifyContent", "createStyles", "wrapperProps", "sortedData", "sortFn", "customSortFunction", "sort", "aValue", "bValue", "tableRows", "handleSort", "handleSelectAllRows", "handleSelectedRow", "handleRowClicked", "handleRowDoubleClicked", "handleChangePage", "handleChangeRowsPerPage", "newRowsPerPage", "updatedPage", "recalculatedPage", "preSelectedRows", "visibleRows", "showSelectAll", "ThemeProvider", "Table", "NoData", "i", "field", "isEmpty", "expanderExpander", "expanderDisabled", "customTheme"], "mappings": "qeAGYA,uBCAI,SAAAC,EAA2BC,EAAQC,GAClD,OAAOD,EAAIC,EACZ,CAwEM,SAAUC,EAAcC,EAAa,GAAIC,EAASC,EAAQ,GAC/D,MAAO,IAAIF,EAAMG,MAAM,EAAGD,GAAQD,KAASD,EAAMG,MAAMD,GACxD,CAEM,SAAUE,EAAcJ,EAAa,GAAIC,EAASI,EAAW,MAClE,MAAMC,EAAWN,EAAMG,QACjBI,EAAaX,EAAKK,EAAkBI,GAkB1C,OAhBIE,EACHD,EAASE,OACRF,EAASG,WAAWC,GACAd,EAAKc,EAAeL,KAEjBE,IAEvB,GAGDD,EAASE,OACRF,EAASG,WAAUC,GAAKA,IAAMT,IAC9B,GAIKK,CACR,CAGM,SAAUK,EAAmBC,GAClC,OAAOA,EAAQC,KAAI,CAACC,EAAQZ,KAC3B,MAAMa,EACFC,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EAAAH,IACHI,SAAUJ,EAAOI,YAAcJ,EAAOK,mBAAgBC,IAGvD,OAAKN,EAAOO,KACXN,EAAgBM,GAAKnB,EAAQ,GAKvBa,CAAe,GAExB,CA0BgB,SAAAO,EAAiBC,EAAkBC,GAClD,OAAOC,KAAKC,KAAKH,EAAWC,EAC7B,CAEgB,SAAAG,EAAgBC,EAAkBC,GACjD,OAAOJ,KAAKK,IAAIF,EAAUC,EAC3B,EDrJA,SAAYlC,GACXA,EAAA,IAAA,MACAA,EAAA,KAAA,MACA,CAHD,CAAYA,IAAAA,EAGX,CAAA,ICoJM,MAAMoC,EAAO,IAAY,KAE1B,SAAUC,EACfC,EACAC,EAA+C,GAC/CC,EAA2B,IAE3B,IAAIC,EAAW,CAAA,EACXC,EAAuB,IAAIF,GAuB/B,OArBID,EAAqBI,QACxBJ,EAAqBK,SAAQC,IAC5B,IAAKA,EAAIC,MAA4B,mBAAbD,EAAIC,KAC3B,MAAM,IAAIC,MAAM,+EAIbF,EAAIC,KAAKR,KACZG,EAAWI,EAAIG,OAAS,GAEpBH,EAAIH,aACPA,EAAa,IAAIA,KAAeG,EAAIH,aAGZ,mBAAdG,EAAIG,QACdP,EAAWI,EAAIG,MAAMV,IAAQ,CAAA,GAE9B,IAII,CAAEW,iBAAkBR,EAAUC,WAAYA,EAAWQ,KAAK,KAClE,CAEM,SAAUC,EAAiBb,EAAQc,EAAoB,GAAI1C,EAAW,MAE3E,MAAME,EAAaX,EAAKqC,EAAiB5B,GAEzC,OAAIE,EACIwC,EAAaC,MAAKC,GACLrD,EAAKqD,EAAe5C,KAEjBE,IAIjBwC,EAAaC,MAAKC,GAAKA,IAAMhB,GACrC,CAMgB,SAAAiB,EAAuBtC,EAA2BS,GACjE,OAAKA,EAIET,EAAQH,WAAU0C,GACjBC,EAAWD,EAAE9B,GAAIA,MAJhB,CAMV,CAEgB,SAAA+B,EAAW1C,EAAgC2C,GAC1D,OAAO3C,GAAK2C,CACb,CCxNgB,SAAAC,EAAgBC,EAAsBC,GACrD,MAAMC,GAA8BF,EAAME,2BAE1C,OAAQD,EAAOE,MACd,IAAK,kBAAmB,CACvB,MAAMrD,SAAEA,EAAQsD,KAAEA,EAAIpC,SAAEA,EAAQqC,gBAAEA,GAAoBJ,EAChDK,GAAcN,EAAMO,YACpBL,GAA8BF,EAAME,2BAE1C,GAAIG,EAAiB,CACpB,MAAMG,EAAaF,EAChB,IAAIN,EAAMR,gBAAiBY,EAAKK,QAAO/B,IAAQa,EAAcb,EAAKsB,EAAMR,aAAc1C,MACtFkD,EAAMR,aAAaiB,QAAO/B,IAAQa,EAAcb,EAAK0B,EAAMtD,KAE9D,OAAAW,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EACIsC,GACH,CAAAO,YAAaD,EACbI,cAAeF,EAAWzB,OAC1BS,aAAcgB,EACdN,8BAED,CAED,OAAAzC,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EACIsC,GAAK,CACRO,YAAaD,EACbI,cAAeJ,EAAatC,EAAW,EACvCwB,aAAcc,EAAaF,EAAO,GAClCF,8BAED,CAED,IAAK,oBAAqB,CACzB,MAAMpD,SAAEA,EAAQ4B,IAAEA,EAAGiC,WAAEA,EAAU3C,SAAEA,EAAQ4C,aAAEA,GAAiBX,EAG9D,OAAIW,EACCD,EACHlD,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EACIsC,GAAK,CACRU,cAAe,EACfH,aAAa,EACbf,aAAc,GACdU,+BAIFzC,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EACIsC,GACH,CAAAU,cAAe,EACfH,aAAa,EACbf,aAAc,CAACd,GACfwB,+BAKES,EAEClD,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EAAAsC,IACHU,cAAeV,EAAMR,aAAaT,OAAS,EAAIiB,EAAMR,aAAaT,OAAS,EAAI,EAC/EwB,aAAa,EACbf,aAAc3C,EAAWmD,EAAMR,aAAcd,EAAK5B,GAClDoD,+BAIFzC,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EACIsC,GACH,CAAAU,cAAeV,EAAMR,aAAaT,OAAS,EAC3CwB,YAAaP,EAAMR,aAAaT,OAAS,IAAMf,EAC/CwB,aAAchD,EAAWwD,EAAMR,aAAcd,GAC7CwB,8BAED,CAED,IAAK,uBAAwB,CAC5B,MAAMpD,SAAEA,EAAQ0C,aAAEA,EAAYqB,UAAEA,EAASR,gBAAEA,GAAoBJ,EAE/D,GAAII,EAAiB,CACpB,MAAMG,EAAa,IACfR,EAAMR,gBACNA,EAAaiB,QAAO/B,IAAQa,EAAcb,EAAKsB,EAAMR,aAAc1C,MAGvE,OAAAW,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EACIsC,GACH,CAAAU,cAAeF,EAAWzB,OAC1BwB,aAAa,EACbf,aAAcgB,EACdN,8BAED,CAED,OAAAzC,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EACIsC,GACH,CAAAU,cAAelB,EAAaT,OAC5BwB,YAAaf,EAAaT,SAAW8B,EACrCrB,eACAU,8BAED,CAED,IAAK,sBAAuB,CAC3B,MAAMY,iBAAEA,GAAqBb,EAE7B,OAAAxC,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EACIsC,GAAK,CACRO,aAAa,EACbG,cAAe,EACflB,aAAc,GACdsB,oBAED,CAED,IAAK,cAAe,CACnB,MAAMC,cAAEA,EAAaC,eAAEA,EAAcC,oBAAEA,GAAwBhB,EAE/D,OACIxC,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EAAAsC,IACHgB,iBACAD,gBACAG,YAAa,IAETD,GAAuB,CAC1BV,aAAa,EACbG,cAAe,EACflB,aAAc,GACdU,8BAGF,CAED,IAAK,cAAe,CACnB,MAAMiB,KAAEA,EAAIC,iBAAEA,EAAgBC,YAAEA,EAAWC,4BAAEA,GAAgCrB,EACvEI,EAAkBe,GAAoBE,EACtCC,EAAuBH,IAAqBE,GAAgCD,EAElF,OACI5D,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EAAAsC,IACHkB,YAAaC,IACTd,GAAmB,CACtBE,aAAa,IAGVgB,GAAuB,CAC1BhB,aAAa,EACbG,cAAe,EACflB,aAAc,GACdU,8BAGF,CAED,IAAK,uBAAwB,CAC5B,MAAMjC,YAAEA,EAAWkD,KAAEA,GAASlB,EAE9B,OAAAxC,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EACIsC,GACH,CAAAkB,YAAaC,EACblD,eAED,EAEH,CCrKA,MAAMuD,EAAcC,EAAGA,GAAA;;;EAKjBC,EAAaC,EAAAA,QAAOC,GAExB;;;;;;;;GAQC,EAAGC,cAAeA,GAAYL;GAC9B,EAAGM,WAAYA,EAAMC,MAAM3C;EChBxB4C,EAAWP,EAAGA,GAAA;;;;;EAOdQ,EAAON,EAAAA,QAAOC,GAElB;;;GAGC,EAAGM,kBAAmBA,GAAgBF;GACtC,EAAGF,WAAYA,EAAMK,KAAK/C;ECbvBgD,EAAUT,EAAAA,QAAOC,GAGrB;;;;GAIC,EAAGE,WAAYA,EAAMO,QAAQjD;GAC7B,EAAGkD,SAAQR,WAAYQ,GAAUR,EAAMO,QAAQE;ECJrCC,EACR,CAACC,KAAmCC,IAAuCjB,EAAGA,GAAA;kCAL9D;KAOhBA,MAAIgB,KAAaC;;GAHTF,EAMR,CAACC,KAAmCC,IAAuCjB,EAAGA,GAAA;kCAT7D;KAWjBA,MAAIgB,KAAaC;;GARTF,EAWR,CAACC,KAAmCC,IAAuCjB,EAAGA,GAAA;kCAb9D;KAehBA,MAAIgB,KAAaC;;GAbTF,EAiBVG,GACD,CAACF,KAAmCC,IAAuCjB,EAAAA,GAAG;mCAC7CkB;MAC7BlB,MAAIgB,KAAaC;;ICtBVE,EAAWjB,EAAAA,QAAOC,GAG7B;;;;;;GAMC,EAAGE,QAAOe,eAAgBf,EAAMe,EAAY,YAAc,SAASzD;GACnE,EAAG0D,gBAAiBA,GAAc;EASxBC,EAAepB,EAAAA,QAAOiB,EAAoB;cACzC,EAAGI,SAAQC,UAAqB,IAATA,GAAcD,EAAS,EAAIC,GAAQ;;;cAG1D,EAAGC,cAAeA,GAAY;cAC9B,EAAGC,cAAeA,GAAY;GACzC,EAAGC,WACJA,GACA3B,EAAAA,GAAG;gBACW2B;gBACAA;;GAEb,EAAGC,WAAYA,GAAS;GACxB,EAAGL,SAAQM,aAAcA,GAAUN,IAAW;GAC9C,EAAGO,UAASP,aAAcO,GAAWP,IAAW;;;GAGhD,EAAGQ,UACJA,GACS,OAATA,GACAhB,CAAQ;;;GAGP,EAAGgB,UACJA,GACS,OAATA,GACAhB,CAAQ;;;GAGP,EAAGgB,UACJA,GACS,OAATA,GACAhB,CAAQ;;;GAGP,EAAGgB,UACJA,GACAC,OAAOC,UAAUF,IACjBhB,EAAagB,EAAe;;;EC/CxBG,EAAclC,EAAGA,GAAgB;;iBAEtB,EAAGmC,eAAiBA,EAAY,SAAW;cAC9C,EAAGC,oBAAsBA,EAAiB,UAAY;;;EAK9DC,EAAYnC,EAAAA,QAAOoB,GAAcgB,OAAMC,IAAU,CACtD5E,MAAO4E,EAAM5E,SACK;GAChB,EAAG6E,oBAAqBA,GAAiBN;GACzC,EAAG7B,QAAOoC,iBAAkBA,GAAepC,EAAMqC,MAAMC;GACvD,EAAGC,gBAAiBA;EAkEvB,IAAAC,EAAeC,EAAMC,MAjDrB,UAAiB1G,GAChBA,EAAEP,OACFA,EAAMmB,IACNA,EAAG+F,SACHA,EAAQC,QACRA,EAAOC,WACPA,EAAUC,YACVA,EAAWC,WACXA,EAAUC,UACVA,EAASC,YACTA,EAAWC,YACXA,IAEA,MAAM3F,iBAAEA,EAAgBP,WAAEA,GAAeL,EAAoBC,EAAKnB,EAAO0H,sBAAuB,CAAC,kBAEjG,OACCV,EAACW,cAAApB,EACA,CAAAhG,GAAIA,EACY,iBAAAP,EAAOO,GACvBqH,KAAK,OACLC,UAAWtG,EAAU,WACX4F,EAAOL,WACL9G,EAAO6B,MAAK6E,gBACP1G,EAAO8H,KACRxB,eAAAtG,EAAO+H,cACvBtC,OAAQzF,EAAOyF,OACfM,OAAQ/F,EAAO+F,OACfC,QAAShG,EAAOgG,QAChBN,KAAM1F,EAAO0F,KACbO,KAAMjG,EAAOiG,KACbN,SAAU3F,EAAO2F,SACjBC,SAAU5F,EAAO4F,SACjBE,MAAO9F,EAAO8F,MACdD,MAAO7F,EAAO6F,gBACH7F,EAAOgI,KAClBnG,MAAOC,EAAuC6E,YACjCS,EACbC,YAAaA,EACbC,WAAYA,EACZC,UAAWA,EACXC,YAAaA,EACbC,YAAaA,IAEXzH,EAAO8H,MAAQd,kCAAeG,GP7B7B,SACLhG,EAEA8G,EACAC,EACAhB,GAEA,OAAKe,EAKDC,GAA4B,mBAAXA,EACbA,EAAO/G,EAAK+F,GAGbe,EAAS9G,EAAK+F,GARb,IAST,COY6CiB,CAAYhH,EAAKnB,EAAOiI,SAAUjI,EAAOkI,OAAQhB,IAC1FlH,EAAO8H,MAAQ9H,EAAO8H,KAAK3G,EAAK+F,EAAUlH,EAAQO,GAGtD,ICxFA,MAAM6H,EAAuB,QA8D7B,IAAAC,EAAerB,EAAMC,MAxCrB,UAAkBqB,KACjBA,EAAIC,UACJA,EAAYH,EAAoBI,iBAChCA,EAAmB,CAAE3G,MAAO,CAAE,GAAE4G,cAChCA,GAAgB,EAAKC,QACrBA,GAAU,EAAKpE,SACfA,GAAW,EAAKqE,QAChBA,EAAU1H,IAEV,MAOM2H,EAAUL,EACVM,EAAYD,IAAYR,EAAuBI,EAAiB3G,MArC5C,CAACyC,GAAsBpE,OAAAC,OAAAD,OAAAC,OAAA,CACjD2I,SAAU,SACLxE,GAAY,CAAEyE,OAAQ,YAC3B,CAAAC,QAAS,EACTC,UAAW,MACXC,cAAe,SACfC,SAAU,aA+BoEC,CAAmB9E,GAC3F+E,EAA2BrC,EAAMsC,SACtC,aRmFDC,KACGpE,GAEH,IAAIqE,EAaJ,OAXAtJ,OAAOuJ,KAAKF,GACVxJ,KAAI2J,GAAKH,EAAOG,KAChBjI,SAAQ,CAAC2D,EAAOhG,KAChB,MAAMuK,EAAYJ,EAEG,mBAAVnE,IACVoE,EAAStJ,OAAAC,OAAAD,OAAAC,OAAA,CAAA,EAAQwJ,GAAW,CAAA,CAACzJ,OAAOuJ,KAAKF,GAAQnK,IAASgG,KAASD,KAEnE,IAGIqE,GAAaD,CACrB,CQpGQK,CAAoBpB,EAAkBC,IAC5C,CAACD,EAAkBC,IAGpB,OACCzB,gBAAC4B,EAAO1I,OAAAC,OAAA,CAEPyC,KAAK,WACLiH,IAlBsBC,IACnBA,IAEHA,EAASrB,cAAgBA,EACzB,EAeA5G,MAAOgH,EACPF,QAASrE,EAAWrD,EAAO0H,EAC3BL,KAAMA,EACM,aAAAA,EACZI,QAASA,EACTpE,SAAUA,GACN+E,EAAwB,CAC5BU,SAAU9I,IAGb,ICzDA,MAAM+I,EAAyB5F,EAAAA,QAAOiB,EAAS;;;;;;;EAsB/C,SAAS4E,GAAqB3B,KAC7BA,EAAI/I,SACJA,EAAQ4B,IACRA,EAAGV,SACHA,EAAQyJ,SACRA,EAAQC,wBACRA,EAAuBC,6BACvBA,EAA4BC,qBAC5BA,EAAoBC,sBACpBA,EAAqBC,cACrBA,IAEA,MAAMjG,KAAcgG,IAAyBA,EAAsBnJ,IAanE,OACC6F,EAACW,cAAAqC,GAAuBrB,QAAU6B,GAAwBA,EAAEC,kBAAmB5C,UAAU,gBAAetC,YAAA,GACvGyB,EAAAW,cAAC+C,EAAQ,CACRpC,KAAMA,EACNC,UAAW4B,EACX3B,iBAAkB4B,EAClB1B,QAASwB,EACK,eAAAA,EACdvB,QAnByB,KAC3B4B,EAAc,CACb3H,KAAM,oBACNzB,MACAiC,WAAY8G,EACZ3K,WACAkB,WACA4C,aAAcgH,GACb,EAYA/F,SAAUA,IAId,CC9DA,MAAMqG,EAAcvG,EAAAA,QAAOqB,MAAM;;;;;;;GAO9B,EAAGlB,WAAYA,EAAMqG,eAAe/I;EAYvC,SAASgJ,GAAkBvG,SAC1BA,GAAW,EAAKwG,SAChBA,GAAW,EAAKC,eAChBA,EAAcxK,GACdA,EAAEY,IACFA,EAAG6J,UACHA,IAEA,MAAMC,EAAOH,EAAWC,EAAeD,SAAWC,EAAeG,UAGjE,OACClE,EAACW,cAAAgD,EACe,CAAA,gBAAArG,EACfqE,QALmB,IAAMqC,GAAaA,EAAU7J,GAK3B,cACR,mBAAmBZ,IAChC+D,SAAUA,EACE,aAAAwG,EAAW,eAAiB,aACxClD,KAAK,SACLhF,KAAK,UAEJqI,EAGJ,CCzCA,MAAME,EAAoB/G,EAAAA,QAAOiB,EAAS;;;;GAIvC,EAAGd,WAAYA,EAAM6G,aAAavJ;EAYrC,SAASwJ,GAAgBlK,IACxBA,EAAG2J,SACHA,GAAW,EAAKC,eAChBA,EAAcxK,GACdA,EAAEyK,UACFA,EAAS1G,SACTA,GAAW,IAEX,OACC0C,EAAAW,cAACwD,EAAiB,CAACxC,QAAU6B,GAAwBA,EAAEC,kBAAiBlF,YAAA,GACvEyB,EAAAW,cAACkD,EAAc,CACdtK,GAAIA,EACJY,IAAKA,EACL2J,SAAUA,EACVC,eAAgBA,EAChBzG,SAAUA,EACV0G,UAAWA,IAIf,CCtCA,MAAMM,EAAmBlH,EAAAA,QAAOC,GAE9B;;;GAGC,EAAGE,WAAYA,EAAMgH,YAAY1J;GACjC,EAAG2J,uBAAwBA;EA6B9B,IAAAC,EAAezE,EAAMC,MAlBrB,UAAwByE,KACvBA,EAAIC,kBACJA,EAAiBC,uBACjBA,EAAsBC,iBACtBA,EAAgBC,mBAChBA,IAGA,MACMvK,EAAa,CAAC,qBADIuK,EAAmBC,MAAM,KAAK7I,QAAOb,GAAW,iBAANA,KACPN,KAAK,KAEhE,OACCiF,gBAACsE,EAAgB,CAACzD,UAAWtG,oBAA+BsK,GAC3D7E,EAACW,cAAAgE,EAAkBzL,OAAAC,OAAA,CAAAuL,KAAMA,GAAUE,IAGtC,ICrCO,MAAMI,EAAgB,iBAE7B,IAAYC,EAMAC,EAMAC,EAZAF,QAIXA,eAAA,GAJWA,EAAAA,oBAAAA,QAAAA,UAIX,CAAA,IAHA,IAAA,MACAA,EAAA,IAAA,MACAA,EAAA,KAAA,OAGWC,QAIXA,eAAA,GAJWA,EAAAA,oBAAAA,QAAAA,UAIX,CAAA,IAHA,KAAA,OACAA,EAAA,MAAA,QACAA,EAAA,OAAA,SAGWC,QAIXA,WAAA,GAJWA,EAAAA,QAAKA,QAALA,cAIX,CAAA,IAHA,GAAA,KACAA,EAAA,GAAA,KACAA,EAAA,GAAA,KCND,MAAMC,EAAelI,EAAGA,GAEtB;;IAEE,EAAGmI,oBAAmB9H,WAAY8H,GAAqB9H,EAAM1B,KAAKyJ;;EAIhEC,EAAarI,EAAGA,GAAA;;;;EAMhBsI,EAAgBpI,EAAM,QAACC,IAAImC,OAAMC,IAAU,CAChD5E,MAAO4E,EAAM5E,SAQZ;;;;;;GAMC,EAAG0C,WAAYA,EAAM1B,KAAKhB;GAC1B,EAAGkD,SAAQR,WAAYQ,GAAUR,EAAM1B,KAAKmC;GAC5C,EAAGyH,WAAUlI,WAAYkI,GAAYlI,EAAM1B,KAAK6J;GAChD,EAAGL,uBAAwBA,GAAqBD;GAChD,EAAGO,qBAAsBA,GAAmBJ;GAC5C,EAAGK,YAAWrI,WAAYqI,GAAarI,EAAM1B,KAAKgK;GAClD,EAAGC,uBAAwBA;EAmD9B,SAASC,GAAOjN,QACfA,EAAU,GAAEsB,qBACZA,EAAuB,GAAE4L,gBACzBA,GAAkB,EAAKC,wBACvBA,GAA0B,EAAKC,MAC/BA,GAAQ,EAAKnC,eACbA,EAAcoC,eACdA,GAAiB,EAAKC,wBACtBA,EAAuBC,6BACvBA,EAA4BC,2BAC5BA,EAA0BC,mBAC1BA,GAAqB,EAAKC,yBAC1BA,GAA2B,EAAKC,iBAChCA,GAAmB,EAAKlN,GACxBA,EAAEmN,mCACFA,EAAkCnO,SAClCA,EAAQoO,aACRA,EAAe1M,EAAI2M,mBACnBA,EAAqB3M,EAAI4M,gBACzBA,EAAkB5M,EAAI6M,gBACtBA,EAAkB7M,EAAI8M,mBACtBA,EAAqB9M,EAAIsJ,cACzBA,EAAgBtJ,EAAI+M,eACpBA,GAAiB,EAAK7M,IACtBA,EAAGV,SACHA,EAAQyG,SACRA,EAAQoD,sBACRA,EAAwB,KAAI2D,eAC5BA,GAAiB,EAAK9D,wBACtBA,EAAuBC,6BACvBA,EAA4B8D,wBAC5BA,GAA0B,EAAK7D,qBAC/BA,GAAuB,EAAKH,SAC5BA,EAAQiE,QACRA,GAAU,EAAKC,iBACfA,EAAgB/G,YAChBA,EAAWC,WACXA,EAAUC,UACVA,EAASC,YACTA,EAAWC,YACXA,IAEA,MAAOqD,EAAUuD,GAAerH,EAAMsH,SAAStB,GAE/ChG,EAAMuH,WAAU,KACfF,EAAYrB,EAAgB,GAC1B,CAACA,IAEJ,MAAMwB,EAAiBxH,EAAMyH,aAAY,KACxCJ,GAAavD,GACbiD,GAAoBjD,EAAU3J,EAAI,GAChC,CAAC2J,EAAUiD,EAAoB5M,IAE5BuN,GAAcV,GAAmBb,IAAmBI,GAAsBC,GAE1EmB,GAAiB3H,EAAMyH,aAC3BjE,IAEeA,EAAEoE,OAENC,aAAa,cAAgB7C,IACvC2B,EAAaxM,EAAKqJ,IAEbyC,GAA2BE,GAAkBI,GACjDiB,IAED,GAEF,CAACvB,EAAyBM,EAAoBJ,EAAgBqB,EAAgBb,EAAcxM,IAGvF2N,GAAuB9H,EAAMyH,aACjCjE,IACeA,EAAEoE,OAENC,aAAa,cAAgB7C,IACvC4B,EAAmBzM,EAAKqJ,IACnByC,GAA2BE,GAAkBK,GACjDgB,IAED,GAEF,CAACvB,EAAyBO,EAA0BL,EAAgBqB,EAAgBZ,EAAoBzM,IAGnG4N,GAAsB/H,EAAMyH,aAChCjE,IACAqD,EAAgB1M,EAAKqJ,EAAE,GAExB,CAACqD,EAAiB1M,IAGb6N,GAAsBhI,EAAMyH,aAChCjE,IACAsD,EAAgB3M,EAAKqJ,EAAE,GAExB,CAACsD,EAAiB3M,IAGb8N,GAAcnQ,EAAKqC,EAAiB5B,IACpCuC,iBAAEA,GAAgBP,WAAEA,IAAeL,EAAoBC,EAAKC,EAAsB,CAAC,iBACnF8N,GAAoBhB,GAA2BhE,EAC/CiF,GAAgBzB,EAAqC5L,GAAmB,GACxEsN,GAAYjB,GAAiBjH,EdItB,GAAM,EcFnB,OACCF,EAAAW,cAAAX,EAAAqI,SAAA,KACCrI,EAACW,cAAA6E,GACAjM,GAAI,OAAOA,IACXqH,KAAK,MACK6E,SAAA2C,qBACS3B,EAAgBd,iBACjBM,GAA2ByB,GACrC3J,OAAAmI,EACRvE,QAASgG,GACTW,cAAeR,GACfS,aAAcR,GACdS,aAAcR,GACdnH,UAAWtG,GAAUqL,UACVsC,GAAiBpC,kBACThL,IAElBmM,GACAjH,EAACW,cAAAsC,GACA3B,KAAM,cAAc2G,KACpB1P,SAAUA,EACV4B,IAAKA,EACLV,SAAUA,EACVyJ,SAAUA,EACVC,wBAAyBA,EACzBC,6BAA8BA,EAC9BE,sBAAuBA,EACvBD,qBAAsBA,EACtBE,cAAeA,IAIhB4C,IAAmBG,GACnBtG,EAACW,cAAA8H,EACA,CAAAlP,GAAI0O,GACJlE,eAAgBA,EAChBD,SAAUA,EACV3J,IAAKA,EACL6J,UAAWwD,EACXlK,SAAU2I,IAIXnN,EAAQC,KAAIC,GACRA,EAAO0P,KACH,KAIP1I,EAACW,cAAAZ,GACAxG,GAAI,QAAQP,EAAOO,MAAM0O,KACzBjQ,IAAK,QAAQgB,EAAOO,MAAM0O,KAE1B9H,QAASnH,EAAO2P,gBAAkB3P,EAAOyF,OAAS,KAAOuG,EACzDhM,OAAQA,EACRmB,IAAKA,EACL+F,SAAUA,EACVE,WAAY9E,EAAW8L,EAAkBpO,EAAOO,IAChD8G,YAAaA,EACbC,WAAYA,EACZC,UAAWA,EACXC,YAAaA,EACbC,YAAaA,OAMhB0F,GAAkBrC,GAClB9D,EAAAW,cAACiI,EACA,CAAA5Q,IAAK,YAAYiQ,KACjBvD,KAAMvK,EACN0K,iBAAkBsD,GAClBrD,mBAAoBvK,GACpBoK,kBAAmByB,EACnBxB,uBAAwByB,IAK7B,CCtRA,MAAMwC,EAAOzL,EAAAA,QAAO0L,IAGlB;;;;;GAKC,EAAGC,iBAAmBA,EAAc,aAAe;GACnD,EAAGC,oBAAwC,SAAnBA,GAA6B;EAQlDC,EAAgD,EAAGC,aAAY1M,mBACpEwD,EAAA,QAAAW,cAACkI,EAAkB,CAAAE,YAAAG,iBAA4B1M,GAAa,KCNvD2M,GAAe/L,EAAAA,QAAOoB,EAA+B;GACxD,EAAGC,YAAaA,GAAU;GAC1B,EAAGlB,QAAOoC,iBAAkBA,GAAepC,EAAM6L,UAAUvJ;EAQxDwJ,GAAcnM,EAAGA,GAAqB;;;;;;KAMvC,EAAG6L,iBAAmBA,EAAc,aAAe;;;;;;;;;;;;;;;;;GAiBrD,EAAGA,kBACHA,GACD7L,EAAAA,GAAG;;;;;;;;;;;EAaCoM,GAAiBlM,EAAAA,QAAOC,GAAwB;;;;;;;;;GASnD,EAAGC,eAAgBA,GAAY+L;EAG5BE,GAAanM,EAAAA,QAAOC,GAAG;;;;EAkK7B,IAAAmM,GAAexJ,EAAMC,MAxIrB,UAAqBjH,OACpBA,EAAMsE,SACNA,EAAQ8J,iBACRA,EAAgB3K,eAChBA,EAAiB,CAAE,EAAAD,cACnBA,EAAaiN,SACbA,EAAQC,WACRA,EAAUC,WACVA,EAAU9M,iBACVA,EAAgB+M,sBAChBA,EAAqBC,0BACrBA,EAAyBC,OACzBA,EAAMzJ,YACNA,EAAWC,WACXA,EAAUC,UACVA,EAASC,YACTA,EAAWC,YACXA,IAEAT,EAAMuH,WAAU,KACgB,iBAApBvO,EAAOiI,UACjB8I,QAAQC,MACP,YAAYhR,EAAOiI,sKAEpB,GAEC,IAEH,MAAOgJ,EAAaC,GAAkBlK,EAAMsH,UAAS,GAC/C6C,EAAYnK,EAAMoK,OAA8B,MAQtD,GANApK,EAAMuH,WAAU,KACX4C,EAAUE,SACbH,EAAeC,EAAUE,QAAQC,YAAcH,EAAUE,QAAQE,YACjE,GACC,CAACN,IAEAjR,EAAO0P,KACV,OAAO,KAGR,MAAM8B,EAAmB,KACxB,IAAKxR,EAAOI,WAAaJ,EAAOiI,SAC/B,OAGD,IAAIwJ,EAAYjO,EAEZlB,EAAWmB,EAAelD,GAAIP,EAAOO,MACxCkR,EAAYjO,IAAkB3E,EAAU6S,IAAM7S,EAAU8S,KAAO9S,EAAU6S,KAG1EZ,EAAO,CACNlO,KAAM,cACNY,cAAeiO,EACfhO,eAAgBzD,EAChB0D,oBACEiN,GAAc9M,IAAqB+M,GAA0BF,GAAcG,GAC5E,EASGe,EAAwB1B,GAC7BlJ,EAACW,cAAAsI,GAAeC,WAAYA,EAAY1M,cAAeA,IAGlDqO,EAAuB,IAC5B7K,EAAAW,cAAA,OAAA,CAAME,UAAW,CAACrE,EAAe,4BAA4BzB,KAAK,MAAO0O,GAGpEP,KAAgBlQ,EAAOI,WAAYkC,EAAWmB,EAAelD,GAAIP,EAAOO,KACxEuR,GAAe9R,EAAOI,UAAYkE,EAClCyN,EAAqB/R,EAAOI,WAAaqQ,IAAazQ,EAAO8F,MAC7DkM,EAAsBhS,EAAOI,WAAaqQ,GAAYzQ,EAAO8F,MAC7DmM,EAAqBjS,EAAOI,UAAYqQ,IAAazQ,EAAO8F,MAC5DoM,EAAsBlS,EAAOI,UAAYqQ,GAAYzQ,EAAO8F,MAElE,OACCkB,EAACW,cAAAwI,GACgB,CAAA,iBAAAnQ,EAAOO,GACvBsH,UAAU,eAEVvC,WAAA,EAAAyC,cAAe/H,EAAO+H,cACtBtC,OAAQzF,EAAOyF,OACfO,QAAShG,EAAOgG,QAChBN,KAAM1F,EAAO0F,KACbO,KAAMjG,EAAOiG,KACbN,SAAU3F,EAAO2F,SACjBC,SAAU5F,EAAO4F,SACjBE,MAAO9F,EAAO8F,MACdC,OAAQ/F,EAAO+F,OACfF,MAAO7F,EAAO6F,MACdsM,UAAWnS,EAAOoS,QAAOzL,YACZrE,EAAWtC,EAAOO,GAAI6N,GACnC/G,YAAaA,EACbC,WAAYA,EACZC,UAAWA,EACXC,YAAaA,EACbC,YAAaA,GAEZzH,EAAOsI,MACPtB,EAAAW,cAAC2I,GACgB,CAAA,iBAAAtQ,EAAOO,kBACTP,EAAOO,GACrBqH,KAAK,eACLyK,SAAU,EACVxK,UAAU,wBACVc,QAAUmJ,OAAiCxR,EAAnBkR,EACxBc,WAAaR,OAA+BxR,EApDxBiS,IACL,UAAdA,EAAMvT,KACTwS,GACA,EAiDuDzB,aACvC+B,GAAe5B,EAC7B5L,SAAUwN,IAERA,GAAeI,GAAuBL,KACtCC,GAAeE,GAAuBJ,EAAqB1B,GAErC,iBAAhBlQ,EAAOsI,KACdtB,EAAAW,cAAC4I,GAAW,CAAAiC,MAAOvB,EAAcjR,EAAOsI,UAAOhI,EAAWuJ,IAAKsH,mBAA2BnR,EAAOO,IAC/FP,EAAOsI,MAGTtI,EAAW,MAGV8R,GAAeG,GAAsBJ,KACrCC,GAAeC,GAAsBH,EAAqB1B,IAKjE,ICtOA,MAAMuC,GAAcrO,EAAAA,QAAOiB,EAAS;;;;;;;EAsBpC,SAASqN,IAAkBC,SAC1BA,GAAW,EAAIC,QACfA,EAAOrT,SACPA,EAAQyD,YACRA,EAAWF,gBACXA,EAAeb,aACfA,EAAYkI,wBACZA,EAAuBC,6BACvBA,EAA4BE,sBAC5BA,EAAqBuI,gBACrBA,IAEA,MAAMpK,EAAgBxG,EAAaT,OAAS,IAAMwB,EAC5CH,EAAOyH,EAAwBsI,EAAQ1P,QAAQ/B,IAAYmJ,EAAsBnJ,KAAQyR,EACzFE,EAA6B,IAAhBjQ,EAAKrB,OAElBf,EAAWE,KAAKK,IAAI4R,EAAQpR,OAAQqB,EAAKrB,QAY/C,OACCwF,gBAACyL,GAAW,CAAC5K,UAAU,yBAA0B8K,EAAQpN,YAAA,GACxDyB,EAAAW,cAAC+C,EAAQ,CACRpC,KAAK,kBACLC,UAAW4B,EACX3B,iBAAkB4B,EAClBzB,QAhBqB,KACvBkK,EAAgB,CACfjQ,KAAM,kBACNC,OACApC,WACAqC,kBACAvD,YACC,EAUAmJ,QAAS1F,EACTyF,cAAeA,EACfnE,SAAUwO,IAId,CClEA,SAASC,GAAOtB,EAAuBxF,QAASA,UAAC+G,MAChD,MAAMC,EAA6B,iBAAXC,QAEjBC,EAAOC,GAAYpM,EAAMsH,UAAS,GAqBzC,OAnBAtH,EAAMuH,WAAU,KACf,GAAK0E,EAIL,GAAkB,SAAdxB,EAWJ2B,EAAuB,QAAd3B,OAXT,CACC,MAAM4B,KAAYH,OAAOI,WAAYJ,OAAOI,SAAS3L,eAC/C4L,EAA6BD,SAASE,qBAAqB,QAAQ,GACnEC,EAA6BH,SAASE,qBAAqB,QAAQ,GACnEE,EAAyB,QAAhBH,EAAQI,KAAiC,QAAhBF,EAAQE,IAEhDP,EAASC,GAAUK,EAGnB,CAE4B,GAC3B,CAACjC,EAAWwB,IAERE,CACR,CCtBA,MAAMS,GAAQxP,EAAAA,QAAOC,GAAG;;;;;UAKd,EAAGE,WAAYA,EAAMsP,YAAYC;cAC7B,EAAGvP,WAAYA,EAAMsP,YAAY/K;;EAIzCiL,GAAiB3P,EAAAA,QAAOC,GAAG;;;;;EAO3B2P,GAAmB5P,EAAAA,QAAOC,GAG9B;;;;;;;;;;;GAWC,EAAG4P,UAAWA,GAAQ;GACtB,EAAG1P,WAAYA,EAAMsP,YAAYhS;GACjC,EAAG0C,QAAO2P,cAAeA,GAAY3P,EAAMsP,YAAYM;EA0B1D,SAASC,IAAYC,eACpBA,EAAcC,eACdA,EAAcC,iBACdA,EAAgBpR,cAChBA,EAAasO,UACbA,IAEA,MAAM0B,EAAQJ,GAAOtB,GACf+C,EAAUrR,EAAgB,EAEhC,OAAIoR,EAEFvN,EAACW,cAAAqM,aAA2BQ,GAC1BxN,EAAMyN,aAAaF,EAAwC,CAAEpR,mBAMhE6D,EAACW,cAAAqM,GAA2B,CAAAE,SAAAM,OAAerB,GAC1CnM,EAACW,cAAAiM,GAAO,KA3CyB,EAACS,EAAgClR,EAAuBuR,KAC3F,GAAsB,IAAlBvR,EACH,OAAO,KAGR,MAAMwR,EAA8B,IAAlBxR,EAAsBkR,EAAeO,SAAWP,EAAeQ,OAGjF,OAAIH,EACI,GAAGvR,KAAiBkR,EAAeS,SAAW,MAAMH,IAGrD,GAAGxR,KAAiBwR,KAAaN,EAAeS,SAAW,IAAI,EA+B5DC,CAA4BV,EAAgBlR,EAAegQ,IACnEnM,EAAAW,cAACoM,GAAgB,KAAAO,GAGpB,CCnFA,MAAMU,GAAc5Q,EAAAA,QAAOC,GAAG;;;;;;;;;;GAU3B,EAAGE,WAAYA,EAAM0Q,OAAOpT;EAGzB+R,GAAQxP,EAAAA,QAAOC,GAAG;;UAEd,EAAGE,WAAYA,EAAM0Q,OAAOnB;cACxB,EAAGvP,WAAYA,EAAM0Q,OAAOnM;;EAIpCoM,GAAU9Q,EAAAA,QAAOC,GAAG;;;;;;;;;EAsBpB8Q,GAAS,EACd3C,QACA4C,UAAU,KACVf,iBACAC,iBACAC,mBACApR,gBACAsO,YACA4D,YAAW,KAEXrO,EAACW,cAAAqN,GAAY,CAAAnN,UAAU,kBAAkBD,KAAK,uBAAsB,GACnEZ,EAACW,cAAAiM,GAAO,KAAApB,GACP4C,GAAWpO,EAAAW,cAACuN,GAAO,KAAEE,GAErBC,GACArO,EAACW,cAAAyM,GACA,CAAAC,eAAgBA,EAChBC,eAAgBA,EAChBC,iBAAkBA,EAClB9C,UAAWA,EACXtO,cAAeA,qYCjEnB,MAAMmS,GAAW,CAChBC,KAAM,aACNzP,MAAO,WACPC,OAAQ,UAKHyP,GAAmBpR,EAAAA,QAAO6Q,MAG9B;;;;;;;;oBAQkB,EAAGQ,WAAYH,GAASG;cAC9B,EAAGC,kBAAoBA,EAAe,OAAS;GAC1D,EAAGnR,WAAYA,EAAMoR,UAAU9T;EAS5B+T,GAAaC,IAAA,IAAAJ,MAAEA,EAAQ,QAAOK,YAAEA,GAAc,GAAID,EAAKE,EAA1CC,GAAAH,EAAA,CAAA,QAAA,gBAAkF,OACpG7O,EAAAW,cAAC6N,GAAgBtV,OAAAC,OAAA,CAACsV,MAAOA,EAAKC,aAAgBI,GAAiBC,GAC/D,ECjCKE,GAAO7R,EAAAA,QAAOC,GAAG;;;ECMjB6R,GAAoB9R,EAAAA,QAAOC,GAI/B;;;;GAIC,EAAG8R,cAAaxR,kBACjBwR,GACAjS,EAAAA,GAAG;;;;iBAIYS,EAAe,OAAS;;;;GAItC,EAAGA,gBAAe,EAAOyR,2BAA2B,WACrDzR,GACAT,EAAAA,GAAG;iBACYkS;;;;GAId,EAAG7R,WAAYA,EAAM8R,kBAAkBxU;EC/BpCyU,GAAkBlS,EAAAA,QAAOC,GAAG;;;;;GAK/BoC,GAASA,EAAMlC,MAAMgS,SAAS1U;ECL3B2U,GAAUpS,EAAAA,QAAOC,GAAG;;;GAGvB,EAAGE,WAAYA,EAAMkS,aAAa5U;ECF/B6U,GAAiBtS,EAAAA,QAAOiB,EAAS;;GAEpC,EAAGd,WAAYA,EAAM6G,aAAavJ;ECH/B8U,GAAgBvS,EAAAA,QAAOC,GAAG;;;;GAI7B,EAAGE,WAAYA,EAAMqS,OAAO/U;ECJzBgV,GAAyB,IAC9B7P,EAAA,QAAAW,cAAA,MAAA,CAAKmP,MAAM,6BAA6BjR,MAAM,KAAKkR,OAAO,KAAKC,QAAQ,aACtEhQ,EAAAA,QAAMW,cAAA,OAAA,CAAAsP,EAAE,mBACRjQ,UAAMW,cAAA,OAAA,CAAAsP,EAAE,gBAAgBC,KAAK,UCDzBC,GAAgB/S,EAAAA,QAAOgT,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6B7BC,GAAgBjT,EAAAA,QAAOC,GAAG;;;;;;;;;;;;;;;;;;;EA2B1BiT,GAAUzB,QAAA0B,aAAEA,EAAYxN,SAAEA,GAAQ8L,EAAKE,EAAIC,GAAAH,EAAjC,6BAAkE,OACjF7O,gBAACqQ,GAAa,KACbrQ,EAACW,cAAAwP,GAAcjX,OAAAC,OAAA,CAAA4J,SAAUA,EAAUwN,aAAcA,GAAkBxB,IACnE/O,EAAAW,cAAC6P,GAAY,MAEd,ECvDYC,GAAe,CAC3B3X,QAAS,GACT4L,KAAM,GACN8G,MAAO,GACPjT,SAAU,KACV0O,gBAAgB,EAChBC,yBAAyB,EACzBwJ,2BAA2B,EAC3BC,sBAAuB,KACvBrN,sBAAuB,KACvBH,wBAAyB,QACzBC,6BAA8B,CAAE,EAChCyG,2BAA2B,EAC3BxG,sBAAsB,EACtBuN,mBAAmB,EACnBzK,gBAAgB,EAChB0K,sBAAuB,KACvBC,sBAAuB,KACvBvK,oBAAoB,EACpBD,4BAA4B,EAC5BE,0BAA0B,EAC1BE,oCAAoC,EACpCN,wBAAyB,WACxB,OACCpG,EAAA,QAAAW,cAAA,MAAA,4DACqDX,EAAAA,QAAwCW,cAAA,SAAA,KAAA,2BAEvF,wDAEP,EACDoD,eAAgB,CACfG,UAAWlE,EAAC,QAAAW,eCvC0B,IACvCX,EAAAA,QAAKW,cAAA,MAAA,CAAAuP,KAAK,eAAeH,OAAO,KAAKC,QAAQ,YAAYnR,MAAM,KAAKiR,MAAM,8BACzE9P,EAAAA,QAAMW,cAAA,OAAA,CAAAsP,EAAE,qDACRjQ,UAAMW,cAAA,OAAA,CAAAsP,EAAE,kBAAkBC,KAAK,WDoCK,MACpCpM,SAAU9D,EAAC,QAAAW,eExC0B,IACtCX,EAAAA,QAAKW,cAAA,MAAA,CAAAuP,KAAK,eAAeH,OAAO,KAAKC,QAAQ,YAAYnR,MAAM,KAAKiR,MAAM,8BACzE9P,EAAAA,QAAMW,cAAA,OAAA,CAAAsP,EAAE,oDACRjQ,UAAMW,cAAA,OAAA,CAAAsP,EAAE,kBAAkBC,KAAK,WFqCG,OAEnC7J,6BAA8B,CAAE,EAChC0K,iBAAiB,EACjBC,kBAAmBhR,EAAAA,QAAKW,cAAA,MAAA,CAAA9F,MAAO,CAAEiH,SAAU,OAAQmP,WAAY,IAAKjP,QAAS,SAA0B,cACvGkP,kBAAkB,EAClBzH,SAAU,KACVpQ,aAAc,KACdqQ,YAAY,EACZvC,SAAS,EACTV,kBAAkB,EAClBO,gBAAgB,EAChBmK,eAAe,EACf9D,eAAgB,CAAEO,SAAU,OAAQC,OAAQ,QAASC,QAAS,YAC9DM,QAAS,KACTd,eAAgB,KAChBC,iBAAkB,KAClB6D,mBAAoB,KACpBC,gBAAgB,EAChBC,YAAY,EACZC,gBAAiBvR,EAAAA,6BAAKnF,MAAO,CAAEmH,QAAS,SAA+C,mCACvF1E,UAAU,EACVkU,aAAa,EACbC,UAAU,EACV9C,WAAW,EACX+C,eAAgBxM,QAASA,UAACyM,MAC1BC,eAAe,EACfC,mBAAoB,KACpBC,aAAa,EACbC,wBAAyB,QACzBpI,YAAY,EACZ9M,kBAAkB,EAClBmV,wBAAyB,CACxBpI,uBAAuB,EACvB7M,6BAA6B,GAE9BkV,sBAAuB,EACvBC,4BAA4B,EAC5BC,oBAAqB,EACrBC,kBAAmB,GACnBC,6BAA8B,CAAC,GAAI,GAAI,GAAI,GAAI,IAC/CC,oBAAqB,KACrBC,2BAA4B,CAAE,EAC9BC,wBAAyBxS,EAAC,QAAAW,eGnFC,IAC3BX,EAAAA,QAAAW,cAAA,MAAA,CACCmP,MAAM,6BACNjR,MAAM,KACNkR,OAAO,KACPC,QAAQ,YAAW,cACP,OACZpP,KAAK,gBAELZ,EAAAA,QAAMW,cAAA,OAAA,CAAAsP,EAAE,+DACRjQ,UAAMW,cAAA,OAAA,CAAAuP,KAAK,OAAOD,EAAE,wBHyEqB,MAC1CwC,uBAAwBzS,EAAC,QAAAW,eIpFC,IAC1BX,EAAAA,QAAAW,cAAA,MAAA,CACCmP,MAAM,6BACNjR,MAAM,KACNkR,OAAO,KACPC,QAAQ,YAAW,cACP,OACZpP,KAAK,gBAELZ,EAAAA,QAAMW,cAAA,OAAA,CAAAsP,EAAE,+DACRjQ,UAAMW,cAAA,OAAA,CAAAuP,KAAK,OAAOD,EAAE,sBJ0EmB,MACxCyC,mBAAoB1S,EAAC,QAAAW,eKrFE,IACvBX,EAAAA,QAAAW,cAAA,MAAA,CACCmP,MAAM,6BACNjR,MAAM,KACNkR,OAAO,KACPC,QAAQ,YAAW,cACP,OACZpP,KAAK,gBAELZ,EAAAA,QAAMW,cAAA,OAAA,CAAAsP,EAAE,mDACRjQ,UAAMW,cAAA,OAAA,CAAAsP,EAAE,gBAAgBC,KAAK,WL2EG,MACjCyC,uBAAwB3S,EAAC,QAAAW,eMtFH,IACtBX,EAAAA,QAAAW,cAAA,MAAA,CACCmP,MAAM,6BACNjR,MAAM,KACNkR,OAAO,KACPC,QAAQ,YAAW,cACP,OACZpP,KAAK,gBAELZ,EAAAA,QAAMW,cAAA,OAAA,CAAAsP,EAAE,kDACRjQ,UAAMW,cAAA,OAAA,CAAAsP,EAAE,gBAAgBC,KAAK,WN4EM,MACpChK,OAAO,EACP9L,qBAAsB,GACtBmD,MAAO,UACPqV,aAAc,CAAE,EAChBnI,UAAWxF,QAASA,UAAC+G,KACrB6G,aAAc5Y,EACd6Y,oBAAqB7Y,EACrB0M,aAAc1M,EACd2M,mBAAoB3M,EACpB4M,gBAAiB5M,EACjB6M,gBAAiB7M,EACjB8M,mBAAoB9M,EACpB8Y,qBAAsB9Y,EACtB6P,OAAQ7P,EACR+Y,oBAAqB/Y,GO5FhBgZ,GAA0B,CAC/BC,gBAAiB,iBACjBC,mBAAoB,KACpBC,eAAe,EACfC,mBAAmB,EACnBC,sBAAuB,OAGlBC,GAAoBnW,EAAAA,QAAOoW,GAAG;;;;;;;;;GASjC,EAAGjW,WAAYA,EAAMoM,WAAW9O;EAG7B4Y,GAASrW,EAAAA,QAAOqB,MAEpB;;;;;GAKC,EAAGlB,WAAYA,EAAMoM,WAAW+J;GAChC,EAAGC,YAAaA,GAAU;EAGvBC,GAAWxW,EAAAA,QAAOC,GAAG;;;;;GAKxBY,CAAQ;;;;EAML4V,GAAOzW,EAAAA,QAAO0L,IAAI;;;EAKlBgL,GAAQ1W,EAAAA,QAAOyW,GAAK;;EAIpBE,GAAW3W,EAAAA,QAAOyW,GAAK;;EAgJ7B,IAAAG,GAAehU,EAAMC,MA7HrB,UAAoBvG,YACnBA,EAAWD,SACXA,EAAQkD,YACRA,EAAW8N,UACXA,EAAYgG,GAAahG,UAAS4H,6BAClCA,EAA+B5B,GAAa4B,6BAA4BI,uBACxEA,EAAyBhC,GAAagC,uBAAsBD,wBAC5DA,EAA0B/B,GAAa+B,wBAAuBE,mBAC9DA,EAAqBjC,GAAaiC,mBAAkBC,uBACpDA,EAAyBlC,GAAakC,uBAAsBJ,2BAC5DA,EAA6B9B,GAAa8B,2BAA0BO,oBACpEA,EAAsBrC,GAAaqC,oBAAmBD,aACtDA,EAAepC,GAAaoC,eAE5B,MAAMoB,ECvFqB,MAC3B,MAAMhI,EAA6B,iBAAXC,OAExB,SAASgI,IACR,MAAO,CACNrV,MAAOoN,EAAWC,OAAOiI,gBAAa7a,EACtCyW,OAAQ9D,EAAWC,OAAOkI,iBAAc9a,EAEzC,CAED,MAAO2a,EAAYI,GAAiBrU,EAAMsH,SAAS4M,GAgBnD,OAdAlU,EAAMuH,WAAU,KACf,IAAK0E,EACJ,MAAO,IAAM,KAGd,SAASqI,IACRD,EAAcH,IACd,CAGD,OADAhI,OAAOqI,iBAAiB,SAAUD,GAC3B,IAAMpI,OAAOsI,oBAAoB,SAAUF,EAAa,GAE7D,IAEIL,CAAU,ED6DEQ,GACbtI,EAAQJ,GAAOtB,GACfiK,EAAaT,EAAWpV,OAASoV,EAAWpV,MhC/F9B,IgCiGd8V,EAAWnb,EAAiBC,EAAUC,GACtCkb,EAAYjY,EAAcjD,EAC1Bmb,EAAaD,EAAYlb,EAAc,EACvCob,EAAiC,IAAhBnY,EACjBoY,EAAkBpY,IAAgBgY,EAClCK,EAAe9b,OAAAC,OAAAD,OAAAC,OAAA,GAAA8Z,IAA4BV,GAC3C0C,EACLtY,IAAgBgY,EACb,GAAGE,KAAcpb,KAAYub,EAAQ7B,sBAAsB1Z,IAC3D,GAAGob,KAAcD,KAAaI,EAAQ7B,sBAAsB1Z,IAE1Dyb,EAAiBlV,EAAMyH,aAAY,IAAMoL,EAAalW,EAAc,IAAI,CAACA,EAAakW,IACtFsC,EAAanV,EAAMyH,aAAY,IAAMoL,EAAalW,EAAc,IAAI,CAACA,EAAakW,IAClFuC,EAAcpV,EAAMyH,aAAY,IAAMoL,EAAa,IAAI,CAACA,IACxDwC,EAAarV,EAAMyH,aACxB,IAAMoL,EAAarZ,EAAiBC,EAAUC,KAC9C,CAACmZ,EAAcpZ,EAAUC,IAEpB4b,EAAoBtV,EAAMyH,aAC9BjE,GAA4CsP,EAAoB5T,OAAOsE,EAAEoE,OAAOxJ,OAAQzB,IACzF,CAACA,EAAamW,IAGTyC,EAAgBlD,EAA6BtZ,KAAKyc,GACvDxV,0BAAQhI,IAAKwd,EAAKpX,MAAOoX,GACvBA,KAICR,EAAQ3B,mBACXkC,EAAcE,KACbzV,0BAAQhI,KAAM,EAAGoG,MAAO3E,GACtBub,EAAQ1B,wBAKZ,MAAMlD,EACLpQ,EAAAW,cAAC2P,GAAO,CAAAvN,SAAUuS,EAAmB/E,aAAc7W,eAAyBsb,EAAQ9B,iBAClFqC,GAIH,OACCvV,EAACW,cAAA4S,GAAkB,CAAA1S,UAAU,mBAC1BmU,EAAQ5B,eAAiBsB,GAC1B1U,EAAAW,cAAAX,EAAAqI,SAAA,KACCrI,EAAAW,cAACoT,GAAQ,KAAEiB,EAAQ9B,iBAClB9C,GAGFsE,GAAc1U,EAAAW,cAACmT,GAAK,KAAEmB,GACvBjV,EAAAW,cAACiT,GAAQ,KACR5T,EAACW,cAAA8S,GACA,CAAAla,GAAG,wBACHqC,KAAK,SAAQ,aACF,aAAY,gBACRkZ,EACfnT,QAASyT,EACT9X,SAAUwX,EACFnB,OAAAxH,GAEPqG,GAGFxS,EAACW,cAAA8S,GACA,CAAAla,GAAG,2BACHqC,KAAK,SAAQ,aACF,gBAAe,gBACXkZ,EACfnT,QAASuT,EACT5X,SAAUwX,EACFnB,OAAAxH,GAEPwG,IAGAqC,EAAQ5B,gBAAkBsB,GAActE,EAE1CpQ,EAACW,cAAA8S,GACA,CAAAla,GAAG,uBACHqC,KAAK,SAAQ,aACF,YAAW,gBACPmZ,EACfpT,QAASwT,EACT7X,SAAUyX,EACFpB,OAAAxH,GAEPuG,GAGF1S,EAAAW,cAAC8S,GAAM,CACNla,GAAG,uBACHqC,KAAK,SAAQ,aACF,YAAW,gBACPmZ,EACfpT,QAAS0T,EACT/X,SAAUyX,EAAepB,OACjBxH,GAEPsG,IAKN,IExMA,MAAMiD,GAAuB,CAACC,EAAIC,KACjC,MAAMC,EAAc7V,EAAMoK,QAAO,GAEjCpK,EAAMuH,WAAU,KACXsO,EAAYxL,QACfwL,EAAYxL,SAAU,EAIvBsL,GAAI,GAEFC,EAAO,uGCbX,IAAIE,GAAoB,SAA2B1X,GAClD,OAID,SAAyBA,GACxB,QAASA,GAA0B,iBAAVA,CAC1B,CANQ2X,CAAgB3X,KAQxB,SAAmBA,GAClB,IAAI4X,EAAc9c,OAAO+c,UAAUC,SAASC,KAAK/X,GAEjD,MAAuB,oBAAhB4X,GACa,kBAAhBA,GAQL,SAAwB5X,GACvB,OAAOA,EAAMgY,WAAaC,EAC3B,CATKC,CAAelY,EACpB,CAbMmY,CAAUnY,EAChB,EAeA,IACIiY,GADiC,mBAAXG,QAAyBA,OAAOC,IAClBD,OAAOC,IAAI,iBAAmB,MAUtE,SAASC,GAA8BtY,EAAO4W,GAC7C,OAA0B,IAAlBA,EAAQ2B,OAAmB3B,EAAQc,kBAAkB1X,GAC1DwY,IANiBC,EAMKzY,EALlB0Y,MAAMC,QAAQF,GAAO,GAAK,CAAE,GAKFzY,EAAO4W,GACrC5W,EAPJ,IAAqByY,CAQrB,CAEA,SAASG,GAAkBpP,EAAQqP,EAAQjC,GAC1C,OAAOpN,EAAOsP,OAAOD,GAAQle,KAAI,SAASoe,GACzC,OAAOT,GAA8BS,EAASnC,EAChD,GACA,CAkBA,SAASoC,GAAQxP,GAChB,OAAO1O,OAAOuJ,KAAKmF,GAAQsP,OAT5B,SAAyCtP,GACxC,OAAO1O,OAAOme,sBACXne,OAAOme,sBAAsBzP,GAAQ1L,QAAO,SAASob,GACtD,OAAOpe,OAAOqe,qBAAqBpB,KAAKvO,EAAQ0P,EACnD,IACI,EACJ,CAGmCE,CAAgC5P,GACnE,CAEA,SAAS6P,GAAmBlV,EAAQmV,GACnC,IACC,OAAOA,KAAYnV,CACnB,CAAC,MAAMoV,GACP,OAAO,CACP,CACF,CASA,SAASC,GAAYhQ,EAAQqP,EAAQjC,GACpC,IAAI6C,EAAc,CAAA,EAiBlB,OAhBI7C,EAAQc,kBAAkBlO,IAC7BwP,GAAQxP,GAAQnN,SAAQ,SAASzC,GAChC6f,EAAY7f,GAAO0e,GAA8B9O,EAAO5P,GAAMgd,EACjE,IAECoC,GAAQH,GAAQxc,SAAQ,SAASzC,IAblC,SAA0B4P,EAAQ5P,GACjC,OAAOyf,GAAmB7P,EAAQ5P,MAC5BkB,OAAO4e,eAAe3B,KAAKvO,EAAQ5P,IACpCkB,OAAOqe,qBAAqBpB,KAAKvO,EAAQ5P,GAC/C,EAUM+f,CAAiBnQ,EAAQ5P,KAIzByf,GAAmB7P,EAAQ5P,IAAQgd,EAAQc,kBAAkBmB,EAAOjf,IACvE6f,EAAY7f,GAhDf,SAA0BA,EAAKgd,GAC9B,IAAKA,EAAQgD,YACZ,OAAOpB,GAER,IAAIoB,EAAchD,EAAQgD,YAAYhgB,GACtC,MAA8B,mBAAhBggB,EAA6BA,EAAcpB,EAC1D,CA0CsBqB,CAAiBjgB,EAAKgd,EAAtBiD,CAA+BrQ,EAAO5P,GAAMif,EAAOjf,GAAMgd,GAE5E6C,EAAY7f,GAAO0e,GAA8BO,EAAOjf,GAAMgd,GAEjE,IACQ6C,CACR,CAEA,SAASjB,GAAUhP,EAAQqP,EAAQjC,IAClCA,EAAUA,GAAW,IACbkD,WAAalD,EAAQkD,YAAclB,GAC3ChC,EAAQc,kBAAoBd,EAAQc,mBAAqBA,GAGzDd,EAAQ0B,8BAAgCA,GAExC,IAAIyB,EAAgBrB,MAAMC,QAAQE,GAIlC,OAFgCkB,IADZrB,MAAMC,QAAQnP,GAKvBuQ,EACHnD,EAAQkD,WAAWtQ,EAAQqP,EAAQjC,GAEnC4C,GAAYhQ,EAAQqP,EAAQjC,GAJ5B0B,GAA8BO,EAAQjC,EAM/C,CAEA4B,GAAUwB,IAAM,SAAsBlgB,EAAO8c,GAC5C,IAAK8B,MAAMC,QAAQ7e,GAClB,MAAM,IAAI0C,MAAM,qCAGjB,OAAO1C,EAAMmgB,QAAO,SAASC,EAAMC,GAClC,OAAO3B,GAAU0B,EAAMC,EAAMvD,EAC7B,GAAE,GACJ,EAEA,UAAkB4B,IC3HlB,MAAM4B,GAAe,CACpBC,KAAM,CACLC,QAAS,sBACTC,UAAW,sBACXrb,SAAU,uBAEXsb,WAAY,CACXC,QAAS,WAEVC,QAAS,CACRF,WAAY,UACZH,KAAM,uBAEPM,QAAS,CACRF,QAAS,mBAEVpa,OAAQ,CACPoa,QAAS,kBACTG,MAAO,kBACPC,MAAO,kBACP3b,SAAU,sBAEX4F,SAAU,CACT2V,QAAS,UACTJ,KAAM,uBAEPhS,iBAAkB,CACjBoS,QAAS,UACTJ,KAAM,uBAEPtR,QAAS,CACR0R,QAAS,UACTJ,KAAM,wBAIKS,GAA8B,CAC1CL,QAASL,GACTW,MAAOX,GACPY,KAAM,CACLX,KAAM,CACLC,QAAS,UACTC,UAAW,2BACXrb,SAAU,mBAEXsb,WAAY,CACXC,QAAS,WAEVC,QAAS,CACRF,WAAY,UACZH,KAAM,WAEPM,QAAS,CACRF,QAAS,uBAEVpa,OAAQ,CACPoa,QAAS,UACTG,MAAO,2BACPC,MAAO,2BACP3b,SAAU,4BAEX4F,SAAU,CACT2V,QAAS,oBACTJ,KAAM,WAEPhS,iBAAkB,CACjBoS,QAAS,oBACTJ,KAAM,WAEPtR,QAAS,CACR0R,QAAS,qBACTJ,KAAM,aC7DT,SAASY,GACRvgB,EACAka,EACA5B,EACAC,GAEA,MAAOiI,EAAcC,GAAmBvZ,EAAMsH,UAA2B,IAAMzO,EAAgBC,MACxFsO,EAAkBoS,GAAqBxZ,EAAMsH,SAAS,IACvDmS,EAAiBzZ,EAAMoK,OAAO,IAEpCsP,IAAmB,KAClBH,EAAgB1gB,EAAgBC,GAAS,GACvC,CAACA,IAEJ,MAAM6gB,EAAkB3Z,EAAMyH,aAC5BjE,cACA,MAAMoW,WAAEA,GAAepW,EAAEoE,OACnBrO,EAAgD,QAA3CsV,EAAA+K,EAAWC,aAAa,yBAAmB,IAAAhL,OAAA,EAAAA,EAAAzQ,MAElD7E,IACHkgB,EAAepP,mBAA+D,QAArDyP,EAAAR,EAAale,EAAoBke,EAAc/f,WAAM,IAAAugB,OAAA,EAAAA,EAAAvgB,yBAAI2c,aAAc,GAEhGsD,EAAkBC,EAAepP,SACjC,GAEF,CAACiP,IAGIS,EAAkB/Z,EAAMyH,aAC5BjE,UACA,MAAMoW,WAAEA,GAAepW,EAAEoE,OACnBrO,EAAgD,QAA3CsV,EAAA+K,EAAWC,aAAa,yBAAmB,IAAAhL,OAAA,EAAAA,EAAAzQ,MAEtD,GAAI7E,GAAMkgB,EAAepP,SAAW9Q,IAAOkgB,EAAepP,QAAS,CAClE,MAAM2P,EAAmB5e,EAAoBke,EAAcG,EAAepP,SACpE4P,EAAiB7e,EAAoBke,EAAc/f,GACnD2gB,EAAgB,IAAIZ,GAE1BY,EAAcF,GAAoBV,EAAaW,GAC/CC,EAAcD,GAAkBX,EAAaU,GAE7CT,EAAgBW,GAEhBlH,EAAoBkH,EACpB,IAEF,CAAClH,EAAqBsG,IAGjBa,EAAiBna,EAAMyH,aAAajE,IACzCA,EAAE4W,gBAAgB,GAChB,IAEGC,EAAkBra,EAAMyH,aAAajE,IAC1CA,EAAE4W,gBAAgB,GAChB,IAEGE,EAAgBta,EAAMyH,aAAajE,IACxCA,EAAE4W,iBAEFX,EAAepP,QAAU,GAEzBmP,EAAkB,GAAG,GACnB,IAEGe,E1CwCS,SAAiBC,GAAoC,GACpE,OAAOA,EAAe3iB,EAAU6S,IAAM7S,EAAU8S,IACjD,C0C1C8B8P,CAAiBpJ,GACxCqJ,EAAoB1a,EAAMsC,SAC/B,IAAMgX,EAAale,EAAoBke,EAAclI,aAAkB,EAAlBA,EAAoB8E,cAAgB,CAAA,GACzF,CAAC9E,EAAoBkI,IAGtB,MAAO,CACNA,eACAlS,mBACAuS,kBACAI,kBACAI,iBACAE,kBACAC,gBACAC,uBACAG,oBAEF,CCmaA,IAAAC,GAAe3a,EAAMC,MAnerB,SAAsBR,GACrB,MAAMiF,KACLA,EAAO+L,GAAa/L,KAAI5L,QACxBA,EAAU2X,GAAa3X,QAAO0S,MAC9BA,EAAQiF,GAAajF,MAAK4C,QAC1BA,EAAUqC,GAAarC,QAAO7V,SAC9BA,EAAWkY,GAAalY,SAAQ4O,QAChCA,EAAUsJ,GAAatJ,QAAOV,iBAC9BA,EAAmBgK,GAAahK,iBAAgBO,eAChDA,EAAiByJ,GAAazJ,eAAcd,MAC5CA,EAAQuK,GAAavK,MAAKe,eAC1BA,EAAiBwJ,GAAaxJ,eAAc5D,qBAC5CA,EAAuBoN,GAAapN,qBAAoB6D,wBACxDA,EAA0BuJ,GAAavJ,wBAAuBwJ,0BAC9DA,EAA4BD,GAAaC,0BAAyB7G,0BAClEA,EAA4B4G,GAAa5G,0BAAyB8G,sBAClEA,EAAwBF,GAAaE,sBAAqBrN,sBAC1DA,EAAwBmN,GAAanN,sBAAqBH,wBAC1DA,EAA0BsN,GAAatN,wBAAuBC,6BAC9DA,EAA+BqN,GAAarN,6BAA4B2D,mBACxEA,EAAqB0J,GAAa1J,mBAAkBgM,qBACpDA,EAAuBtC,GAAasC,qBAAoBhP,eACxDA,EAAiB0M,GAAa1M,eAAc+O,oBAC5CA,EAAsBrC,GAAaqC,oBAAmBD,aACtDA,EAAepC,GAAaoC,aAAYhW,iBACxCA,EAAmB4T,GAAa5T,iBAAgBmV,wBAChDA,EAA0BvB,GAAauB,wBAAuBG,oBAC9DA,EAAsB1B,GAAa0B,oBAAmBF,sBACtDA,EAAwBxB,GAAawB,sBAAqBC,2BAC1DA,EAA6BzB,GAAayB,2BAA0BE,kBACpEA,EAAoB3B,GAAa2B,kBAAiBC,6BAClDA,EAA+B5B,GAAa4B,6BAA4BI,uBACxEA,EAAyBhC,GAAagC,uBAAsBD,wBAC5DA,EAA0B/B,GAAa+B,wBAAuBE,mBAC9DA,EAAqBjC,GAAaiC,mBAAkBC,uBACpDA,EAAyBlC,GAAakC,uBAAsBL,oBAC5DA,EAAsB7B,GAAa6B,oBAAmBC,2BACtDA,EAA6B9B,GAAa8B,2BAA0BjB,WACpEA,EAAab,GAAaa,WAAUP,gBACpCA,EAAkBN,GAAaM,gBAAeC,kBAC9CA,EAAoBP,GAAaO,kBAAiBE,iBAClDA,EAAmBT,GAAaS,iBAAgBK,gBAChDA,GAAkBd,GAAac,gBAAejU,SAC9CA,GAAWmT,GAAanT,SAAQkU,YAChCA,GAAcf,GAAae,YAAWC,SACtCA,GAAWhB,GAAagB,SAAQK,YAChCA,GAAcrB,GAAaqB,YAAWC,wBACtCA,GAA0BtB,GAAasB,wBAAuBpI,WAC9DA,GAAa8G,GAAa9G,WAAUgF,UACpCA,GAAY8B,GAAa9B,UAAS+C,eAClCA,GAAiBjB,GAAaiB,eAAcE,cAC5CA,GAAgBnB,GAAamB,cAAaC,mBAC1CA,GAAqBpB,GAAaoB,mBAAkBV,cACpDA,GAAgBV,GAAaU,cAAa9D,eAC1CA,GAAiBoD,GAAapD,eAAcC,eAC5CA,GAAiBmD,GAAanD,eAAcC,iBAC5CA,GAAmBkD,GAAalD,iBAAgBpH,eAChDA,GAAiBsK,GAAatK,eAAcQ,aAC5CA,GAAe8J,GAAa9J,aAAYC,mBACxCA,GAAqB6J,GAAa7J,mBAAkBC,gBACpDA,GAAkB4J,GAAa5J,gBAAeC,gBAC9CA,GAAkB2J,GAAa3J,gBAAe2C,SAC9CA,GAAWgH,GAAahH,SAAQK,OAChCA,GAAS2G,GAAa3G,OAAMzQ,aAC5BA,GAAeoX,GAAapX,aAAYqQ,WACxCA,GAAa+G,GAAa/G,WAAUtD,wBACpCA,GAA0BqK,GAAarK,wBAAuBC,6BAC9DA,GAA+BoK,GAAapK,6BAA4BwK,sBACxEA,GAAwBJ,GAAaI,sBAAqBvK,2BAC1DA,GAA6BmK,GAAanK,2BAA0BC,mBACpEA,GAAqBkK,GAAalK,mBAAkBC,yBACpDA,GAA2BiK,GAAajK,yBAAwBsK,sBAChEA,GAAwBL,GAAaK,sBAAqBpK,mCAC1DA,GAAqC+J,GAAa/J,mCAAkC0K,mBACpFA,GAAqBX,GAAaW,mBAAkBC,eACpDA,GAAiBZ,GAAaY,eAAcT,kBAC5CA,GAAoBH,GAAaG,kBAAiBxW,qBAClDA,GAAuBqW,GAAarW,qBAAoBmD,MACxDA,GAAQkT,GAAalT,MAAKqV,aAC1BA,GAAenC,GAAamC,aAAYnI,UACxCA,GAAYgG,GAAahG,UAASuI,oBAClCA,GAAsBvC,GAAauC,oBAAmBnS,UACtDA,GAAS+Z,UACTA,IACGnb,GAEE6Z,aACLA,GAAYlS,iBACZA,GAAgBuS,gBAChBA,GAAeI,gBACfA,GAAeI,eACfA,GAAcE,gBACdA,GAAeC,cACfA,GAAaC,qBACbA,GAAoBG,kBACpBA,IACGrB,GAAWvgB,EAASka,GAAqB5B,GAAoBC,MAGhE3X,YACCA,GAAWiD,YACXA,GAAW1B,aACXA,GAAYe,YACZA,GAAWG,cACXA,GAAaM,eACbA,GAAcD,cACdA,GAAab,2BACbA,IAEDkf,IACG7a,EAAM8a,WAAoDtf,EAAc,CAC3EQ,aAAa,EACbG,cAAe,EACflB,aAAc,GACdwB,eAAgBie,GAChB/e,4BAA4B,EAC5Ba,cAAe+d,GACf5d,YAAasV,EACbvY,YAAa0Y,EACb7V,kBAAkB,EAClB8Q,eAAgBoD,GAAapD,kBAGxBzD,sBAAEA,IAAwB,EAAK7M,4BAAEA,IAA8B,GAAUiV,EACzElW,MAAqBe,IAAqBE,KAA+B6M,IACzEmR,GAAoBpR,KAAeoH,GAAmBrM,EAAKlK,OAAS,EACpEwgB,GAAa1I,GAAuB0B,GAEpCiH,GAAejb,EAAMsC,SAAQ,IC0DR,EAC3BsQ,EAA4B,CAAE,EAC9BsI,EAAY,UACZC,EAAkB,aAElB,MAAMC,EAAYlC,GAAcgC,GAAaA,EAAYC,EAEzD,OAAOE,GAhOqD,CAC5D7d,MAAO,CACN3C,MAAO,CACNygB,OAH2B/d,EAgOF2b,GAAckC,IA7N1B3C,KAAKC,QAClB6C,gBAAiBhe,EAAMqb,WAAWC,UAGpCpJ,aAAc,CACb5U,MAAO,CACN2gB,QAAS,UAGXnM,kBAAmB,CAClBxU,MAAO,CAAE,GAEVoT,OAAQ,CACPpT,MAAO,CACNiH,SAAU,OACVwZ,MAAO/d,EAAMkb,KAAKC,QAClB6C,gBAAiBhe,EAAMqb,WAAWC,QAClC4C,UAAW,OACXC,YAAa,OACbC,aAAc,QAGhBhN,UAAW,CACV9T,MAAO,CACN0gB,gBAAiBhe,EAAMqb,WAAWC,QAClC4C,UAAW,SAGb7d,KAAM,CACL/C,MAAO,CACNygB,MAAO/d,EAAMkb,KAAKC,QAClB5W,SAAU,OACVmP,WAAY,MAGdnT,QAAS,CACRjD,MAAO,CACN0gB,gBAAiBhe,EAAMqb,WAAWC,QAClC4C,UAAW,OACXG,kBAAmB,MACnBC,kBAAmBte,EAAMwb,QAAQF,QACjCiD,kBAAmB,SAEpB9d,WAAY,CACXyd,UAAW,SAGbrS,UAAW,CACVvO,MAAO,CACN6gB,YAAa,OACbC,aAAc,QAEf9b,cAAe,CACdkC,OAAQ,SAGV8K,YAAa,CACZhS,MAAO,CACN0gB,gBAAiBhe,EAAMub,QAAQF,WAC/B9W,SAAU,OACVmP,WAAY,IACZqK,MAAO/d,EAAMub,QAAQL,KACrBiD,YAAa,OACbC,aAAc,MACdI,UAAW,2BACXC,mBAAoB,QACpBC,yBAA0B,6BAC1BC,WAAY,aAEb/O,YAAa,CACZ4O,UAAW,yBAGbnc,MAAO,CACN/E,MAAO,CACN6gB,YAAa,OACbC,aAAc,OACdQ,UAAW,cAEZtc,cAAe,CAAE,GAElBhE,KAAM,CACLhB,MAAO,CACNiH,SAAU,OACVmP,WAAY,IACZqK,MAAO/d,EAAMkb,KAAKC,QAClB6C,gBAAiBhe,EAAMqb,WAAWC,QAClC4C,UAAW,OACX,uBAAwB,CACvBK,kBAAmB,QACnBF,kBAAmB,MACnBC,kBAAmBte,EAAMwb,QAAQF,UAGnC7a,WAAY,CACXyd,UAAW,QAEZ5V,uBAAwB,CAEvB,mBAAoB,CACnByV,MAAO/d,EAAM2F,SAASuV,KACtB8C,gBAAiBhe,EAAM2F,SAAS2V,QAChCgD,kBAAmBte,EAAMqb,WAAWC,UAGtCvT,sBAAuB,CACtBgW,MAAO/d,EAAMkJ,iBAAiBgS,KAC9B8C,gBAAiBhe,EAAMkJ,iBAAiBoS,QACxCmD,mBAAoB,QACpBI,mBAAoB,mBACpBP,kBAAmBte,EAAMqb,WAAWC,QACpCwD,aAAc,QACdC,aAAc,MACdC,aAAchf,EAAMqb,WAAWC,SAEhCnT,aAAc,CACb4V,MAAO/d,EAAM4J,QAAQsR,KACrB8C,gBAAiBhe,EAAM4J,QAAQ0R,UAGjCtU,YAAa,CACZ1J,MAAO,CACNygB,MAAO/d,EAAMkb,KAAKC,QAClB6C,gBAAiBhe,EAAMqb,WAAWC,UAGpCzU,aAAc,CACbvJ,MAAO,CACN2hB,KAAM,aAGR5Y,eAAgB,CACf/I,MAAO,CACNygB,MAAO/d,EAAMkB,OAAOoa,QACpB3I,KAAM3S,EAAMkB,OAAOoa,QACnB0C,gBAAiB,cACjBkB,aAAc,MACdC,WAAY,QACZ3M,OAAQ,OACRlR,MAAO,OACP,kBAAmB,CAClBkD,OAAQ,WAET,aAAc,CACbuZ,MAAO/d,EAAMkB,OAAOnB,UAErB,yBAA0B,CACzByE,OAAQ,UACRwZ,gBAAiBhe,EAAMkB,OAAOwa,OAE/B,UAAW,CACV0D,QAAS,OACTpB,gBAAiBhe,EAAMkB,OAAOua,OAE/B4D,IAAK,CACJC,OAAQ,UAIXlT,WAAY,CACX9O,MAAO,CACNygB,MAAO/d,EAAMkb,KAAKE,UAClB7W,SAAU,OACV2Z,UAAW,OACXF,gBAAiBhe,EAAMqb,WAAWC,QAClCiE,eAAgB,QAChBC,eAAgB,MAChBC,eAAgBzf,EAAMwb,QAAQF,SAE/BnF,iBAAkB,CACjB+I,aAAc,MACd1M,OAAQ,OACRlR,MAAO,OACPmD,QAAS,MACT6a,OAAQ,KACR9a,OAAQ,UACR2a,WAAY,OACZpB,MAAO/d,EAAMkB,OAAOoa,QACpB3I,KAAM3S,EAAMkB,OAAOoa,QACnB0C,gBAAiB,cACjB,aAAc,CACbxZ,OAAQ,QACRuZ,MAAO/d,EAAMkB,OAAOnB,SACpB4S,KAAM3S,EAAMkB,OAAOnB,UAEpB,yBAA0B,CACzBie,gBAAiBhe,EAAMkB,OAAOwa,OAE/B,UAAW,CACV0D,QAAS,OACTpB,gBAAiBhe,EAAMkB,OAAOua,SAIjCpJ,OAAQ,CACP/U,MAAO,CACN2gB,QAAS,OACTyB,WAAY,SACZC,eAAgB,SAChB5B,MAAO/d,EAAMkb,KAAKC,QAClB6C,gBAAiBhe,EAAMqb,WAAWC,UAGpCtJ,SAAU,CACT1U,MAAO,CACN2gB,QAAS,OACTyB,WAAY,SACZC,eAAgB,SAChB5B,MAAO/d,EAAMkb,KAAKC,QAClB6C,gBAAiBhe,EAAMqb,WAAWC,WAYkBjG,GAhO1B,IAACrV,CAgOsC,EDjE1B4f,CAAavK,GAAcrV,KAAQ,CAACqV,GAAcrV,KACrF6f,GAAepd,EAAMsC,SAAQ,IAAYpJ,OAAAC,OAAA,GAAe,SAAdsR,IAAwB,CAAEkC,IAAKlC,MAAiB,CAACA,KAE3F4S,GAAard,EAAMsC,SAAQ,KAEhC,GAAIoH,GACH,OAAOhF,EAGR,IAAIjI,cAAc,EAAdA,GAAgBpD,eAAuD,mBAAhCoD,GAAepD,aAA6B,CACtF,MAAMikB,EAAS7gB,GAAepD,aACxBkkB,EAAqB/gB,KAAkB3E,EAAU6S,IAAM4S,EAAS,CAAC1kB,EAAM2C,KAAyB,EAAhB+hB,EAAO1kB,EAAG2C,GAEhG,MAAO,IAAImJ,GAAM8Y,KAAKD,EACtB,CAED,O3CpKI,SACL1hB,EACAoF,EACAwJ,EACA6S,GAEA,OAAKrc,EAIDqc,GAA4B,mBAAXA,EAEbA,EAAOzhB,EAAKxD,MAAM,GAAI4I,EAAUwJ,GAGjC5O,EAAKxD,MAAM,GAAGmlB,MAAK,CAAC5kB,EAAM2C,KAChC,MAAMkiB,EAASxc,EAASrI,GAClB8kB,EAASzc,EAAS1F,GAExB,GAAkB,QAAdkP,EAAqB,CACxB,GAAIgT,EAASC,EACZ,OAAQ,EAGT,GAAID,EAASC,EACZ,OAAO,CAER,CAED,GAAkB,SAAdjT,EAAsB,CACzB,GAAIgT,EAASC,EACZ,OAAQ,EAGT,GAAID,EAASC,EACZ,OAAO,CAER,CAED,OAAO,CAAC,IAhCD7hB,CAkCT,C2C2HS2hB,CAAK9Y,EAAMjI,cAAc,EAAdA,GAAgBwE,SAAUzE,GAAenD,GAAa,GACtE,CAACqQ,GAAYjN,GAAgBD,GAAekI,EAAMrL,KAE/CskB,GAAY3d,EAAMsC,SAAQ,KAC/B,GAAIqH,KAAe9M,EAAkB,CAEpC,MAAM+X,EAAYjY,GAAcjD,GAC1Bmb,EAAaD,EAAYlb,GAE/B,OAAO2jB,GAAWhlB,MAAMwc,EAAYD,EACpC,CAED,OAAOyI,EAAU,GACf,CAAC1gB,GAAagN,GAAY9M,EAAkBnD,GAAa2jB,KAEtDO,GAAa5d,EAAMyH,aAAa/L,IACrCmf,GAASnf,EAAO,GACd,IAEGmiB,GAAsB7d,EAAMyH,aAAa/L,IAC9Cmf,GAASnf,EAAO,GACd,IAEGoiB,GAAoB9d,EAAMyH,aAAa/L,IAC5Cmf,GAASnf,EAAO,GACd,IAEGqiB,GAAmB/d,EAAMyH,aAC9B,CAACtN,EAAQqJ,IAA6CmD,GAAaxM,EAAKqJ,IACxE,CAACmD,KAGIqX,GAAyBhe,EAAMyH,aACpC,CAACtN,EAAQqJ,IAA6CoD,GAAmBzM,EAAKqJ,IAC9E,CAACoD,KAGImB,GAAsB/H,EAAMyH,aACjC,CAACtN,EAAQqJ,IAA6CqD,GAAgB1M,EAAKqJ,IAC3E,CAACqD,KAGImB,GAAsBhI,EAAMyH,aACjC,CAACtN,EAAQqJ,IAA6CsD,GAAgB3M,EAAKqJ,IAC3E,CAACsD,KAGImX,GAAmBje,EAAMyH,aAC7B7K,GACAie,GAAS,CACRjf,KAAM,cACNgB,OACAC,mBACAC,YAAa+M,EACb9M,kCAEF,CAACF,EAAkBE,GAA6B8M,IAG3CqU,GAA0Ble,EAAMyH,aACpC0W,IACA,MACMC,EAAc5kB,EADH2Y,GAAuBwL,GAAUnjB,OACH2jB,GACzCE,EAAmBxkB,EAAgB8C,GAAayhB,GAIjDvhB,GACJohB,GAAiBI,GAGlBxD,GAAS,CAAEjf,KAAM,uBAAwBgB,KAAMyhB,EAAkB3kB,YAAaykB,GAAiB,GAEhG,CAACxhB,GAAashB,GAAkBphB,EAAkBsV,EAAqBwL,GAAUnjB,SAgClF,GAAImP,KAAe9M,GAAoBwgB,GAAW7iB,OAAS,GAA0B,IAArBmjB,GAAUnjB,OAAc,CACvF,MAAM4jB,EAAc5kB,EAAiB6jB,GAAW7iB,OAAQd,IAClD2kB,EAAmBxkB,EAAgB8C,GAAayhB,GAEtDH,GAAiBI,EACjB,CAED3E,IAAmB,KAClB3G,EAAqB,CAAE/W,eAAaG,iBAAelB,aAAcA,GAAa5C,MAAM,IAAK,GAEvF,CAACsD,KAEJ+d,IAAmB,KAClB5P,GAAOrN,GAAgBD,GAAe6gB,GAAWhlB,MAAM,GAAG,GAExD,CAACoE,GAAgBD,KAEpBkd,IAAmB,KAClB7G,EAAalW,GAAawV,GAAuBkL,GAAW7iB,OAAO,GACjE,CAACmC,KAEJ+c,IAAmB,KAClB5G,EAAoBpZ,GAAaiD,GAAY,GAC3C,CAACjD,KAEJggB,IAAmB,KAClBuE,GAAiBhM,EAAsB,GACrC,CAACA,EAAuBC,IAE3BwH,IAAmB,KAClB,GAAI/P,IAAc9M,GAAoBsV,EAAsB,EAAG,CAC9D,MAAMiM,EAAc5kB,EAAiB2Y,EAAqBzY,IACpD2kB,EAAmBxkB,EAAgB8C,GAAayhB,GAElDzhB,KAAgB0hB,GACnBJ,GAAiBI,EAElB,IACC,CAAClM,IAEJnS,EAAMuH,WAAU,KACfsT,GAAS,CAAEjf,KAAM,sBAAuBW,iBAAkBqU,IAAoB,GAC5E,CAACvN,EAAsBuN,KAE1B5Q,EAAMuH,WAAU,KACf,IAAKoJ,EACJ,OAGD,MAAM2N,EAAkBjB,GAAWnhB,QAAO/B,GAAOwW,EAAsBxW,KAEjE+I,EAAWG,EAAuBib,EAAgBjmB,MAAM,EAAG,GAAKimB,EAEtEzD,GAAS,CACRjf,KAAM,uBACNrD,WACA0C,aAAciI,EACd5G,UAAW+gB,GAAW7iB,OACtBsB,oBACC,GAIA,CAAC4I,EAAMiM,IAEV,MAAM4N,GAAc1U,EAA4B8T,GAAYN,GACtDmB,GAAgBzhB,IAA+BsG,GAAwBqN,EAE7E,OACC1Q,EAACW,cAAA8d,EAAaA,cAAC,CAAAlhB,MAAO0d,KArFlBxJ,OAIAjG,KAIA4C,IA+EFpO,EAACW,cAAAwN,IACA3C,MAAOA,EACP4C,QAASA,EACTC,UAAW8C,GACXhV,cAAeA,GACfsO,UAAWA,GACX6C,eAAgBA,GAChBC,iBAAkBA,GAClBF,eAAgBA,KAIjBsB,IACA3O,EAACW,cAAAiO,IAAUH,MAAOiD,GAAgB5C,YAAa8C,IAC7CC,IAIH7R,EAAAW,cAACuO,GAAiBhW,OAAAC,OAAA,CAAAgW,YACJmC,EAAU3T,aACTmU,GAAW1C,yBACC2C,GAC1BlR,UAAWA,IACPuc,IAEJpd,EAAAW,cAAC6O,GAAO,KACNuB,IAAoBG,GAAoBlR,gBAACsP,GAAe,KAAE0B,GAE3DhR,EAACW,cAAA+d,iBAAMphB,SAAUA,GAAUuD,UAAU,YAAYD,KAAK,SAAaga,IAAa,CAAE,aAAcA,MA/H/FpJ,OAIAN,GAIGmM,GAAW7iB,OAAS,IAAMuW,IAyH5B/Q,EAAAW,cAACjD,EAAK,CAAAmD,UAAU,gBAAgBD,KAAK,wBAAyBkR,IAC7D9R,EAACW,cAAA9C,EAAQ,CAAAgD,UAAU,mBAAmBD,KAAK,MAAK7C,OAASmI,GACvDe,IACCuX,GACAxe,EAAAW,cAACtC,EAAQ,CAACxD,MAAO,CAAE2hB,KAAM,cAEzBxc,EAAAW,cAAC+K,GAAc,CACd1P,YAAaA,GACbf,aAAcA,GACdkI,wBAAyBA,EACzBC,6BAA8BA,EAC9BE,sBAAuBA,EACvBsI,QAAS2S,GACThmB,SAAUA,EACVuD,gBAAiBA,GACjB+P,gBAAiBgS,MAGnB1X,KAAmBG,IAA8BtG,EAAAW,cAAC+O,GAAiB,MACnE4J,GAAavgB,KAAIC,GACjBgH,EAACW,cAAA6I,IACAxR,IAAKgB,EAAOO,GACZP,OAAQA,EACRyD,eAAgBA,GAChBa,SAAUyT,GAAyC,IAAtBsM,GAAW7iB,OACxCmP,WAAYA,GACZ9M,iBAAkBA,EAClB+M,sBAAuBA,GACvBC,0BAA2BA,EAC3BrN,cAAeA,GACfiN,SAAUA,GACVC,WAAYA,GACZI,OAAQ8T,GACRvd,YAAasZ,GACbrZ,WAAY6Z,GACZ5Z,UAAW+Z,GACX9Z,YAAauZ,GACbtZ,YAAa4Z,GACbjT,iBAAkBA,UAOrBiW,GAAW7iB,SAAWuW,GAAmB/Q,EAACW,cAAAge,GAAQ,KAAApN,IAEnDR,GAAmBG,GAAoBlR,gBAACsP,GAAe,KAAE0B,IAExDD,GAAmBsM,GAAW7iB,OAAS,GACxCwF,EAAAW,cAACsO,GAAI,CAACpO,UAAU,gBAAgBD,KAAK,YACnC+c,GAAU5kB,KAAI,CAACoB,EAAKykB,KACpB,MAAM5mB,EAAMF,EAAKqC,EAAiB5B,GAC5BgB,E3C/aC,SAAQslB,EAAqC,IAC5D,MAAqB,iBAAVA,KAIHA,GAA0B,IAAjBA,EAAMrkB,OACxB,C2CyaoBskB,CAAQ9mB,GAAO4mB,EAAI5mB,EACxBkL,EAAWlI,EAAcb,EAAKc,GAAc1C,GAC5CwmB,KAAsB5Y,IAAkB2K,IAAyBA,GAAsB3W,IACvF6kB,KAAsB7Y,IAAkB0K,IAAyBA,GAAsB1W,IAE7F,OACC6F,EAACW,cAAAoF,GACAxM,GAAIA,EACJvB,IAAKuB,EACLhB,SAAUA,EACG,cAAAgB,EACbT,QAASwgB,GACTnf,IAAKA,EACLV,SAAU4jB,GAAW7iB,OACrB0F,SAAU0e,EACV3X,eAAgBA,EAChBd,eAAgBA,GAChBpC,eAAgBA,EAChB0C,iBAAkBA,EAClBO,eAAgBA,EAChBd,MAAOA,EACPK,mBAAoBA,GACpBC,yBAA0BA,GAC1BJ,wBAAyBA,GACzBC,6BAA8BA,GAC9BC,2BAA4BA,GAC5BL,wBAAyB+Y,EACzBhZ,gBAAiB+Y,EACjBrY,mCAAoCA,GACpCtM,qBAAsBA,GACtB8I,SAAUA,EACVgE,wBAAyBA,EACzB/D,wBAAyBA,EACzBC,6BAA8BA,EAC9BE,sBAAuBA,EACvBD,qBAAsBA,EACtB8D,QAASA,EACTJ,mBAAoBA,EACpBJ,aAAcoX,GACdnX,mBAAoBoX,GACpBnX,gBAAiBkB,GACjBjB,gBAAiBkB,GACjBzE,cAAeua,GACf1W,iBAAkBA,GAClB/G,YAAasZ,GACbrZ,WAAY6Z,GACZ5Z,UAAW+Z,GACX9Z,YAAauZ,GACbtZ,YAAa4Z,IAEb,QAQPU,IACA/a,EAAAW,cAAA,MAAA,KACCX,EAACW,cAAAqa,GACA,CAAAnI,aAAcoL,GACdnL,oBAAqBoL,GACrBzkB,SAAU0Y,GAAuBkL,GAAW7iB,OAC5CmC,YAAaA,GACbjD,YAAaA,GACb+Q,UAAWA,GACX4H,6BAA8BA,EAC9BI,uBAAwBA,EACxBD,wBAAyBA,EACzBE,mBAAoBA,EACpBC,uBAAwBA,EACxBJ,2BAA4BA,KAMlC,gDFjbM,SAAyBjR,EAAO,UAAW2d,EAAiB9D,EAAkB,WAQnF,OAPKjC,GAAc5X,KAClB4X,GAAc5X,GAAQ+Z,GAAMnC,GAAciC,GAAU8D,GAAe,CAAA,IAIpE/F,GAAc5X,GAAQ+Z,GAAMnC,GAAc5X,GAAO2d,GAAe,CAAA,GAEzD/F,GAAc5X,EACtB"}