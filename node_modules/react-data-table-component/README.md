[![Netlify Status](https://api.netlify.com/api/v1/badges/26e0d16d-a986-46b1-9097-1a76c10d7cad/deploy-status)](https://app.netlify.com/sites/react-data-table-component/deploys) [![npm version](https://badge.fury.io/js/react-data-table-component.svg)](https://badge.fury.io/js/react-data-table-component) [![codecov](https://codecov.io/gh/jbetancur/react-data-table-component/branch/master/graph/badge.svg)](https://codecov.io/gh/jbetancur/react-data-table-component) [![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)

# React Data Table Component

[![GitHub release](https://img.shields.io/github/release/jbetancur/react-data-table-component.svg)](https://GitHub.com/jbetancur/react-data-table-component/releases/)

Creating yet another React table library came out of necessity while developing a web application for a growing startup. I discovered that while there are some great table libraries out there, some required heavy customization, were missing out of the box features such as built in sorting and pagination, or required understanding the atomic structure of html tables.

If you want to achieve balance with the force and want a simple but flexible table library give React Data Table Component a chance. If you require an Excel clone, then this is not the React table library you are looking for 👋

# Key Features

- Declarative configuration
- Built-in and configurable:
  - Sorting
  - Selectable Rows
  - Expandable Rows
  - Pagination
- Themeable/Customizable
- Accessibility
- Responsive (via x-scroll/flex)

# Documentation Website

[![Netlify Status](https://api.netlify.com/api/v1/badges/26e0d16d-a986-46b1-9097-1a76c10d7cad/deploy-status)](https://app.netlify.com/sites/react-data-table-component/deploys)

The documentation contains information about installation, usage and contributions.

https://react-data-table-component.netlify.app

# Supporting React Data Table Component

If you would like to support the project financially, visit
[our campaign on OpenCollective](https://opencollective.com/react-data-table-component). Your contributions help accelerate the development of React Data Table Component!

<a href="https://opencollective.com/react-data-table-component" target="_blank">
	<img src="https://opencollective.com/react-data-table-component/contribute/<EMAIL>?color=blue" width="250px" />
</a>

# Contributors

<a href="https://github.com/jbetancur/react-data-table-component/graphs/contributors">
	<img src="https://opencollective.com/react-data-table-component/contributors.svg?width=890" />
</a>
