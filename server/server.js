import express from  'express'
import cors from  'cors'
import dotenv from 'dotenv';
import employeeRoutes from './routes/employees.js';
import authMiddleware from './middleware/auth.js';


const app = express()
app.use(cors())
app.use(express.json())

app.use('/api/employees', authMiddleware, employeeRoutes);

app.listen(process.env.PORT, ()=>{
    console.log(`Server is Running on  port ${process.env.PORT}`)
})
